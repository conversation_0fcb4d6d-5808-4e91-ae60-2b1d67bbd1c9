using UnityEngine;
using UnityEditor;
using AssetPipeline.Core;
using AssetPipeline.UI.Components;
using System.Collections.Generic;
using System.Linq;
using AssetPipeline.Processors;

namespace AssetPipeline.UI.Windows
{
    public class AssetProfileEditorWindow : EditorWindow
    {
        private AssetProfile targetProfile;
        private Vector2 scrollPosition;
        private bool showBasicConfig = true;
        private bool showFiltersConfig = true;
        private bool showTestConfig = true;
        
        private string testAssetPath = "";

        public static void ShowWindow(AssetProfile profile)
        {
            var window = GetWindow<AssetProfileEditorWindow>("AssetProfile 编辑器");
            window.targetProfile = profile;
            window.minSize = new Vector2(650, 600);
            window.Show();
        }

        void OnGUI()
        {
            if (targetProfile == null)
            {
                EditorGUILayout.HelpBox("没有选中的AssetProfile", MessageType.Warning);
                if (GUILayout.Button("关闭"))
                {
                    Close();
                }
                return;
            }

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            DrawHeader();
            EditorGUILayout.Space();
            
            DrawBasicConfig();
            EditorGUILayout.Space();
            
            DrawFiltersConfig();
            EditorGUILayout.Space();
            
            DrawTestSection();

            EditorGUILayout.EndScrollView();
        }

        void DrawHeader()
        {
            UIHelper.DrawHeader($"编辑Profile: {targetProfile.name}", "📋");
        }

        void DrawBasicConfig()
        {
            showBasicConfig = UIHelper.DrawFoldout(ref showBasicConfig, "基础配置", "⚙️", true);
            
            if (showBasicConfig)
            {
                UIHelper.DrawBox(() =>
                {
                    targetProfile.DisplayName = EditorGUILayout.TextField("显示名称", targetProfile.DisplayName);
                    targetProfile.Description = EditorGUILayout.TextField("描述", targetProfile.Description);
                    
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField("启用状态", GUILayout.Width(60));
                    targetProfile.Enabled = EditorGUILayout.Toggle(targetProfile.Enabled);
                    EditorGUILayout.EndHorizontal();
                    
                    targetProfile.Priority = EditorGUILayout.IntField("优先级", targetProfile.Priority);
                    targetProfile.PathPrefix = EditorGUILayout.TextField("路径前缀", targetProfile.PathPrefix);
                    
                    if (GUILayout.Button("选择文件夹设置路径前缀"))
                    {
                        var path = EditorUtility.OpenFolderPanel("选择路径前缀", "Assets", "");
                        if (!string.IsNullOrEmpty(path) && path.StartsWith(Application.dataPath))
                        {
                            targetProfile.PathPrefix = "Assets" + path.Substring(Application.dataPath.Length) + "/";
                            EditorUtility.SetDirty(targetProfile);
                        }
                    }
                    
                    UIHelper.DrawSeparator();
                    
                    UIHelper.DrawKeyValue("GUID", targetProfile.GUID, 60);
                });
            }
        }

        void DrawFiltersConfig()
        {
            showFiltersConfig = UIHelper.DrawFoldout(ref showFiltersConfig, "筛选器配置", "🔍", true);
            
            if (showFiltersConfig)
            {
                UIHelper.DrawBox(() =>
                {
                    EditorGUILayout.LabelField($"已配置筛选器: {targetProfile.Filters.Count}", EditorStyles.boldLabel);
                    
                    var filtersByLayer = targetProfile.Filters
                        .Where(f => f != null)
                        .GroupBy(f => f.Layer)
                        .OrderBy(g => g.Key);
                    
                    foreach (var layerGroup in filtersByLayer)
                    {
                        EditorGUILayout.LabelField($"层级 {layerGroup.Key}:", EditorStyles.boldLabel);
                        EditorGUI.indentLevel++;
                        
                        var sortedFilters = layerGroup.OrderByDescending(f => f.Priority);
                        int index = 0;
                        
                        foreach (var filter in sortedFilters)
                        {
                            DrawFilterItem(filter, index++);
                        }
                        
                        EditorGUI.indentLevel--;
                        EditorGUILayout.Space();
                    }
                    
                    EditorGUILayout.BeginHorizontal();
                    if (GUILayout.Button("创建新筛选器"))
                    {
                        CreateNewFilter();
                    }
                    
                    if (GUILayout.Button("从现有筛选器添加"))
                    {
                        ShowAddExistingFilterMenu();
                    }
                    EditorGUILayout.EndHorizontal();
                });
            }
        }

        void DrawFilterItem(AssetFilter filter, int index)
        {
            EditorGUILayout.BeginHorizontal(EditorStyles.helpBox);
            
            EditorGUILayout.BeginVertical();
            EditorGUILayout.LabelField($"{index + 1}. {filter.DisplayName}", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"层级: {filter.Layer}, 优先级: {filter.Priority}, 启用: {(filter.Enabled ? "是" : "否")}", EditorStyles.miniLabel);
            EditorGUILayout.LabelField($"类型: {filter.AssetTypes}", EditorStyles.miniLabel);
            
            if (!string.IsNullOrEmpty(filter.FilePattern?.Pattern))
            {
                EditorGUILayout.LabelField($"文件匹配: {filter.FilePattern.GetDescription()}", EditorStyles.miniLabel);
            }
            
            if (filter.SpecificPaths?.Count > 0)
            {
                EditorGUILayout.LabelField($"特定路径: {filter.SpecificPaths.Count} 个", EditorStyles.miniLabel);
            }
            
            EditorGUILayout.LabelField($"处理器: {filter.Processors.Count} 个", EditorStyles.miniLabel);
            EditorGUILayout.EndVertical();
            
            EditorGUILayout.BeginVertical(GUILayout.Width(120));
            if (GUILayout.Button("编辑"))
            {
                AssetFilterEditorWindow.ShowWindow(filter);
            }
            
            if (GUILayout.Button("复制"))
            {
                var clone = filter.Clone();
                var filterDir = "Assets/Editor/AssetPipeline/Data/Filters";
                if (!System.IO.Directory.Exists(filterDir))
                {
                    System.IO.Directory.CreateDirectory(filterDir);
                }
                
                var filterPath = AssetDatabase.GenerateUniqueAssetPath($"{filterDir}/{clone.DisplayName}.asset");
                AssetDatabase.CreateAsset(clone, filterPath);
                
                targetProfile.Filters.Add(clone);
                AssetDatabase.SaveAssets();
                EditorUtility.SetDirty(targetProfile);
            }
            
            if (GUILayout.Button("移除"))
            {
                if (EditorUtility.DisplayDialog("确认移除", $"确定要从Profile中移除筛选器 '{filter.DisplayName}' 吗？", "移除", "取消"))
                {
                    targetProfile.Filters.Remove(filter);
                    EditorUtility.SetDirty(targetProfile);
                }
            }
            EditorGUILayout.EndVertical();
            
            EditorGUILayout.EndHorizontal();
        }

        void CreateNewFilter()
        {
            var filter = CreateInstance<AssetFilter>();
            filter.name = $"NewFilter_{System.DateTime.Now:HHmmss}";
            filter.DisplayName = "新筛选器";
            
            var filterDir = "Assets/Editor/AssetPipeline/Data/Filters";
            if (!System.IO.Directory.Exists(filterDir))
            {
                System.IO.Directory.CreateDirectory(filterDir);
            }
            
            var filterPath = AssetDatabase.GenerateUniqueAssetPath($"{filterDir}/{filter.name}.asset");
            AssetDatabase.CreateAsset(filter, filterPath);
            
            targetProfile.Filters.Add(filter);
            AssetDatabase.SaveAssets();
            EditorUtility.SetDirty(targetProfile);
            
            AssetFilterEditorWindow.ShowWindow(filter);
        }

        void ShowAddExistingFilterMenu()
        {
            var allFilters = AssetDatabase.FindAssets("t:AssetFilter")
                .Select(AssetDatabase.GUIDToAssetPath)
                .Select(AssetDatabase.LoadAssetAtPath<AssetFilter>)
                .Where(f => f != null && !targetProfile.Filters.Contains(f))
                .ToArray();
            
            var menu = new GenericMenu();
            
            if (allFilters.Length == 0)
            {
                menu.AddDisabledItem(new GUIContent("没有可用的筛选器"));
            }
            else
            {
                foreach (var filter in allFilters)
                {
                    menu.AddItem(new GUIContent(filter.DisplayName), false, () =>
                    {
                        targetProfile.Filters.Add(filter);
                        EditorUtility.SetDirty(targetProfile);
                    });
                }
            }
            
            menu.ShowAsContext();
        }

        void DrawTestSection()
        {
            showTestConfig = UIHelper.DrawFoldout(ref showTestConfig, "测试匹配", "🧪", true);
            
            if (showTestConfig)
            {
                UIHelper.DrawBox(() =>
                {
                    EditorGUILayout.BeginHorizontal();
                    testAssetPath = EditorGUILayout.TextField("测试路径", testAssetPath);
                    if (GUILayout.Button("选择", GUILayout.Width(50)))
                    {
                        var path = EditorUtility.OpenFilePanel("选择测试文件", "Assets", "");
                        if (!string.IsNullOrEmpty(path) && path.StartsWith(Application.dataPath))
                        {
                            testAssetPath = "Assets" + path.Substring(Application.dataPath.Length);
                        }
                    }
                    EditorGUILayout.EndHorizontal();
                    
                    if (!string.IsNullOrEmpty(testAssetPath))
                    {
                        EditorGUILayout.Space();
                        
                        var isMatch = targetProfile.IsMatch(testAssetPath);
                        var message = isMatch ? "✅ 路径匹配成功" : "❌ 路径不匹配";
                        var messageType = isMatch ? MessageType.Info : MessageType.Warning;
                        
                        UIHelper.DrawStatusText(message, messageType);
                        
                        if (isMatch)
                        {
                            var processors = new List<AssetProcessor>();
                            targetProfile.GetMatchingProcessors(testAssetPath, processors);
                            var matchingFilters = targetProfile.GetMatchingFilters(testAssetPath);
                            
                            EditorGUILayout.LabelField($"匹配的筛选器: {matchingFilters.Count} 个", EditorStyles.boldLabel);
                            foreach (var filter in matchingFilters)
                            {
                                EditorGUILayout.LabelField($"  - {filter.DisplayName} (层级: {filter.Layer})", EditorStyles.miniLabel);
                            }
                            
                            EditorGUILayout.LabelField($"最终处理器: {processors.Count} 个", EditorStyles.boldLabel);
                            foreach (var processor in processors)
                            {
                                EditorGUILayout.LabelField($"  - {processor.DisplayName} (优先级: {processor.Priority})", EditorStyles.miniLabel);
                            }
                        }
                    }
                });
            }
        }
    }
} 