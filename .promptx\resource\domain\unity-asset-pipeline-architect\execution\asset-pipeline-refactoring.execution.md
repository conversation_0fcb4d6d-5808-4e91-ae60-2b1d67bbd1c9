<execution>
  <constraint>
    ## 客观技术限制
    - **Unity版本约束**：必须兼容Unity 2018.4，使用C# 6.0语法特性
    - **现有代码基础**：不能完全推倒重建，必须基于现有代码渐进式重构
    - **团队协作要求**：配置文件必须支持SVN版本控制和团队共享
    - **性能边界**：编辑器工具不能影响日常开发效率，响应时间<200ms
    - **PPT一致性**：最终实现必须与PPT描述保持一致，通过领导审核
    - **美术工作流**：必须符合大型项目美术资产复杂情况的日常使用需求
  </constraint>

  <rule>
    ## 强制性重构规则
    - **MVP方法论**：使用最小可行产品方法，优先核心功能，UI延后开发
    - **代码评分驱动**：每个模块必须通过5维度50分制评分，达到40分以上才能保留
    - **简化优先原则**：在功能完整性和实现复杂度之间，坚决选择简化实现
    - **现实主义验证**：所有设计决策必须通过"大型项目日常使用"的现实检验
    - **渐进式验证**：通过阶段性重构和用户反馈验证设计方向正确性
    - **无测试代码**：敏捷开发模式，暂不编写单元测试，专注核心架构
    - **底层优先**：自下而上修改代码，自上而下思考优化，确保架构一致性
  </rule>

  <guideline>
    ## 重构指导原则
    - **清晰、精准、明确**：代码设计的核心价值观，避免模糊和歧义
    - **原地优化优先**：让新架构从现有代码中自然演进，保留有价值部分
    - **配置化驱动**：将硬编码逻辑迁移到配置文件，支持无编译规则变更
    - **职责单一化**：每个类和方法都有明确单一的职责，避免功能耦合
    - **接口标准化**：统一处理器接口，支持不同Pipeline的灵活组合
    - **错误友好性**：提供清晰的错误信息和自动修复建议
    - **性能适度化**：避免过度复杂的性能优化，保持代码简洁可维护
  </guideline>

  <process>
    ## 具体重构执行流程

    ### Phase 1: 代码评估与优先级确定
    ```
    1. 建立评分体系
       - 功能性 (10分)：是否解决实际问题
       - 架构一致性 (10分)：是否符合整体设计
       - 扩展性 (10分)：是否便于后续扩展
       - 性能 (10分)：是否满足性能要求
       - 代码质量 (10分)：是否清晰、精准、明确
    
    2. 逐模块评分
       - AssetTree: 评估缓存机制和路径匹配效率
       - AssetProfile: 评估规则筛选逻辑和配置灵活性
       - AssetFilter: 评估类型匹配和处理器管理
       - AssetProcessor: 评估接口设计和实现复用性
       - Pipeline: 评估上下文管理和生命周期控制
    
    3. 重构优先级排序
       - 40分以上：保留并优化
       - 30-39分：重构改进
       - 30分以下：重新设计或删除
    ```

    ### Phase 2: 核心架构重构
    ```
    1. AssetTree架构简化
       - 保留基本树形结构，简化缓存机制
       - 优化路径匹配算法，提高查找效率
       - 统一TreeElement和Profile的关联关系
    
    2. AssetProfile规则优化
       - 明确PathPrefix匹配语义
       - 优化Filter层级处理逻辑
       - 统一处理器获取接口
    
    3. AssetFilter类型系统
       - 精确化资产类型匹配（避免OR组合）
       - 简化文件名匹配逻辑
       - 统一处理器排序和去重
    
    4. AssetProcessor接口统一
       - 标准化生命周期接口
       - 统一错误处理和日志记录
       - 简化配置验证逻辑
    ```

    ### Phase 3: Pipeline架构重构
    ```
    1. PipelineContext统一设计
       - 建立统一的Context基类
       - 标准化生命周期管理
       - 支持AssetPaths批量处理
    
    2. Pipeline特化处理
       - AssetImportPipeline: 保留循环检测和多轮导入复杂性
       - AssetModificationPipeline: 优化批量操作和错误聚合
       - SVNCommitPipeline: 保持状态连续性和数据库复用
    
    3. 通用Handler架构
       - 设计IAssetHandler接口覆盖90%简单场景
       - 保留Pipeline特化架构处理10%复杂场景
       - 建立Handler到Pipeline的自动路由机制
    ```

    ### Phase 4: 配置系统整合
    ```
    1. 双配置系统合并
       - AssetPipelineConfig: 团队共享项目设置（保存到SVN）
       - AssetPipelineSettings: 本地编辑器运行时设置（不序列化）
       - 建立配置优先级和覆盖机制
    
    2. JSON配置优化
       - 简化JSON优先加载逻辑
       - 优化反射使用，减少性能开销
       - 建立配置验证和错误恢复机制
    
    3. 配置迁移工具
       - 提供ScriptableObject到JSON的迁移工具
       - 支持配置版本升级和兼容性处理
       - 建立配置备份和恢复机制
    ```

    ### Phase 5: 质量验证与优化
    ```
    1. 重构后评分验证
       - 对重构后的模块重新评分
       - 确保所有核心模块达到40分以上
       - 记录改进点和剩余问题
    
    2. 现实场景测试
       - 使用大型项目资源进行压力测试
       - 验证美术工作流的适配性
       - 收集性能数据和用户反馈
    
    3. 持续优化迭代
       - 基于测试结果进行微调
       - 解决发现的边界情况问题
       - 为UI开发阶段做好准备
    ```
  </process>

  <criteria>
    ## 重构质量评价标准

    ### 架构质量指标
    - ✅ 代码复用率提升30%以上
    - ✅ 配置变更无需编译，支持热更新
    - ✅ 核心模块评分达到40分以上
    - ✅ 接口设计清晰，职责分离明确

    ### 性能质量指标
    - ✅ 资源检查响应时间<200ms
    - ✅ 大批量操作（1000+文件）处理时间<10s
    - ✅ 内存占用稳定，无明显泄漏
    - ✅ 编辑器启动时间无明显增加

    ### 功能完整性指标
    - ✅ 覆盖PPT描述的所有核心功能
    - ✅ 支持现有所有Pipeline的功能
    - ✅ 配置系统完全可用
    - ✅ 错误处理和自动修复机制完善

    ### 可维护性指标
    - ✅ 代码结构清晰，符合"清晰、精准、明确"原则
    - ✅ 新增处理器和规则简单易行
    - ✅ 配置文件版本控制友好
    - ✅ 文档和注释完整，便于团队维护

    ### 用户体验指标
    - ✅ 美术人员操作流程顺畅
    - ✅ 错误信息清晰，修复建议实用
    - ✅ 配置界面直观易用
    - ✅ 与现有工作流无缝集成
  </criteria>
</execution>
