using System;
using L10.Editor.AssetPipeline.Sqlite;

namespace AssetPipeline.Database.Models
{
    [Table("TextureAuditInfo")]
    public class TextureAuditInfo
    {
        [PrimaryKey]
        public string GUID { get; set; }
        
        public string Path { get; set; }
        public int Width { get; set; }
        public int Height { get; set; }
        
        /// <summary>
        /// The version of the processor that performed the audit.
        /// </summary>
        public uint ProcessorVersion { get; set; }
        
        public DateTime AuditTime { get; set; }
    }
} 