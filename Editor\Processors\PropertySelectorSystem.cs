using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using UnityEditor.Presets;

namespace AssetPipeline.Processors
{
    /// <summary>
    /// 属性选择器系统
    /// 提供直观的属性选择界面，使用Unity Inspector风格的属性名称和分组
    /// </summary>
    public static class PropertySelectorSystem
    {
        #region 属性信息结构

        /// <summary>
        /// 属性信息
        /// </summary>
        [System.Serializable]
        public class PropertyInfo
        {
            public string propertyPath;        // 技术属性路径
            public string displayName;         // 用户友好的显示名称
            public string description;         // 属性描述
            public string category;            // 属性分类
            public string subCategory;         // 子分类
            public bool isSelected;            // 是否被选中
            public PropertyType propertyType;  // 属性类型
            public string importerType;        // 适用的导入器类型
        }

        /// <summary>
        /// 属性类型
        /// </summary>
        public enum PropertyType
        {
            Basic,          // 基础设置
            Advanced,       // 高级设置
            Platform,       // 平台设置
            Performance,    // 性能相关
            Quality,        // 质量设置
            Animation,      // 动画设置
            Material,       // 材质设置
            Audio,          // 音频设置
            Other           // 其他
        }

        /// <summary>
        /// 属性分类信息
        /// </summary>
        [System.Serializable]
        public class PropertyCategory
        {
            public string name;
            public string displayName;
            public string description;
            public List<PropertyInfo> properties = new List<PropertyInfo>();
            public bool isExpanded = true;
        }

        #endregion

        #region 预定义属性映射

        /// <summary>
        /// TextureImporter属性映射
        /// </summary>
        private static readonly Dictionary<string, PropertyInfo> TextureImporterProperties = new Dictionary<string, PropertyInfo>
        {
            // 基础纹理设置
            ["m_TextureType"] = new PropertyInfo
            {
                propertyPath = "m_TextureType",
                displayName = "纹理类型",
                description = "指定纹理的用途类型（Default、Sprite、Normal Map等）",
                category = "纹理设置",
                subCategory = "基础设置",
                propertyType = PropertyType.Basic,
                importerType = "TextureImporter"
            },
            ["m_sRGBTexture"] = new PropertyInfo
            {
                propertyPath = "m_sRGBTexture",
                displayName = "sRGB（颜色纹理）",
                description = "是否将纹理视为sRGB颜色空间（用于颜色纹理）",
                category = "纹理设置",
                subCategory = "颜色空间",
                propertyType = PropertyType.Basic,
                importerType = "TextureImporter"
            },
            ["m_IsReadable"] = new PropertyInfo
            {
                propertyPath = "m_IsReadable",
                displayName = "读/写权限",
                description = "是否允许脚本访问纹理数据（会增加内存使用）",
                category = "高级设置",
                subCategory = "内存优化",
                propertyType = PropertyType.Performance,
                importerType = "TextureImporter"
            },
            ["m_TextureCompression"] = new PropertyInfo
            {
                propertyPath = "m_TextureCompression",
                displayName = "压缩",
                description = "纹理压缩方式（None、Low Quality、Normal Quality、High Quality）",
                category = "纹理设置",
                subCategory = "压缩设置",
                propertyType = PropertyType.Quality,
                importerType = "TextureImporter"
            },
            ["m_StreamingMipmaps"] = new PropertyInfo
            {
                propertyPath = "m_StreamingMipmaps",
                displayName = "流式Mipmap",
                description = "启用Mipmap流式加载以减少内存使用",
                category = "高级设置",
                subCategory = "内存优化",
                propertyType = PropertyType.Performance,
                importerType = "TextureImporter"
            },
            ["m_BorderMipmap"] = new PropertyInfo
            {
                propertyPath = "m_BorderMipmap",
                displayName = "边界Mipmap",
                description = "在Mipmap边界保留颜色",
                category = "高级设置",
                subCategory = "Mipmap设置",
                propertyType = PropertyType.Advanced,
                importerType = "TextureImporter"
            },
            ["m_MipMapsPreserveCoverage"] = new PropertyInfo
            {
                propertyPath = "m_MipMapsPreserveCoverage",
                displayName = "Mipmap保持覆盖",
                description = "在生成Mipmap时保持Alpha覆盖",
                category = "高级设置",
                subCategory = "Mipmap设置",
                propertyType = PropertyType.Advanced,
                importerType = "TextureImporter"
            },
            ["m_AlphaTestReferenceValue"] = new PropertyInfo
            {
                propertyPath = "m_AlphaTestReferenceValue",
                displayName = "Alpha测试参考值",
                description = "Alpha测试的参考值",
                category = "高级设置",
                subCategory = "Alpha设置",
                propertyType = PropertyType.Advanced,
                importerType = "TextureImporter"
            },
            ["m_MipMapMode"] = new PropertyInfo
            {
                propertyPath = "m_MipMapMode",
                displayName = "生成Mip Maps",
                description = "是否生成Mipmap",
                category = "纹理设置",
                subCategory = "Mipmap设置",
                propertyType = PropertyType.Basic,
                importerType = "TextureImporter"
            },
            ["m_EnableMipMap"] = new PropertyInfo
            {
                propertyPath = "m_EnableMipMap",
                displayName = "启用Mip Maps",
                description = "是否启用Mipmap",
                category = "纹理设置",
                subCategory = "Mipmap设置",
                propertyType = PropertyType.Basic,
                importerType = "TextureImporter"
            },
            ["m_FadeOut"] = new PropertyInfo
            {
                propertyPath = "m_FadeOut",
                displayName = "淡出Mip Maps",
                description = "在较小的Mipmap级别中淡出到灰色",
                category = "高级设置",
                subCategory = "Mipmap设置",
                propertyType = PropertyType.Advanced,
                importerType = "TextureImporter"
            },
            ["m_FilterMode"] = new PropertyInfo
            {
                propertyPath = "m_FilterMode",
                displayName = "过滤模式",
                description = "纹理过滤模式（Point、Bilinear、Trilinear）",
                category = "纹理设置",
                subCategory = "过滤设置",
                propertyType = PropertyType.Basic,
                importerType = "TextureImporter"
            },
            ["m_WrapMode"] = new PropertyInfo
            {
                propertyPath = "m_WrapMode",
                displayName = "包装模式",
                description = "纹理包装模式（Repeat、Clamp、Mirror等）",
                category = "纹理设置",
                subCategory = "包装设置",
                propertyType = PropertyType.Basic,
                importerType = "TextureImporter"
            },
            ["m_NPOTScale"] = new PropertyInfo
            {
                propertyPath = "m_NPOTScale",
                displayName = "非2次幂缩放",
                description = "非2次幂纹理的缩放方式",
                category = "高级设置",
                subCategory = "缩放设置",
                propertyType = PropertyType.Advanced,
                importerType = "TextureImporter"
            },

            // 平台设置 - Default Platform
            ["m_PlatformSettings.Array.data[0].m_BuildTarget"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[0].m_BuildTarget",
                displayName = "默认平台",
                description = "默认平台设置",
                category = "平台设置",
                subCategory = "默认平台",
                propertyType = PropertyType.Platform,
                importerType = "TextureImporter"
            },
            ["m_PlatformSettings.Array.data[0].m_MaxTextureSize"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[0].m_MaxTextureSize",
                displayName = "最大尺寸",
                description = "纹理的最大尺寸限制",
                category = "平台设置",
                subCategory = "默认平台",
                propertyType = PropertyType.Performance,
                importerType = "TextureImporter"
            },
            ["m_PlatformSettings.Array.data[0].m_ResizeAlgorithm"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[0].m_ResizeAlgorithm",
                displayName = "缩放算法",
                description = "纹理缩放时使用的算法",
                category = "平台设置",
                subCategory = "默认平台",
                propertyType = PropertyType.Quality,
                importerType = "TextureImporter"
            },
            ["m_PlatformSettings.Array.data[0].m_TextureFormat"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[0].m_TextureFormat",
                displayName = "格式",
                description = "纹理的存储格式",
                category = "平台设置",
                subCategory = "默认平台",
                propertyType = PropertyType.Quality,
                importerType = "TextureImporter"
            },
            ["m_PlatformSettings.Array.data[0].m_TextureCompression"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[0].m_TextureCompression",
                displayName = "压缩",
                description = "纹理压缩方式",
                category = "平台设置",
                subCategory = "默认平台",
                propertyType = PropertyType.Performance,
                importerType = "TextureImporter"
            },
            ["m_PlatformSettings.Array.data[0].m_CompressionQuality"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[0].m_CompressionQuality",
                displayName = "压缩质量",
                description = "压缩质量设置",
                category = "平台设置",
                subCategory = "默认平台",
                propertyType = PropertyType.Quality,
                importerType = "TextureImporter"
            },
            ["m_PlatformSettings.Array.data[0].m_CrunchedCompression"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[0].m_CrunchedCompression",
                displayName = "Crunch压缩",
                description = "是否使用Crunch压缩",
                category = "平台设置",
                subCategory = "默认平台",
                propertyType = PropertyType.Performance,
                importerType = "TextureImporter"
            },
            ["m_PlatformSettings.Array.data[0].m_AllowsAlphaSplitting"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[0].m_AllowsAlphaSplitting",
                displayName = "Alpha分离",
                description = "是否允许Alpha通道分离",
                category = "平台设置",
                subCategory = "默认平台",
                propertyType = PropertyType.Advanced,
                importerType = "TextureImporter"
            },
            ["m_PlatformSettings.Array.data[0].m_Overridden"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[0].m_Overridden",
                displayName = "覆盖设置",
                description = "是否覆盖默认设置",
                category = "平台设置",
                subCategory = "默认平台",
                propertyType = PropertyType.Basic,
                importerType = "TextureImporter"
            },

            // iOS平台设置
            ["m_PlatformSettings.Array.data[1].m_BuildTarget"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[1].m_BuildTarget",
                displayName = "iOS平台",
                description = "iOS平台设置",
                category = "平台设置",
                subCategory = "iOS平台",
                propertyType = PropertyType.Platform,
                importerType = "TextureImporter"
            },
            ["m_PlatformSettings.Array.data[1].m_MaxTextureSize"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[1].m_MaxTextureSize",
                displayName = "最大尺寸 (iOS)",
                description = "iOS平台纹理的最大尺寸限制",
                category = "平台设置",
                subCategory = "iOS平台",
                propertyType = PropertyType.Performance,
                importerType = "TextureImporter"
            },
            ["m_PlatformSettings.Array.data[1].m_TextureFormat"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[1].m_TextureFormat",
                displayName = "格式 (iOS)",
                description = "iOS平台纹理的存储格式",
                category = "平台设置",
                subCategory = "iOS平台",
                propertyType = PropertyType.Quality,
                importerType = "TextureImporter"
            },
            ["m_PlatformSettings.Array.data[1].m_CompressionQuality"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[1].m_CompressionQuality",
                displayName = "压缩质量 (iOS)",
                description = "iOS平台压缩质量设置",
                category = "平台设置",
                subCategory = "iOS平台",
                propertyType = PropertyType.Quality,
                importerType = "TextureImporter"
            },
            ["m_PlatformSettings.Array.data[1].m_Overridden"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[1].m_Overridden",
                displayName = "覆盖设置 (iOS)",
                description = "是否覆盖iOS平台的默认设置",
                category = "平台设置",
                subCategory = "iOS平台",
                propertyType = PropertyType.Basic,
                importerType = "TextureImporter"
            },

            // Android平台设置
            ["m_PlatformSettings.Array.data[2].m_BuildTarget"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[2].m_BuildTarget",
                displayName = "Android平台",
                description = "Android平台设置",
                category = "平台设置",
                subCategory = "Android平台",
                propertyType = PropertyType.Platform,
                importerType = "TextureImporter"
            },
            ["m_PlatformSettings.Array.data[2].m_MaxTextureSize"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[2].m_MaxTextureSize",
                displayName = "最大尺寸 (Android)",
                description = "Android平台纹理的最大尺寸限制",
                category = "平台设置",
                subCategory = "Android平台",
                propertyType = PropertyType.Performance,
                importerType = "TextureImporter"
            },
            ["m_PlatformSettings.Array.data[2].m_TextureFormat"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[2].m_TextureFormat",
                displayName = "格式 (Android)",
                description = "Android平台纹理的存储格式",
                category = "平台设置",
                subCategory = "Android平台",
                propertyType = PropertyType.Quality,
                importerType = "TextureImporter"
            },
            ["m_PlatformSettings.Array.data[2].m_CompressionQuality"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[2].m_CompressionQuality",
                displayName = "压缩质量 (Android)",
                description = "Android平台压缩质量设置",
                category = "平台设置",
                subCategory = "Android平台",
                propertyType = PropertyType.Quality,
                importerType = "TextureImporter"
            },
            ["m_PlatformSettings.Array.data[2].m_Overridden"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[2].m_Overridden",
                displayName = "覆盖设置 (Android)",
                description = "是否覆盖Android平台的默认设置",
                category = "平台设置",
                subCategory = "Android平台",
                propertyType = PropertyType.Basic,
                importerType = "TextureImporter"
            },

            // Standalone平台设置
            ["m_PlatformSettings.Array.data[3].m_BuildTarget"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[3].m_BuildTarget",
                displayName = "Standalone平台",
                description = "Standalone平台设置",
                category = "平台设置",
                subCategory = "Standalone平台",
                propertyType = PropertyType.Platform,
                importerType = "TextureImporter"
            },
            ["m_PlatformSettings.Array.data[3].m_MaxTextureSize"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[3].m_MaxTextureSize",
                displayName = "最大尺寸 (Standalone)",
                description = "Standalone平台纹理的最大尺寸限制",
                category = "平台设置",
                subCategory = "Standalone平台",
                propertyType = PropertyType.Performance,
                importerType = "TextureImporter"
            },
            ["m_PlatformSettings.Array.data[3].m_TextureFormat"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[3].m_TextureFormat",
                displayName = "格式 (Standalone)",
                description = "Standalone平台纹理的存储格式",
                category = "平台设置",
                subCategory = "Standalone平台",
                propertyType = PropertyType.Quality,
                importerType = "TextureImporter"
            },
            ["m_PlatformSettings.Array.data[3].m_CompressionQuality"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[3].m_CompressionQuality",
                displayName = "压缩质量 (Standalone)",
                description = "Standalone平台压缩质量设置",
                category = "平台设置",
                subCategory = "Standalone平台",
                propertyType = PropertyType.Quality,
                importerType = "TextureImporter"
            },
            ["m_PlatformSettings.Array.data[3].m_Overridden"] = new PropertyInfo
            {
                propertyPath = "m_PlatformSettings.Array.data[3].m_Overridden",
                displayName = "覆盖设置 (Standalone)",
                description = "是否覆盖Standalone平台的默认设置",
                category = "平台设置",
                subCategory = "Standalone平台",
                propertyType = PropertyType.Basic,
                importerType = "TextureImporter"
            }
        };

        /// <summary>
        /// ModelImporter属性映射
        /// </summary>
        private static readonly Dictionary<string, PropertyInfo> ModelImporterProperties = new Dictionary<string, PropertyInfo>
        {
            // 网格设置
            ["m_MeshCompression"] = new PropertyInfo
            {
                propertyPath = "m_MeshCompression",
                displayName = "网格压缩",
                description = "网格压缩级别（Off、Low、Medium、High）",
                category = "网格",
                subCategory = "压缩设置",
                propertyType = PropertyType.Performance,
                importerType = "ModelImporter"
            },
            ["m_IsReadable"] = new PropertyInfo
            {
                propertyPath = "m_IsReadable",
                displayName = "读/写权限",
                description = "是否允许脚本访问网格数据（会增加内存使用）",
                category = "网格",
                subCategory = "内存优化",
                propertyType = PropertyType.Performance,
                importerType = "ModelImporter"
            },
            ["m_OptimizeMesh"] = new PropertyInfo
            {
                propertyPath = "m_OptimizeMesh",
                displayName = "优化网格",
                description = "优化网格的顶点顺序以提高渲染性能",
                category = "网格",
                subCategory = "优化设置",
                propertyType = PropertyType.Performance,
                importerType = "ModelImporter"
            },
            ["m_GenerateSecondaryUV"] = new PropertyInfo
            {
                propertyPath = "m_GenerateSecondaryUV",
                displayName = "生成光照贴图UV",
                description = "为光照贴图生成第二套UV坐标",
                category = "网格",
                subCategory = "UV设置",
                propertyType = PropertyType.Advanced,
                importerType = "ModelImporter"
            },
            ["m_UseFileUnits"] = new PropertyInfo
            {
                propertyPath = "m_UseFileUnits",
                displayName = "使用文件单位",
                description = "使用文件中定义的单位而不是缩放因子",
                category = "变换",
                subCategory = "缩放设置",
                propertyType = PropertyType.Basic,
                importerType = "ModelImporter"
            },
            ["m_GlobalScale"] = new PropertyInfo
            {
                propertyPath = "m_GlobalScale",
                displayName = "缩放因子",
                description = "应用到模型的全局缩放因子",
                category = "变换",
                subCategory = "缩放设置",
                propertyType = PropertyType.Basic,
                importerType = "ModelImporter"
            },
            ["m_UseFileScale"] = new PropertyInfo
            {
                propertyPath = "m_UseFileScale",
                displayName = "使用文件缩放",
                description = "使用文件中定义的缩放",
                category = "变换",
                subCategory = "缩放设置",
                propertyType = PropertyType.Basic,
                importerType = "ModelImporter"
            },
            
            // 动画设置
            ["m_ImportAnimation"] = new PropertyInfo
            {
                propertyPath = "m_ImportAnimation",
                displayName = "导入动画",
                description = "是否导入动画数据",
                category = "动画",
                subCategory = "基础设置",
                propertyType = PropertyType.Animation,
                importerType = "ModelImporter"
            },
            ["m_BakeIK"] = new PropertyInfo
            {
                propertyPath = "m_BakeIK",
                displayName = "烘焙IK",
                description = "将IK动画烘焙到FK关键帧中",
                category = "动画",
                subCategory = "IK设置",
                propertyType = PropertyType.Animation,
                importerType = "ModelImporter"
            },
            ["m_ResampleCurves"] = new PropertyInfo
            {
                propertyPath = "m_ResampleCurves",
                displayName = "重采样曲线",
                description = "重采样动画曲线",
                category = "动画",
                subCategory = "优化设置",
                propertyType = PropertyType.Animation,
                importerType = "ModelImporter"
            },
            ["m_OptimizeGameObjects"] = new PropertyInfo
            {
                propertyPath = "m_OptimizeGameObjects",
                displayName = "优化游戏对象",
                description = "移除不必要的游戏对象层级",
                category = "动画",
                subCategory = "优化设置",
                propertyType = PropertyType.Performance,
                importerType = "ModelImporter"
            },
            
            // 材质设置
            ["m_ImportMaterials"] = new PropertyInfo
            {
                propertyPath = "m_ImportMaterials",
                displayName = "导入材质",
                description = "是否导入材质",
                category = "材质",
                subCategory = "基础设置",
                propertyType = PropertyType.Material,
                importerType = "ModelImporter"
            },
            ["m_MaterialName"] = new PropertyInfo
            {
                propertyPath = "m_MaterialName",
                displayName = "材质命名",
                description = "材质命名方式",
                category = "材质",
                subCategory = "命名设置",
                propertyType = PropertyType.Material,
                importerType = "ModelImporter"
            },
            ["m_MaterialSearch"] = new PropertyInfo
            {
                propertyPath = "m_MaterialSearch",
                displayName = "材质搜索",
                description = "材质搜索方式",
                category = "材质",
                subCategory = "搜索设置",
                propertyType = PropertyType.Material,
                importerType = "ModelImporter"
            }
        };

        /// <summary>
        /// AudioImporter属性映射
        /// </summary>
        private static readonly Dictionary<string, PropertyInfo> AudioImporterProperties = new Dictionary<string, PropertyInfo>
        {
            ["m_ForceToMono"] = new PropertyInfo
            {
                propertyPath = "m_ForceToMono",
                displayName = "强制单声道",
                description = "将立体声音频转换为单声道",
                category = "音频设置",
                subCategory = "基础设置",
                propertyType = PropertyType.Audio,
                importerType = "AudioImporter"
            },
            ["m_Normalize"] = new PropertyInfo
            {
                propertyPath = "m_Normalize",
                displayName = "标准化",
                description = "标准化音频音量",
                category = "音频设置",
                subCategory = "音量设置",
                propertyType = PropertyType.Audio,
                importerType = "AudioImporter"
            },
            ["m_PreloadAudioData"] = new PropertyInfo
            {
                propertyPath = "m_PreloadAudioData",
                displayName = "预加载音频数据",
                description = "在场景加载时预加载音频数据",
                category = "音频设置",
                subCategory = "加载设置",
                propertyType = PropertyType.Performance,
                importerType = "AudioImporter"
            },
            ["m_LoadInBackground"] = new PropertyInfo
            {
                propertyPath = "m_LoadInBackground",
                displayName = "后台加载",
                description = "在后台线程中加载音频",
                category = "音频设置",
                subCategory = "加载设置",
                propertyType = PropertyType.Performance,
                importerType = "AudioImporter"
            }
        };

        #endregion

        #region 公共方法

        /// <summary>
        /// 根据Preset获取属性信息列表
        /// </summary>
        public static List<PropertyInfo> GetPropertiesFromPreset(Preset preset)
        {
            if (preset == null)
                return new List<PropertyInfo>();

            var properties = new List<PropertyInfo>();
            var modifications = preset.PropertyModifications;
            var importerType = GetImporterTypeFromPreset(preset);

            foreach (var modification in modifications)
            {
                var propertyInfo = GetPropertyInfo(modification.propertyPath, importerType);
                if (propertyInfo != null)
                {
                    properties.Add(propertyInfo);
                }
            }

            return properties.OrderBy(p => p.category).ThenBy(p => p.subCategory).ThenBy(p => p.displayName).ToList();
        }

        /// <summary>
        /// 按分类组织属性
        /// </summary>
        public static List<PropertyCategory> OrganizePropertiesByCategory(List<PropertyInfo> properties)
        {
            var categories = new Dictionary<string, PropertyCategory>();

            foreach (var property in properties)
            {
                if (!categories.ContainsKey(property.category))
                {
                    categories[property.category] = new PropertyCategory
                    {
                        name = property.category,
                        displayName = property.category,
                        description = GetCategoryDescription(property.category)
                    };
                }

                categories[property.category].properties.Add(property);
            }

            return categories.Values.OrderBy(c => GetCategoryOrder(c.name)).ToList();
        }

        /// <summary>
        /// 搜索属性
        /// </summary>
        public static List<PropertyInfo> SearchProperties(List<PropertyInfo> properties, string searchTerm)
        {
            if (string.IsNullOrEmpty(searchTerm))
                return properties;

            searchTerm = searchTerm.ToLower();
            return properties.Where(p => 
                p.displayName.ToLower().Contains(searchTerm) ||
                p.description.ToLower().Contains(searchTerm) ||
                p.category.ToLower().Contains(searchTerm) ||
                p.subCategory.ToLower().Contains(searchTerm)
            ).ToList();
        }

        /// <summary>
        /// 按属性类型过滤
        /// </summary>
        public static List<PropertyInfo> FilterByPropertyType(List<PropertyInfo> properties, PropertyType propertyType)
        {
            return properties.Where(p => p.propertyType == propertyType).ToList();
        }

        /// <summary>
        /// 根据导入器类型获取属性列表
        /// </summary>
        public static List<PropertyInfo> GetPropertiesForImporterType(string importerType)
        {
            var properties = new List<PropertyInfo>();

            switch (importerType)
            {
                case "TextureImporter":
                    properties.AddRange(TextureImporterProperties.Values);
                    break;
                case "ModelImporter":
                    properties.AddRange(ModelImporterProperties.Values);
                    break;
                case "AudioImporter":
                    properties.AddRange(AudioImporterProperties.Values);
                    break;
            }

            return properties.OrderBy(p => p.category).ThenBy(p => p.subCategory).ThenBy(p => p.displayName).ToList();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 从Preset推断导入器类型
        /// </summary>
        private static string GetImporterTypeFromPreset(Preset preset)
        {
            var modifications = preset.PropertyModifications;
            if (modifications == null || modifications.Length == 0)
                return "Unknown";

            foreach (var modification in modifications)
            {
                if (modification.propertyPath.Contains("m_sRGBTexture") || 
                    modification.propertyPath.Contains("m_TextureType"))
                {
                    return "TextureImporter";
                }
                else if (modification.propertyPath.Contains("m_OptimizeMesh") || 
                        modification.propertyPath.Contains("m_ImportAnimation"))
                {
                    return "ModelImporter";
                }
                else if (modification.propertyPath.Contains("m_ForceToMono") || 
                        modification.propertyPath.Contains("m_DefaultSettings"))
                {
                    return "AudioImporter";
                }
            }

            return "Unknown";
        }

        /// <summary>
        /// 获取属性信息
        /// </summary>
        private static PropertyInfo GetPropertyInfo(string propertyPath, string importerType)
        {
            PropertyInfo propertyInfo = null;

            // 根据导入器类型查找预定义属性
            switch (importerType)
            {
                case "TextureImporter":
                    TextureImporterProperties.TryGetValue(propertyPath, out propertyInfo);
                    break;
                case "ModelImporter":
                    ModelImporterProperties.TryGetValue(propertyPath, out propertyInfo);
                    break;
                case "AudioImporter":
                    AudioImporterProperties.TryGetValue(propertyPath, out propertyInfo);
                    break;
            }

            // 如果没有找到预定义属性，创建默认属性信息
            if (propertyInfo == null)
            {
                propertyInfo = new PropertyInfo
                {
                    propertyPath = propertyPath,
                    displayName = GetFriendlyPropertyName(propertyPath),
                    description = $"属性路径: {propertyPath}",
                    category = "其他设置",
                    subCategory = "未分类",
                    propertyType = PropertyType.Other,
                    importerType = importerType
                };
            }

            return propertyInfo;
        }

        /// <summary>
        /// 获取友好的属性名称
        /// </summary>
        private static string GetFriendlyPropertyName(string propertyPath)
        {
            // 移除前缀和后缀
            var name = propertyPath.Replace("m_", "").Replace("_", " ");
            
            // 处理特殊情况
            name = name.Replace("sRGB", "sRGB");
            name = name.Replace("UV", "UV");
            name = name.Replace("LOD", "LOD");
            name = name.Replace("IK", "IK");
            
            // 首字母大写
            if (name.Length > 0)
            {
                name = char.ToUpper(name[0]) + name.Substring(1);
            }

            return name;
        }

        /// <summary>
        /// 获取分类描述
        /// </summary>
        private static string GetCategoryDescription(string category)
        {
            switch (category)
            {
                case "纹理设置": return "控制纹理的基本属性和显示方式";
                case "高级设置": return "高级纹理选项和优化设置";
                case "平台设置": return "特定平台的纹理格式和压缩设置";
                case "网格": return "3D模型的网格相关设置";
                case "动画": return "动画导入和优化设置";
                case "材质": return "材质导入和处理设置";
                case "变换": return "模型的位置、旋转和缩放设置";
                case "音频设置": return "音频文件的导入和处理设置";
                default: return "其他导入设置";
            }
        }

        /// <summary>
        /// 获取分类排序顺序
        /// </summary>
        private static int GetCategoryOrder(string category)
        {
            switch (category)
            {
                case "纹理设置": return 1;
                case "网格": return 1;
                case "音频设置": return 1;
                case "动画": return 2;
                case "材质": return 3;
                case "变换": return 4;
                case "平台设置": return 5;
                case "高级设置": return 6;
                case "其他设置": return 99;
                default: return 50;
            }
        }

        #endregion
    }
}
