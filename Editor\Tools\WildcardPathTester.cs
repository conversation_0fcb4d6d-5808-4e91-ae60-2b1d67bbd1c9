using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using AssetPipeline.Core;

namespace AssetPipeline.Tools
{
    /// <summary>
    /// 通配符路径匹配测试工具
    /// 用于验证Trie通配符功能的正确性和性能
    /// </summary>
    public class WildcardPathTester : EditorWindow
    {
        private string testPath = "Assets/Art/Characters/Hero/Textures/diffuse.png";
        private List<string> testPatterns = new List<string>
        {
            "Assets/Art/*/Textures/",      // 单层通配符
            "Assets/**/Textures/",         // 递归通配符
            "Assets/Art/Characters/",      // 精确匹配
            "Assets/Audio/",               // 不匹配的路径
            "Assets/**/",                  // 匹配所有子目录
        };
        
        private ProfilePathTrie testTrie;
        private List<AssetProfile> testProfiles;
        private Vector2 scrollPosition;

        [MenuItem("AssetPipeline/Tools/Wildcard Path Tester")]
        public static void ShowWindow()
        {
            GetWindow<WildcardPathTester>("通配符路径测试器");
        }

        void OnEnable()
        {
            InitializeTestData();
        }

        void InitializeTestData()
        {
            testTrie = new ProfilePathTrie();
            testProfiles = new List<AssetProfile>();

            // 创建测试Profile
            foreach (var pattern in testPatterns)
            {
                var profile = CreateInstance<AssetProfile>();
                profile.PathPrefix = pattern;
                profile.DisplayName = $"Profile for {pattern}";
                profile.Enabled = true;
                testProfiles.Add(profile);
            }

            // 构建Trie
            testTrie.BuildFromProfiles(testProfiles);
        }

        void OnGUI()
        {
            EditorGUILayout.LabelField("🧪 通配符路径匹配测试", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            DrawTestInput();
            EditorGUILayout.Space();
            
            DrawPatternList();
            EditorGUILayout.Space();
            
            DrawTestResults();
            EditorGUILayout.Space();
            
            DrawPerformanceInfo();
        }

        void DrawTestInput()
        {
            EditorGUILayout.LabelField("📁 测试路径", EditorStyles.boldLabel);
            EditorGUILayout.BeginHorizontal();
            testPath = EditorGUILayout.TextField("资源路径:", testPath);
            if (GUILayout.Button("测试", GUILayout.Width(60)))
            {
                RunTest();
            }
            EditorGUILayout.EndHorizontal();
        }

        void DrawPatternList()
        {
            EditorGUILayout.LabelField("🎯 测试模式", EditorStyles.boldLabel);
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(120));
            
            for (int i = 0; i < testPatterns.Count; i++)
            {
                EditorGUILayout.BeginHorizontal();
                
                // 验证图标
                var isValid = ProfilePathTrie.IsValidPathPattern(testPatterns[i]);
                EditorGUILayout.LabelField(isValid ? "✅" : "❌", GUILayout.Width(20));
                
                // 模式输入
                testPatterns[i] = EditorGUILayout.TextField(testPatterns[i]);
                
                // 删除按钮
                if (GUILayout.Button("删除", GUILayout.Width(40)))
                {
                    testPatterns.RemoveAt(i);
                    InitializeTestData();
                    break;
                }
                
                EditorGUILayout.EndHorizontal();
            }
            
            EditorGUILayout.EndScrollView();
            
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("添加模式"))
            {
                testPatterns.Add("Assets/");
                InitializeTestData();
            }
            if (GUILayout.Button("重置为默认"))
            {
                testPatterns = new List<string>
                {
                    "Assets/Art/*/Textures/",
                    "Assets/**/Textures/",
                    "Assets/Art/Characters/",
                    "Assets/Audio/",
                    "Assets/**/",
                };
                InitializeTestData();
            }
            EditorGUILayout.EndHorizontal();
        }

        void DrawTestResults()
        {
            EditorGUILayout.LabelField("📊 匹配结果", EditorStyles.boldLabel);
            
            if (testTrie == null)
            {
                EditorGUILayout.HelpBox("请先初始化测试数据", MessageType.Warning);
                return;
            }

            var directoryPath = ExtractDirectoryPath(testPath);
            var matchingProfiles = new List<AssetProfile>();
            
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            testTrie.GetMatchingProfiles(directoryPath, matchingProfiles);
            stopwatch.Stop();

            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField($"目录路径: {directoryPath}");
            EditorGUILayout.LabelField($"匹配数量: {matchingProfiles.Count}");
            EditorGUILayout.LabelField($"查询时间: {stopwatch.ElapsedTicks * 1000000 / System.Diagnostics.Stopwatch.Frequency:F2} μs");
            
            if (matchingProfiles.Count > 0)
            {
                EditorGUILayout.Space();
                EditorGUILayout.LabelField("匹配的模式:", EditorStyles.boldLabel);
                foreach (var profile in matchingProfiles)
                {
                    EditorGUILayout.LabelField($"  • {profile.PathPrefix}", EditorStyles.miniLabel);
                }
            }
            EditorGUILayout.EndVertical();
        }

        void DrawPerformanceInfo()
        {
            EditorGUILayout.LabelField("⚡ 性能统计", EditorStyles.boldLabel);
            
            if (testTrie != null)
            {
                var stats = testTrie.GetStatistics();
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                EditorGUILayout.LabelField($"节点数量: {stats.TotalNodes}");
                EditorGUILayout.LabelField($"Profile数量: {stats.TotalProfiles}");
                EditorGUILayout.LabelField($"内存估算: {stats.MemoryEstimate / 1024f:F1} KB");
                EditorGUILayout.LabelField($"总查询次数: {stats.TotalQueries}");
                EditorGUILayout.LabelField($"平均查询时间: {stats.AvgQueryTime:F2} μs");
                EditorGUILayout.EndVertical();
                
                if (GUILayout.Button("重置统计"))
                {
                    testTrie.ResetStatistics();
                }
            }
        }

        void RunTest()
        {
            InitializeTestData();
        }

        private string ExtractDirectoryPath(string assetPath)
        {
            var normalizedPath = assetPath.Replace('\\', '/');
            var lastSlashIndex = normalizedPath.LastIndexOf('/');
            return lastSlashIndex >= 0 ? normalizedPath.Substring(0, lastSlashIndex) : "";
        }
    }
}
