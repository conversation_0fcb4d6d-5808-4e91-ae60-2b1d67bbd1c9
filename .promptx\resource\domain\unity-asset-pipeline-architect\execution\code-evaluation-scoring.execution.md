<execution>
  <constraint>
    ## 客观评分限制
    - **评分标准固化**：5维度50分制评分体系不可随意变更
    - **职业素养标准**：必须符合资深Unity系统架构师的专业判断标准
    - **现实项目导向**：评分必须基于大型项目实际使用场景
    - **量化可比性**：评分结果必须具有可比性和一致性
    - **重构决策依据**：评分结果直接决定代码的保留、重构或删除
  </constraint>

  <rule>
    ## 强制性评分规则
    - **5维度完整性**：每个代码模块必须从功能性、架构一致性、扩展性、性能、代码质量5个维度评分
    - **50分制标准**：总分50分，每个维度10分，必须给出具体分数和理由
    - **40分保留线**：总分40分以上保留并优化，30-39分重构改进，30分以下重新设计
    - **评分记录完整**：每次评分必须记录详细理由和改进建议
    - **渐进式重评**：重构后必须重新评分验证改进效果
    - **现实价值导向**：评分必须考虑在实际大型项目中的使用价值
  </rule>

  <guideline>
    ## 评分指导原则
    - **客观性优先**：基于代码事实和架构分析，避免主观偏好
    - **现实主义导向**：重点评估在复杂项目环境中的实际效用
    - **清晰精准明确**：评分理由必须具体明确，避免模糊表述
    - **改进导向性**：每个低分项都要提供具体的改进方向
    - **一致性保证**：同类代码使用相同的评分标准
    - **长期价值考虑**：评估代码的长期维护价值和扩展潜力
  </guideline>

  <process>
    ## 代码评分执行流程

    ### Phase 1: 评分维度定义
    ```
    维度1: 功能性 (10分)
    - 9-10分：完全解决目标问题，功能完整可靠
    - 7-8分：基本解决问题，有少量功能缺失
    - 5-6分：部分解决问题，存在明显功能缺陷
    - 3-4分：勉强可用，功能严重不足
    - 1-2分：基本不可用，功能严重缺失
    - 0分：完全无法工作

    维度2: 架构一致性 (10分)
    - 9-10分：完全符合整体架构设计，接口统一
    - 7-8分：基本符合架构，有少量不一致
    - 5-6分：部分符合，存在明显架构偏差
    - 3-4分：架构不一致，但不影响基本功能
    - 1-2分：严重偏离架构设计
    - 0分：完全不符合架构要求

    维度3: 扩展性 (10分)
    - 9-10分：设计优雅，易于扩展和修改
    - 7-8分：基本支持扩展，有少量限制
    - 5-6分：扩展性一般，需要较多修改
    - 3-4分：扩展困难，但可以实现
    - 1-2分：几乎无法扩展
    - 0分：完全无法扩展

    维度4: 性能 (10分)
    - 9-10分：性能优秀，无明显瓶颈
    - 7-8分：性能良好，有轻微优化空间
    - 5-6分：性能一般，存在明显瓶颈
    - 3-4分：性能较差，影响用户体验
    - 1-2分：性能很差，严重影响使用
    - 0分：性能问题导致无法使用

    维度5: 代码质量 (10分)
    - 9-10分：代码清晰精准明确，易于理解和维护
    - 7-8分：代码质量良好，有少量改进空间
    - 5-6分：代码质量一般，存在明显问题
    - 3-4分：代码质量较差，难以维护
    - 1-2分：代码质量很差，严重影响维护
    - 0分：代码完全无法维护
    ```

    ### Phase 2: 模块评分执行
    ```
    1. AssetTree模块评分
       功能性: 评估路径匹配和处理器查找功能
       架构一致性: 评估与整体设计的契合度
       扩展性: 评估新增节点和规则的便利性
       性能: 评估缓存机制和查找效率
       代码质量: 评估代码结构和可读性

    2. AssetProfile模块评分
       功能性: 评估规则筛选和处理器管理功能
       架构一致性: 评估与AssetTree和Filter的协调
       扩展性: 评估新增筛选条件的灵活性
       性能: 评估规则匹配和处理器获取效率
       代码质量: 评估配置管理和接口设计

    3. AssetFilter模块评分
       功能性: 评估资源筛选和类型匹配功能
       架构一致性: 评估与Profile和Processor的集成
       扩展性: 评估新增筛选逻辑的便利性
       性能: 评估匹配算法和缓存策略
       代码质量: 评估逻辑清晰度和维护性

    4. AssetProcessor模块评分
       功能性: 评估具体处理逻辑的完整性
       架构一致性: 评估接口统一性和生命周期管理
       扩展性: 评估新增处理器的便利性
       性能: 评估处理效率和资源占用
       代码质量: 评估代码复用和错误处理

    5. Pipeline模块评分
       功能性: 评估流程编排和上下文管理功能
       架构一致性: 评估与Unity生命周期的集成
       扩展性: 评估新增Pipeline的便利性
       性能: 评估批量处理和状态管理效率
       代码质量: 评估复杂逻辑的清晰度
    ```

    ### Phase 3: 评分结果分析
    ```
    1. 分数统计分析
       - 计算各模块总分和平均分
       - 识别最高分和最低分模块
       - 分析各维度的分布情况
       - 确定重构优先级排序

    2. 问题模式识别
       - 识别共性问题（如性能、扩展性）
       - 分析架构层面的系统性问题
       - 找出影响整体质量的关键因素
       - 制定针对性改进策略

    3. 改进方向确定
       - 40分以上：保留并进行局部优化
       - 30-39分：重构改进，保留核心价值
       - 30分以下：重新设计或考虑删除
       - 制定具体的改进计划和时间安排
    ```

    ### Phase 4: 重构后重评验证
    ```
    1. 重构效果评估
       - 对重构后的模块重新评分
       - 对比重构前后的分数变化
       - 验证改进目标的达成情况
       - 记录意外的副作用或新问题

    2. 整体质量提升验证
       - 计算整体平均分的提升
       - 验证架构一致性的改善
       - 确认性能和扩展性的优化
       - 评估代码质量的整体提升

    3. 持续改进循环
       - 基于新的评分结果制定下一轮改进计划
       - 识别仍需改进的模块和维度
       - 调整重构策略和优先级
       - 建立长期的质量监控机制
    ```
  </process>

  <criteria>
    ## 评分质量标准

    ### 评分准确性
    - ✅ 评分理由具体明确，有充分的代码分析支撑
    - ✅ 分数与实际代码质量高度匹配
    - ✅ 同类代码评分标准一致
    - ✅ 评分结果具有可重现性

    ### 改进指导性
    - ✅ 每个低分项都有具体的改进建议
    - ✅ 改进建议具有可操作性
    - ✅ 优先级排序合理，符合项目实际需求
    - ✅ 改进路径清晰，时间安排合理

    ### 现实价值导向
    - ✅ 评分充分考虑大型项目使用场景
    - ✅ 重点关注实际使用价值而非理论完美
    - ✅ 平衡功能完整性和实现复杂度
    - ✅ 符合"清晰、精准、明确"的核心原则

    ### 重构决策支撑
    - ✅ 评分结果能够有效指导重构决策
    - ✅ 40分保留线设置合理，符合项目需求
    - ✅ 重构后评分验证改进效果明显
    - ✅ 建立了可持续的质量改进机制
  </criteria>
</execution>
