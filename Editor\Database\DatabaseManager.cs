using System;
using System.IO;
using System.Diagnostics;
using UnityEditor;
using UnityEngine;
using Logger = AssetPipeline.Core.Logger;

namespace AssetPipeline.Database
{
    /// <summary>
    /// 数据库管理器 
    /// </summary>
    [InitializeOnLoad]
    public static class DatabaseManager
    {
        #region 核心状态管理
        
        private static readonly object _lockObject = new object();
        private static IDatabaseService _instance;
        private static bool _isInitialized;
        private static bool _isShuttingDown;
        private static int _initializationAttempts = 0;
        private const int MAX_INITIALIZATION_ATTEMPTS = 3;
        private static DateTime _lastInitializationAttempt = DateTime.MinValue;
        private static readonly TimeSpan RETRY_COOLDOWN = TimeSpan.FromSeconds(5);

        /// <summary>
        /// 线程安全的数据库服务实例
        /// </summary>
        public static IDatabaseService Instance
        {
            get
            {
                lock (_lockObject)
                {
                    if (_instance == null && !_isShuttingDown)
                    {
                        // 检查是否需要等待重试冷却
                        if (DateTime.Now - _lastInitializationAttempt > RETRY_COOLDOWN)
                        {
                            Initialize();
                        }
                    }
                    return _instance;
                }
            }
        }

        /// <summary>
        /// 数据库路径配置
        /// </summary>
        private static readonly string DatabasePath = Path.Combine(Application.dataPath, "..", "Library", "AssetPipeline", "AssetPipeline.db");
        
        /// <summary>
        /// 备份数据库路径
        /// </summary>
        private static readonly string BackupDatabasePath = DatabasePath + ".backup";

        #endregion

        #region Unity生命周期集成
        
        static DatabaseManager()
        {
            // 编辑器启动时自动初始化
            EditorApplication.delayCall += () => Initialize();
            
            // 注册Unity生命周期事件
            AssemblyReloadEvents.beforeAssemblyReload += OnBeforeAssemblyReload;
            EditorApplication.quitting += OnEditorQuitting;
            
            Logger.Debug(Core.LogModule.Database, "DatabaseManager静态构造函数完成");
        }

        private static void OnBeforeAssemblyReload()
        {
            Logger.Debug(Core.LogModule.Database, "Assembly重新加载前，正在关闭数据库连接");
            Shutdown();
        }

        private static void OnEditorQuitting()
        {
            Logger.Debug(Core.LogModule.Database, "编辑器退出前，正在关闭数据库连接");
            Shutdown();
        }

        #endregion

        #region 初始化与关闭

        /// <summary>
        /// 企业级数据库初始化 - 带性能监控和智能重试
        /// </summary>
        internal static void Initialize()
        {
            lock (_lockObject)
            {
                if (_isInitialized || _isShuttingDown)
                {
                    return;
                }

                _lastInitializationAttempt = DateTime.Now;
                _initializationAttempts++;
                var stopwatch = Stopwatch.StartNew();
                
                try
                {
                    Logger.Info(Core.LogModule.Database, $"开始数据库初始化 (尝试 {_initializationAttempts}/{MAX_INITIALIZATION_ATTEMPTS})");

                    // 确保目录存在
                    EnsureDatabaseDirectory();

                    // 检查并处理损坏的数据库
                    var integrityCheckPassed = ValidateDatabase();
                    if (!integrityCheckPassed && File.Exists(BackupDatabasePath))
                    {
                        Logger.Warning(Core.LogModule.Database, "主数据库损坏，尝试从备份恢复");
                        RestoreFromBackup();
                    }

                    // 创建数据库服务
                    var service = new SqliteDatabaseService();
                    service.Initialize(DatabasePath);
                    
                    // 创建或更新所有已知表
                    InitializeTables(service);
                    
                    // 创建备份（只在主数据库正常时）
                    if (integrityCheckPassed || ValidateDatabase())
                    {
                        CreateBackup();
                    }

                    _instance = service;
                    _isInitialized = true;
                    _initializationAttempts = 0; // 重置尝试计数
                    
                    stopwatch.Stop();
                    Logger.Info(Core.LogModule.Database, $"数据库服务初始化成功，耗时: {stopwatch.ElapsedMilliseconds}ms");
                }
                catch (Exception e)
                {
                    stopwatch.Stop();
                    Logger.Error(Core.LogModule.Database, $"数据库初始化失败 (尝试 {_initializationAttempts}/{MAX_INITIALIZATION_ATTEMPTS}, 耗时: {stopwatch.ElapsedMilliseconds}ms): {e.Message}");
                    
                    _instance?.Dispose();
                    _instance = null;
                    _isInitialized = false;

                    // 如果超过最大尝试次数，记录详细错误信息
                    if (_initializationAttempts >= MAX_INITIALIZATION_ATTEMPTS)
                    {
                        Logger.Error(Core.LogModule.Database, $"数据库初始化彻底失败，已达到最大重试次数。错误详情: {e}");
                        LogDatabaseDiagnostics();
                        return;
                    }

                    // 尝试删除损坏的数据库文件，为下次重试做准备
                    TryDeleteCorruptedDatabase();
                }
            }
        }

        /// <summary>
        /// 优雅关闭数据库服务
        /// </summary>
        private static void Shutdown()
        {
            lock (_lockObject)
            {
                _isShuttingDown = true;
                var stopwatch = Stopwatch.StartNew();
                
                try
                {
                    _instance?.Dispose();
                    stopwatch.Stop();
                    Logger.Info(Core.LogModule.Database, $"数据库服务已关闭，耗时: {stopwatch.ElapsedMilliseconds}ms");
                }
                catch (Exception e)
                {
                    stopwatch.Stop();
                    Logger.Error(Core.LogModule.Database, $"数据库关闭时发生错误 (耗时: {stopwatch.ElapsedMilliseconds}ms): {e.Message}");
                }
                finally
                {
                    _instance = null;
                    _isInitialized = false;
                    
                    // 清理事件订阅
                    AssemblyReloadEvents.beforeAssemblyReload -= OnBeforeAssemblyReload;
                    EditorApplication.quitting -= OnEditorQuitting;
                }
            }
        }

        #endregion

        #region 数据库维护工具

        /// <summary>
        /// 确保数据库目录存在
        /// </summary>
        private static void EnsureDatabaseDirectory()
        {
            var directory = Path.GetDirectoryName(DatabasePath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
                Logger.Debug(Core.LogModule.Database, $"创建数据库目录: {directory}");
            }
        }

        /// <summary>
        /// 增强的数据库完整性验证
        /// </summary>
        private static bool ValidateDatabase()
        {
            if (!File.Exists(DatabasePath))
            {
                Logger.Debug(Core.LogModule.Database, "数据库文件不存在，将创建新数据库");
                return true; // 新数据库，无需验证
            }

            try
            {
                var fileInfo = new FileInfo(DatabasePath);
                if (fileInfo.Length == 0)
                {
                    Logger.Warning(Core.LogModule.Database, "数据库文件为空，需要重新创建");
                    return false;
                }

                using (var testService = new SqliteDatabaseService())
                {
                    testService.Initialize(DatabasePath);
                    
                    // 执行完整性检查
                    var integrityResult = testService.Execute("PRAGMA integrity_check");
                    Logger.Debug(Core.LogModule.Database, "数据库完整性检查通过");
                    return true;
                }
            }
            catch (Exception e)
            {
                Logger.Warning(Core.LogModule.Database, $"数据库验证失败: {e.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从备份恢复数据库
        /// </summary>
        private static void RestoreFromBackup()
        {
            try
            {
                if (!File.Exists(BackupDatabasePath))
                {
                    Logger.Error(Core.LogModule.Database, "备份文件不存在，无法恢复");
                    return;
                }

                // 验证备份文件完整性
                var backupInfo = new FileInfo(BackupDatabasePath);
                if (backupInfo.Length == 0)
                {
                    Logger.Error(Core.LogModule.Database, "备份文件为空，无法恢复");
                    return;
                }

                if (File.Exists(DatabasePath))
                {
                    // 保存损坏的数据库文件作为调试信息
                    var corruptedPath = DatabasePath + ".corrupted." + DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    File.Move(DatabasePath, corruptedPath);
                    Logger.Debug(Core.LogModule.Database, $"损坏的数据库已保存为: {corruptedPath}");
                }
                
                File.Copy(BackupDatabasePath, DatabasePath);
                Logger.Info(Core.LogModule.Database, "从备份成功恢复数据库");
            }
            catch (Exception e)
            {
                Logger.Error(Core.LogModule.Database, $"备份恢复失败: {e.Message}");
            }
        }

        /// <summary>
        /// 创建数据库备份
        /// </summary>
        private static void CreateBackup()
        {
            try
            {
                if (!File.Exists(DatabasePath))
                {
                    Logger.Debug(Core.LogModule.Database, "数据库文件不存在，跳过备份创建");
                    return;
                }

                // 确保主数据库不为空
                var dbInfo = new FileInfo(DatabasePath);
                if (dbInfo.Length == 0)
                {
                    Logger.Warning(Core.LogModule.Database, "主数据库为空，跳过备份创建");
                    return;
                }

                File.Copy(DatabasePath, BackupDatabasePath, true);
                Logger.Debug(Core.LogModule.Database, $"数据库备份创建成功，大小: {dbInfo.Length / 1024}KB");
            }
            catch (Exception e)
            {
                Logger.Warning(Core.LogModule.Database, $"创建备份失败: {e.Message}");
            }
        }

        /// <summary>
        /// 尝试删除损坏的数据库文件
        /// </summary>
        private static void TryDeleteCorruptedDatabase()
        {
            try
            {
                if (File.Exists(DatabasePath))
                {
                    // 先尝试备份损坏的文件
                    var corruptedPath = DatabasePath + ".corrupted." + DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    try
                    {
                        File.Move(DatabasePath, corruptedPath);
                        Logger.Info(Core.LogModule.Database, $"损坏的数据库已移动到: {corruptedPath}");
                    }
                    catch
                    {
                        File.Delete(DatabasePath);
                        Logger.Info(Core.LogModule.Database, "已删除损坏的数据库文件");
                    }
                }
            }
            catch (Exception e)
            {
                Logger.Warning(Core.LogModule.Database, $"删除损坏数据库失败: {e.Message}");
            }
        }

        /// <summary>
        /// 初始化所有数据表
        /// </summary>
        private static void InitializeTables(IDatabaseService service)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                service.CreateOrUpdateTable<Models.AssetInfo>();
                service.CreateOrUpdateTable<Models.AssetDependency>();
                service.CreateOrUpdateTable<Models.GUIDHistoryRecord>();
                service.CreateOrUpdateTable<Models.TextureAuditInfo>();
                service.CreateOrUpdateTable<Models.OrphanAsset>();
                service.CreateOrUpdateTable<Models.DuplicateAssetGroup>();
                service.CreateOrUpdateTable<Models.MaterialFingerprint>();
                service.CreateOrUpdateTable<Models.AssetReference>();
                service.CreateOrUpdateTable<Models.DatabaseStatistics>();
                
                stopwatch.Stop();
                Logger.Debug(Core.LogModule.Database, $"所有数据表初始化完成，耗时: {stopwatch.ElapsedMilliseconds}ms");
            }
            catch (Exception e)
            {
                stopwatch.Stop();
                Logger.Error(Core.LogModule.Database, $"数据表初始化失败 (耗时: {stopwatch.ElapsedMilliseconds}ms): {e.Message}");
                throw;
            }
        }

        #endregion

        #region 诊断工具

        /// <summary>
        /// 获取数据库健康状态 - 增强版
        /// </summary>
        public static bool IsHealthy()
        {
            lock (_lockObject)
            {
                if (!_isInitialized || _instance == null || _isShuttingDown)
                    return false;

                try
                {
                    // 尝试执行简单查询验证连接
                    _instance.Execute("SELECT 1");
                    return true;
                }
                catch
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// 获取详细的数据库状态信息
        /// </summary>
        public static string GetDatabaseStatus()
        {
            lock (_lockObject)
            {
                var status = new System.Text.StringBuilder();
                status.AppendLine("=== 数据库状态报告 ===");
                status.AppendLine($"初始化状态: {(_isInitialized ? "已初始化" : "未初始化")}");
                status.AppendLine($"实例状态: {(_instance != null ? "存在" : "不存在")}");
                status.AppendLine($"关闭状态: {(_isShuttingDown ? "正在关闭" : "正常运行")}");
                status.AppendLine($"初始化尝试次数: {_initializationAttempts}");
                status.AppendLine($"数据库文件路径: {DatabasePath}");
                status.AppendLine($"数据库文件存在: {File.Exists(DatabasePath)}");
                
                if (File.Exists(DatabasePath))
                {
                    var fileInfo = new FileInfo(DatabasePath);
                    status.AppendLine($"数据库文件大小: {fileInfo.Length / 1024}KB");
                    status.AppendLine($"最后修改时间: {fileInfo.LastWriteTime}");
                }
                
                status.AppendLine($"备份文件存在: {File.Exists(BackupDatabasePath)}");
                status.AppendLine($"健康检查: {(IsHealthy() ? "通过" : "失败")}");
                
                return status.ToString();
            }
        }

        /// <summary>
        /// 记录数据库诊断信息
        /// </summary>
        private static void LogDatabaseDiagnostics()
        {
            Logger.Info(Core.LogModule.Database, GetDatabaseStatus());
        }

        /// <summary>
        /// 强制重新初始化数据库 - 增强版
        /// </summary>
        public static void ForceReinitialize()
        {
            lock (_lockObject)
            {
                Logger.Info(Core.LogModule.Database, "强制重新初始化数据库");
                Shutdown();
                _isShuttingDown = false;
                _initializationAttempts = 0;
                _lastInitializationAttempt = DateTime.MinValue; // 重置冷却时间
                Initialize();
            }
        }

        #endregion
    }
} 