using System.IO;
using System.Linq;
using UnityEngine;
using UnityEditor;
using UnityEditor.Presets;
using AssetPipeline.Processors;

namespace AssetPipeline.Tools
{
    /// <summary>
    /// Preset处理器配置向导 - 重新设计版本
    /// 现实场景：帮助团队快速配置基于Unity原生体验的Preset处理器
    /// </summary>
    public class PresetProcessorSetupWizard : ScriptableWizard
    {
        #region 配置选项

        [Header("处理器配置")]
        [SerializeField] private PresetProcessor.ProcessingMode defaultMode = PresetProcessor.ProcessingMode.PresetMode;
        [SerializeField] private bool enableSelectiveRestriction = false;
        [SerializeField] private bool enableDetailedLogging = true;

        [Header("目标路径")]
        [SerializeField] private string processorFolder = "Assets/Editor/AssetPipeline/Processors";
        [SerializeField] private string presetFolder = "Assets/Editor/AssetPipeline/Presets";

        [Header("快速配置模板")]
        [SerializeField] private bool createTextureProcessor = true;
        [SerializeField] private bool createModelProcessor = true;
        [SerializeField] private bool createAudioProcessor = false;

        #endregion

        #region 向导入口

        [MenuItem("Tools/Asset Pipeline/Setup Preset Processors")]
        public static void ShowWizard()
        {
            DisplayWizard<PresetProcessorSetupWizard>("Preset处理器配置向导", "创建处理器", "取消");
        }

        #endregion

        #region 向导逻辑

        void OnWizardCreate()
        {
            try
            {
                // 确保目标文件夹存在
                EnsureDirectoryExists(processorFolder);
                EnsureDirectoryExists(presetFolder);

                int createdCount = 0;

                // 创建贴图处理器
                if (createTextureProcessor)
                {
                    CreateUniversalProcessor("Texture", typeof(TextureImporter));
                    createdCount++;
                }

                // 创建模型处理器
                if (createModelProcessor)
                {
                    CreateUniversalProcessor("Model", typeof(ModelImporter));
                    createdCount++;
                }

                // 创建音频处理器
                if (createAudioProcessor)
                {
                    CreateUniversalProcessor("Audio", typeof(AudioImporter));
                    createdCount++;
                }

                // 刷新资产数据库
                AssetDatabase.Refresh();

                EditorUtility.DisplayDialog("成功",
                    $"已创建 {createdCount} 个Preset处理器！\n\n" +
                    "接下来请：\n" +
                    "1. 为每个处理器配置对应的Preset资产\n" +
                    "2. 根据需要启用选择性属性限制\n" +
                    "3. 将处理器添加到AssetProfile中", "确定");
            }
            catch (System.Exception ex)
            {
                EditorUtility.DisplayDialog("错误", $"创建处理器时发生错误：{ex.Message}", "确定");
            }
        }

        void OnWizardUpdate()
        {
            // 验证输入
            helpString = ValidateInput();
            isValid = string.IsNullOrEmpty(helpString);
        }

        #endregion

        #region 处理器创建

        /// <summary>
        /// 创建Preset处理器
        /// </summary>
        private void CreateUniversalProcessor(string typeName, System.Type importerType)
        {
            var processor = ScriptableObject.CreateInstance<PresetProcessor>();
            
            // 使用反射设置私有字段
            SetPrivateField(processor, "processingMode", defaultMode);
            SetPrivateField(processor, "enableSelectiveRestriction", enableSelectiveRestriction);
            SetPrivateField(processor, "enableDetailedLogging", enableDetailedLogging);
            SetPrivateField(processor, "preserveUserData", true);

            // 尝试创建对应的Preset
            var preset = CreateDefaultPresetForType(importerType);
            if (preset != null)
            {
                // 保存Preset
                var presetPath = Path.Combine(presetFolder, $"Default{typeName}Preset.preset");
                AssetDatabase.CreateAsset(preset, presetPath);
                
                // 设置处理器的Preset引用
                SetPrivateField(processor, "targetPreset", preset);
            }

            // 保存处理器
            var processorPath = Path.Combine(processorFolder, $"{typeName}PresetProcessor.asset");
            AssetDatabase.CreateAsset(processor, processorPath);

            Debug.Log($"已创建{typeName}处理器: {processorPath}");
        }

        /// <summary>
        /// 为指定类型创建默认Preset
        /// </summary>
        private Preset CreateDefaultPresetForType(System.Type importerType)
        {
            try
            {
                // 查找项目中对应类型的资产
                var sampleAssetPath = FindSampleAssetForImporterType(importerType);
                if (string.IsNullOrEmpty(sampleAssetPath))
                {
                    Debug.LogWarning($"未找到 {importerType.Name} 类型的示例资产，无法创建默认Preset");
                    return null;
                }

                var importer = AssetImporter.GetAtPath(sampleAssetPath);
                if (importer != null && importer.GetType() == importerType)
                {
                    // 配置默认设置
                    ConfigureDefaultImporterSettings(importer);
                    
                    // 创建Preset
                    return new Preset(importer);
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"创建默认Preset失败: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// 查找指定导入器类型的示例资产
        /// </summary>
        private string FindSampleAssetForImporterType(System.Type importerType)
        {
            string[] searchPatterns = null;

            if (importerType == typeof(TextureImporter))
            {
                searchPatterns = new[] { "*.png", "*.jpg", "*.tga" };
            }
            else if (importerType == typeof(ModelImporter))
            {
                searchPatterns = new[] { "*.fbx", "*.obj", "*.dae" };
            }
            else if (importerType == typeof(AudioImporter))
            {
                searchPatterns = new[] { "*.wav", "*.mp3", "*.ogg" };
            }

            if (searchPatterns != null)
            {
                foreach (var pattern in searchPatterns)
                {
                    var assets = AssetDatabase.FindAssets($"t:{GetAssetTypeForPattern(pattern)}")
                        .Select(AssetDatabase.GUIDToAssetPath)
                        .Where(path => path.StartsWith("Assets/"))
                        .FirstOrDefault();

                    if (!string.IsNullOrEmpty(assets))
                        return assets;
                }
            }

            return null;
        }

        /// <summary>
        /// 根据文件模式获取资产类型
        /// </summary>
        private string GetAssetTypeForPattern(string pattern)
        {
            if (pattern.Contains("png") || pattern.Contains("jpg") || pattern.Contains("tga"))
                return "Texture2D";
            if (pattern.Contains("fbx") || pattern.Contains("obj") || pattern.Contains("dae"))
                return "GameObject";
            if (pattern.Contains("wav") || pattern.Contains("mp3") || pattern.Contains("ogg"))
                return "AudioClip";
            
            return "Object";
        }

        /// <summary>
        /// 配置默认导入器设置
        /// </summary>
        private void ConfigureDefaultImporterSettings(AssetImporter importer)
        {
            switch (importer)
            {
                case TextureImporter textureImporter:
                    ConfigureDefaultTextureSettings(textureImporter);
                    break;
                case ModelImporter modelImporter:
                    ConfigureDefaultModelSettings(modelImporter);
                    break;
                case AudioImporter audioImporter:
                    ConfigureDefaultAudioSettings(audioImporter);
                    break;
            }
        }

        /// <summary>
        /// 配置默认贴图设置
        /// </summary>
        private void ConfigureDefaultTextureSettings(TextureImporter importer)
        {
            importer.textureType = TextureImporterType.Default;
            importer.sRGBTexture = true;
            importer.isReadable = false;
            importer.textureCompression = TextureImporterCompression.Compressed;

            var platformSettings = importer.GetDefaultPlatformTextureSettings();
            platformSettings.maxTextureSize = 2048;
            platformSettings.format = TextureImporterFormat.Automatic;
            importer.SetPlatformTextureSettings(platformSettings);
        }

        /// <summary>
        /// 配置默认模型设置
        /// </summary>
        private void ConfigureDefaultModelSettings(ModelImporter importer)
        {
            importer.isReadable = false;
            importer.optimizeMesh = true;
            importer.importMaterials = true;
            importer.importAnimation = true;
            importer.meshCompression = ModelImporterMeshCompression.Off;
        }

        /// <summary>
        /// 配置默认音频设置
        /// </summary>
        private void ConfigureDefaultAudioSettings(AudioImporter importer)
        {
            var settings = importer.defaultSampleSettings;
            settings.loadType = AudioClipLoadType.CompressedInMemory;
            settings.compressionFormat = AudioCompressionFormat.Vorbis;
            settings.quality = 0.7f;
            importer.defaultSampleSettings = settings;
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 确保目录存在
        /// </summary>
        private void EnsureDirectoryExists(string path)
        {
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
        }

        /// <summary>
        /// 使用反射设置私有字段
        /// </summary>
        private void SetPrivateField(object obj, string fieldName, object value)
        {
            var field = obj.GetType().GetField(fieldName, 
                System.Reflection.BindingFlags.NonPublic | 
                System.Reflection.BindingFlags.Instance);
            
            if (field != null)
            {
                field.SetValue(obj, value);
            }
            else
            {
                Debug.LogWarning($"未找到字段: {fieldName}");
            }
        }

        /// <summary>
        /// 验证输入参数
        /// </summary>
        private string ValidateInput()
        {
            if (!createTextureProcessor && !createModelProcessor && !createAudioProcessor)
            {
                return "至少选择一种处理器类型";
            }

            if (string.IsNullOrEmpty(processorFolder))
            {
                return "请指定处理器文件夹";
            }

            if (string.IsNullOrEmpty(presetFolder))
            {
                return "请指定Preset文件夹";
            }

            return "";
        }

        #endregion
    }
}
