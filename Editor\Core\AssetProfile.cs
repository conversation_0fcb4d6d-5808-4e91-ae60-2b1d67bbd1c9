using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using UnityEngine;
using AssetPipeline.Config;
using AssetPipeline.Processors;
using AssetPipeline.UI;
using UnityEditor;

namespace AssetPipeline.Core
{
    /// <summary>
    /// 资源配置文件，负责管理某个路径下的资源筛选规则
    /// 包含多个筛选器，用于匹配不同类型的资源
    /// </summary>
    [System.Serializable]
    public class AssetProfile : ScriptableObject
    {
        #region 基本信息

        [SerializeField] private string guid = System.Guid.NewGuid().ToString();
        public string GUID => guid;
        
        [SerializeField] private string displayName = "New Asset Profile";
        public string DisplayName { get => displayName; set => displayName = value; }
        
        [SerializeField] private string description = "";
        public string Description { get => description; set => description = value; }
        
        [SerializeField] private bool enabled = true;
        public bool Enabled { get => enabled; set => enabled = value; }
        
        [SerializeField] private int priority = 100;
        public int Priority { get => priority; set => priority = value; }

        #endregion

        #region 路径匹配配置

        [Tooltip("此Profile匹配的目录前缀")]
        [SerializeField] private string pathPrefix = "Assets/";
        public string PathPrefix { get => pathPrefix; set => pathPrefix = value; }
        
        #endregion

        #region 筛选器配置

        [SerializeField] private List<AssetFilter> filters = new List<AssetFilter>();

        public List<AssetFilter> Filters
        {
            get => filters;
            set => filters = value;
        }


        // 预计算缓存
        [NonSerialized] private Dictionary<int, List<AssetFilter>> _layeredFilters;
        [NonSerialized] private int[] _sortedLayers;
        [NonSerialized] private bool _layerCacheDirty = true;

        #endregion

        #region 构造函数和资源管理

        public static AssetProfile Create(string filename)
        {
            var instance = CreateInstance<AssetProfile>();
            instance.name = filename;
            var assetPath = GenerateProfileAssetPath(filename);
            AssetDatabase.CreateAsset(instance, assetPath);
            return instance;
        }

        public static string GenerateProfileAssetPath(string filename)
        {
            var path = Path.Combine(AssetPipelineConfig.PROFILE_FOLDER, filename).Replace(@"\", "/");
            if (Path.GetExtension(path) != ".asset")
            {
                path += ".asset";
            }

            var folderPath = Path.GetDirectoryName(path);
            if (!Directory.Exists(folderPath))
            {
                Directory.CreateDirectory(folderPath);
            }

            path = AssetDatabase.GenerateUniqueAssetPath(path);
            return path;
        }

        public bool Rename(string newName)
        {
            if (string.IsNullOrEmpty(newName) || newName == name) 
                return true;
            
            // 清理文件名，移除无效字符
            var cleanName = newName.Trim();
            var invalidChars = Path.GetInvalidFileNameChars();
            foreach (var c in invalidChars)
            {
                cleanName = cleanName.Replace(c, '_');
            }
            
            // 确保不包含路径分隔符
            cleanName = cleanName.Replace('/', '_').Replace('\\', '_');
            
            if (string.IsNullOrEmpty(cleanName))
            {
                Debug.LogError($"[AssetProfile] 重命名失败: 新名称无效 '{newName}'");
                return false;
            }
            
            var assetPath = AssetDatabase.GetAssetPath(this);
            if (string.IsNullOrEmpty(assetPath))
            {
                Debug.LogError($"[AssetProfile] 重命名失败: 无法获取资产路径");
                return false;
            }
            
            var error = AssetDatabase.RenameAsset(assetPath, cleanName);
            if (string.IsNullOrEmpty(error))
            {
                name = cleanName;
                EditorUtility.SetDirty(this);
                AssetDatabase.SaveAssets();
                return true;
            }
            else
            {
                Debug.LogError($"[AssetProfile] 重命名失败: {error}");
                return false;
            }
        }
       
        #endregion

        #region 核心匹配API

        /// <summary>
        /// 检查资源是否匹配此配置文件的路径规则
        /// </summary>
        public bool IsMatch(string assetPath)
        {
            if (!enabled || string.IsNullOrEmpty(assetPath) || string.IsNullOrEmpty(pathPrefix))
                return false;
            
            var normalizedAssetPath = assetPath.Replace('\\', '/');
            return normalizedAssetPath.StartsWith(pathPrefix, StringComparison.OrdinalIgnoreCase);
        }
        
        /// <summary>
        /// 将匹配的处理器添加到结果列表中 - 使用分层预计算优化
        /// </summary>
        public void GetMatchingProcessors(string assetPath, List<AssetProcessor> results)
        {
            if (!enabled || results == null) return;

            EnsureLayerCacheUpdated();
            if (_layeredFilters.Count == 0) return;

            // 优化：使用栈分配的临时变量，避免List分配
            AssetFilter[] matchingFiltersBuffer = new AssetFilter[8]; // 栈分配缓冲区
            int matchingCount = 0;

            foreach (var layer in _sortedLayers)
            {
                if (!_layeredFilters.TryGetValue(layer, out var filtersInLayer)) continue;

                matchingCount = 0;
                var maxPriority = int.MinValue;

                // 1. 收集本层匹配的筛选器，同时找出最高优先级
                foreach (var filter in filtersInLayer)
                {
                    if (filter.IsMatch(assetPath))
                    {
                        if (filter.Priority > maxPriority)
                        {
                            maxPriority = filter.Priority;
                            matchingCount = 1;
                            matchingFiltersBuffer[0] = filter;
                        }
                        else if (filter.Priority == maxPriority)
                        {
                            // 扩展缓冲区如果需要
                            if (matchingCount >= matchingFiltersBuffer.Length)
                            {
                                var newBuffer = new AssetFilter[matchingFiltersBuffer.Length * 2];
                                Array.Copy(matchingFiltersBuffer, newBuffer, matchingCount);
                                matchingFiltersBuffer = newBuffer;
                            }
                            matchingFiltersBuffer[matchingCount++] = filter;
                        }
                    }
                }

                // 2. 应用本层最高优先级的筛选器
                for (int i = 0; i < matchingCount; i++)
                {
                    matchingFiltersBuffer[i].GetMatchingProcessors(assetPath, results);
                }
            }
        }

        /// <summary>
        /// 确保分层缓存是最新的 - 分层预计算优化
        /// </summary>
        private void EnsureLayerCacheUpdated()
        {
            if (!_layerCacheDirty && _layeredFilters != null) return;

            // 预计算分层结构
            _layeredFilters = new Dictionary<int, List<AssetFilter>>();

            // 优化：直接遍历，避免LINQ Where的开销
            foreach (var filter in filters)
            {
                if (filter != null && filter.Enabled)
                {
                    if (!_layeredFilters.TryGetValue(filter.Layer, out var layerList))
                    {
                        layerList = new List<AssetFilter>(4); // 预分配容量
                        _layeredFilters[filter.Layer] = layerList;
                    }
                    layerList.Add(filter);
                }
            }

            // 预计算排序的层级数组 - 避免LINQ OrderBy
            var layerKeys = new List<int>(_layeredFilters.Keys);
            layerKeys.Sort();
            _sortedLayers = layerKeys.ToArray();

            // 每层内部按优先级降序排序，减少运行时比较
            foreach (var layerList in _layeredFilters.Values)
            {
                if (layerList.Count > 1)
                {
                    layerList.Sort((a, b) => b.Priority.CompareTo(a.Priority));
                }
            }

            _layerCacheDirty = false;

            Logger.Debug(LogModule.Core,
                $"[AssetProfile] 分层缓存已更新 - {DisplayName}: {_layeredFilters.Count}层, {filters.Count}个筛选器");
        }

        /// <summary>
        /// 清除所有缓存 - 配置变化时调用
        /// </summary>
        public void ClearCache()
        {
            _layerCacheDirty = true;
            _layeredFilters?.Clear();
            _layeredFilters = null;
            _sortedLayers = null;

            // 同时清理所有筛选器的缓存
            if (filters != null)
            {
                foreach (var filter in filters)
                {
                    filter?.ClearCache();
                }
            }
        }

        /// <summary>
        /// 获取指定类型的匹配处理器
        /// </summary>
        public List<T> GetMatchingProcessors<T>(string assetPath) where T : class, IProcessor
        {
            var results = new List<AssetProcessor>();
            GetMatchingProcessors(assetPath, results);
            return results.OfType<T>().ToList();
        }

        /// <summary>
        /// 检查是否包含指定类型的处理器 - 使用分层预计算优化
        /// </summary>
        public bool HasProcessor<T>(string assetPath) where T : class, IProcessor
        {
            if (!enabled) return false;

            EnsureLayerCacheUpdated();
            if (_layeredFilters.Count == 0) return false;

            // 使用预计算的分层结构，早期退出优化
            foreach (var layer in _sortedLayers)
            {
                if (!_layeredFilters.TryGetValue(layer, out var filtersInLayer)) continue;

                var maxPriority = int.MinValue;
                var hasMatchingFilter = false;

                // 1. 先找出本层最高优先级（利用预排序）
                foreach (var filter in filtersInLayer)
                {
                    if (filter.IsMatch(assetPath))
                    {
                        if (!hasMatchingFilter || filter.Priority > maxPriority)
                        {
                            maxPriority = filter.Priority;
                            hasMatchingFilter = true;
                        }
                        else if (filter.Priority < maxPriority)
                        {
                            break; // 已预排序，后续都是较低优先级
                        }
                    }
                }

                if (!hasMatchingFilter) continue;

                // 2. 检查最高优先级的筛选器是否包含目标处理器
                foreach (var filter in filtersInLayer)
                {
                    if (filter.Priority == maxPriority && filter.IsMatch(assetPath))
                    {
                        if (filter.HasProcessor<T>(assetPath))
                        {
                            return true; // 早期退出
                        }
                    }
                    else if (filter.Priority < maxPriority)
                    {
                        break; // 已预排序，跳出本层
                    }
                }
            }
            
            return false;
        }

        #endregion

        #region Unity生命周期事件

        void OnValidate()
        {
            // 自动规范化前缀和验证通配符
            if (!string.IsNullOrEmpty(pathPrefix))
            {
                var normalizedPath = pathPrefix.Replace('\\', '/');

                // 验证通配符模式
                if (!ProfilePathTrie.IsValidPathPattern(normalizedPath))
                {
                    Debug.LogWarning($"[AssetProfile] 无效的路径模式: {normalizedPath}，通配符使用不正确");
                }

                // 只有非通配符路径才自动添加结尾斜杠
                if (!normalizedPath.Contains("*") && !normalizedPath.EndsWith("/"))
                {
                    normalizedPath += "/";
                }

                if (pathPrefix != normalizedPath)
                {
                    pathPrefix = normalizedPath;
                    EditorUtility.SetDirty(this);
                }
            }

            // 清理无效的筛选器
            bool hasFilterChanges = false;
            for (int i = filters.Count - 1; i >= 0; i--)
            {
                if (filters[i] == null)
                {
                    filters.RemoveAt(i);
                    hasFilterChanges = true;
                }
            }

            if (hasFilterChanges)
            {
                _layerCacheDirty = true;
                EditorUtility.SetDirty(this);
            }
        }

        #endregion

        public List<AssetFilter> GetMatchingFilters(string assetPath)
        {
            return filters.Where(f => f.IsMatch(assetPath)).ToList();
        }
    }
} 
