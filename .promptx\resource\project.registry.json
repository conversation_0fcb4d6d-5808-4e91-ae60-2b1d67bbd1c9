{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-06-19T09:31:17.073Z", "updatedAt": "2025-06-19T09:31:17.074Z", "resourceCount": 14}, "resources": [{"id": "unity-asset-pipeline-architect", "source": "project", "protocol": "role", "name": "Unity Asset Pipeline Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/unity-asset-pipeline-architect/unity-asset-pipeline-architect.role.md", "metadata": {"createdAt": "2025-06-19T09:31:17.073Z", "updatedAt": "2025-06-19T09:31:17.073Z", "scannedAt": "2025-06-19T09:31:17.073Z"}}, {"id": "unity-asset-pipeline-thinking", "source": "project", "protocol": "thought", "name": "Unity Asset Pipeline Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/unity-asset-pipeline-architect/thought/unity-asset-pipeline-thinking.thought.md", "metadata": {"createdAt": "2025-06-19T09:31:17.074Z", "updatedAt": "2025-06-19T09:31:17.074Z", "scannedAt": "2025-06-19T09:31:17.074Z"}}, {"id": "asset-pipeline-refactoring", "source": "project", "protocol": "execution", "name": "Asset Pipeline Refactoring 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/unity-asset-pipeline-architect/execution/asset-pipeline-refactoring.execution.md", "metadata": {"createdAt": "2025-06-19T09:31:17.074Z", "updatedAt": "2025-06-19T09:31:17.074Z", "scannedAt": "2025-06-19T09:31:17.074Z"}}, {"id": "code-evaluation-scoring", "source": "project", "protocol": "execution", "name": "Code Evaluation Scoring 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/unity-asset-pipeline-architect/execution/code-evaluation-scoring.execution.md", "metadata": {"createdAt": "2025-06-19T09:31:17.074Z", "updatedAt": "2025-06-19T09:31:17.074Z", "scannedAt": "2025-06-19T09:31:17.074Z"}}, {"id": "enterprise-architecture-patterns", "source": "project", "protocol": "knowledge", "name": "Enterprise Architecture Patterns 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/unity-asset-pipeline-architect/knowledge/enterprise-architecture-patterns.knowledge.md", "metadata": {"createdAt": "2025-06-19T09:31:17.074Z", "updatedAt": "2025-06-19T09:31:17.074Z", "scannedAt": "2025-06-19T09:31:17.074Z"}}, {"id": "unity-asset-pipeline-expertise", "source": "project", "protocol": "knowledge", "name": "Unity Asset Pipeline Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/unity-asset-pipeline-architect/knowledge/unity-asset-pipeline-expertise.knowledge.md", "metadata": {"createdAt": "2025-06-19T09:31:17.074Z", "updatedAt": "2025-06-19T09:31:17.074Z", "scannedAt": "2025-06-19T09:31:17.074Z"}}, {"id": "unity-editor-ui-architect", "source": "project", "protocol": "role", "name": "Unity Editor Ui Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/unity-editor-ui-architect/unity-editor-ui-architect.role.md", "metadata": {"createdAt": "2025-06-19T09:31:17.074Z", "updatedAt": "2025-06-19T09:31:17.074Z", "scannedAt": "2025-06-19T09:31:17.074Z"}}, {"id": "artist-workflow-understanding", "source": "project", "protocol": "thought", "name": "Artist Workflow Understanding 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/unity-editor-ui-architect/thought/artist-workflow-understanding.thought.md", "metadata": {"createdAt": "2025-06-19T09:31:17.074Z", "updatedAt": "2025-06-19T09:31:17.074Z", "scannedAt": "2025-06-19T09:31:17.074Z"}}, {"id": "unity-editor-thinking", "source": "project", "protocol": "thought", "name": "Unity Editor Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/unity-editor-ui-architect/thought/unity-editor-thinking.thought.md", "metadata": {"createdAt": "2025-06-19T09:31:17.074Z", "updatedAt": "2025-06-19T09:31:17.074Z", "scannedAt": "2025-06-19T09:31:17.074Z"}}, {"id": "editor-performance-optimization", "source": "project", "protocol": "execution", "name": "Editor Performance Optimization 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/unity-editor-ui-architect/execution/editor-performance-optimization.execution.md", "metadata": {"createdAt": "2025-06-19T09:31:17.074Z", "updatedAt": "2025-06-19T09:31:17.074Z", "scannedAt": "2025-06-19T09:31:17.074Z"}}, {"id": "unity-ui-design-principles", "source": "project", "protocol": "execution", "name": "Unity Ui Design Principles 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/unity-editor-ui-architect/execution/unity-ui-design-principles.execution.md", "metadata": {"createdAt": "2025-06-19T09:31:17.074Z", "updatedAt": "2025-06-19T09:31:17.074Z", "scannedAt": "2025-06-19T09:31:17.074Z"}}, {"id": "artist-asset-management", "source": "project", "protocol": "knowledge", "name": "Artist Asset Management 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/unity-editor-ui-architect/knowledge/artist-asset-management.knowledge.md", "metadata": {"createdAt": "2025-06-19T09:31:17.074Z", "updatedAt": "2025-06-19T09:31:17.074Z", "scannedAt": "2025-06-19T09:31:17.074Z"}}, {"id": "editor-ui-architecture", "source": "project", "protocol": "knowledge", "name": "Editor Ui Architecture 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/unity-editor-ui-architect/knowledge/editor-ui-architecture.knowledge.md", "metadata": {"createdAt": "2025-06-19T09:31:17.074Z", "updatedAt": "2025-06-19T09:31:17.074Z", "scannedAt": "2025-06-19T09:31:17.074Z"}}, {"id": "unity-editor-development", "source": "project", "protocol": "knowledge", "name": "Unity Editor Development 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/unity-editor-ui-architect/knowledge/unity-editor-development.knowledge.md", "metadata": {"createdAt": "2025-06-19T09:31:17.074Z", "updatedAt": "2025-06-19T09:31:17.074Z", "scannedAt": "2025-06-19T09:31:17.074Z"}}], "stats": {"totalResources": 14, "byProtocol": {"role": 2, "thought": 3, "execution": 4, "knowledge": 5}, "bySource": {"project": 14}}}