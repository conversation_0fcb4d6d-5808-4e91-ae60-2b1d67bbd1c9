using System;
using System.Reflection;
using UnityEngine;
using UnityEditor;
using AssetPipeline.Processors;

namespace AssetPipeline.UI
{
    /// <summary>
    /// Inspector增强器
    /// 在Unity原生Inspector中集成属性锁定功能
    /// </summary>
    [InitializeOnLoad]
    public static class InspectorEnhancer
    {
        #region 静态初始化

        static InspectorEnhancer()
        {
            EditorApplication.update += OnEditorUpdate;
            Selection.selectionChanged += OnSelectionChanged;
        }

        #endregion

        #region 状态管理

        private static bool isEnhancementActive = false;
        private static AssetImporter currentImporter = null;
        private static Editor currentInspector = null;

        /// <summary>
        /// 激活Inspector增强功能
        /// </summary>
        public static void ActivateEnhancement()
        {
            isEnhancementActive = true;
            SafeInspectorManager.EnablePropertyLocking("inspector");
            Debug.Log("[InspectorEnhancer] Inspector增强功能已激活");
        }

        /// <summary>
        /// 停用Inspector增强功能
        /// </summary>
        public static void DeactivateEnhancement()
        {
            isEnhancementActive = false;
            SafeInspectorManager.DisablePropertyLocking("inspector");
            Debug.Log("[InspectorEnhancer] Inspector增强功能已停用");
        }

        /// <summary>
        /// 检查增强功能是否激活
        /// </summary>
        public static bool IsEnhancementActive => isEnhancementActive;

        #endregion

        #region 菜单项

        [MenuItem("Tools/Asset Pipeline/Toggle Inspector Enhancement")]
        public static void ToggleInspectorEnhancement()
        {
            if (isEnhancementActive)
            {
                DeactivateEnhancement();
            }
            else
            {
                ActivateEnhancement();
            }
        }

        [MenuItem("Tools/Asset Pipeline/Toggle Inspector Enhancement", true)]
        public static bool ValidateToggleInspectorEnhancement()
        {
            Menu.SetChecked("Tools/Asset Pipeline/Toggle Inspector Enhancement", isEnhancementActive);
            return true;
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 编辑器更新事件
        /// </summary>
        private static void OnEditorUpdate()
        {
            if (!isEnhancementActive) return;

            // 检查当前选中的对象是否为资产导入器
            CheckCurrentSelection();
            
            // 监控属性变化
            MonitorPropertyChanges();
        }

        /// <summary>
        /// 选择变化事件
        /// </summary>
        private static void OnSelectionChanged()
        {
            if (!isEnhancementActive) return;

            CheckCurrentSelection();
        }

        /// <summary>
        /// 检查当前选择
        /// </summary>
        private static void CheckCurrentSelection()
        {
            var selectedObject = Selection.activeObject;
            if (selectedObject != null)
            {
                var assetPath = AssetDatabase.GetAssetPath(selectedObject);
                if (!string.IsNullOrEmpty(assetPath))
                {
                    var importer = AssetImporter.GetAtPath(assetPath);
                    if (importer != null && importer != currentImporter)
                    {
                        currentImporter = importer;
                        OnImporterChanged();
                    }
                }
            }
            else
            {
                currentImporter = null;
            }
        }

        /// <summary>
        /// 导入器变化处理
        /// </summary>
        private static void OnImporterChanged()
        {
            if (currentImporter == null) return;

            // 验证并恢复锁定属性
            SafeInspectorManager.ValidateAndRestoreLockedProperties(currentImporter, "inspector");
        }

        /// <summary>
        /// 监控属性变化
        /// </summary>
        private static void MonitorPropertyChanges()
        {
            if (currentImporter == null) return;

            // 检查是否有锁定的属性被意外修改
            var lockedProperties = SafeInspectorManager.GetLockedProperties(currentImporter.assetPath, "inspector");
            if (lockedProperties.Count > 0)
            {
                // 验证并恢复锁定属性的值
                SafeInspectorManager.ValidateAndRestoreLockedProperties(currentImporter, "inspector");
            }
        }

        #endregion

        #region Inspector GUI增强

        /// <summary>
        /// 绘制属性锁定指示器
        /// </summary>
        /// <param name="rect">绘制区域</param>
        /// <param name="propertyPath">属性路径</param>
        /// <param name="assetPath">资产路径</param>
        public static void DrawPropertyLockIndicator(Rect rect, string propertyPath, string assetPath)
        {
            if (!isEnhancementActive) return;

            var isLocked = SafeInspectorManager.IsPropertyLocked(assetPath, propertyPath, "inspector");
            
            if (isLocked)
            {
                // 绘制红色边框
                var borderRect = new Rect(rect.x - 2, rect.y - 2, rect.width + 4, rect.height + 4);
                EditorGUI.DrawRect(borderRect, new Color(1, 0, 0, 0.3f));
                
                // 绘制锁定图标
                var iconRect = new Rect(rect.x + rect.width - 20, rect.y, 20, rect.height);
                GUI.Label(iconRect, "🔒");
            }
        }

        /// <summary>
        /// 处理属性右键菜单
        /// </summary>
        /// <param name="propertyPath">属性路径</param>
        /// <param name="assetPath">资产路径</param>
        public static void HandlePropertyContextMenu(string propertyPath, string assetPath)
        {
            if (!isEnhancementActive) return;

            var currentEvent = Event.current;
            if (currentEvent.type == EventType.ContextClick)
            {
                var menu = new GenericMenu();
                
                var isLocked = SafeInspectorManager.IsPropertyLocked(assetPath, propertyPath, "inspector");
                
                if (isLocked)
                {
                    menu.AddItem(new GUIContent("解锁属性"), false, () =>
                    {
                        SafeInspectorManager.UnlockProperty(assetPath, propertyPath, "inspector");
                    });
                }
                else
                {
                    menu.AddItem(new GUIContent("锁定属性"), false, () =>
                    {
                        // 保存当前值作为期望值
                        var importer = AssetImporter.GetAtPath(assetPath);
                        if (importer != null)
                        {
                            var serializedImporter = new SerializedObject(importer);
                            var property = serializedImporter.FindProperty(propertyPath);
                            if (property != null)
                            {
                                var currentValue = GetPropertyValueAsString(property);
                                SafeInspectorManager.SaveExpectedPropertyValue(assetPath, propertyPath, currentValue, "inspector");
                            }
                        }
                        
                        SafeInspectorManager.LockProperty(assetPath, propertyPath, "inspector");
                    });
                }
                
                menu.AddSeparator("");
                menu.AddItem(new GUIContent("清除所有锁定"), false, () =>
                {
                    SafeInspectorManager.ClearAssetLocks(assetPath, "inspector");
                });
                
                menu.ShowAsContext();
                currentEvent.Use();
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取SerializedProperty的字符串值
        /// </summary>
        private static string GetPropertyValueAsString(SerializedProperty property)
        {
            switch (property.propertyType)
            {
                case SerializedPropertyType.Boolean:
                    return property.boolValue ? "1" : "0";
                case SerializedPropertyType.Integer:
                    return property.intValue.ToString();
                case SerializedPropertyType.Float:
                    return property.floatValue.ToString();
                case SerializedPropertyType.String:
                    return property.stringValue;
                case SerializedPropertyType.Enum:
                    return property.enumValueIndex.ToString();
                default:
                    return property.stringValue ?? "";
            }
        }

        /// <summary>
        /// 获取当前活动的Inspector编辑器
        /// </summary>
        private static Editor GetActiveInspectorEditor()
        {
            try
            {
                var inspectorWindowType = typeof(Editor).Assembly.GetType("UnityEditor.InspectorWindow");
                var inspectorWindow = EditorWindow.GetWindow(inspectorWindowType);
                
                if (inspectorWindow != null)
                {
                    var trackerField = inspectorWindowType.GetField("m_Tracker", BindingFlags.NonPublic | BindingFlags.Instance);
                    if (trackerField != null)
                    {
                        var tracker = trackerField.GetValue(inspectorWindow);
                        var activeEditorsProperty = tracker.GetType().GetProperty("activeEditors", BindingFlags.Public | BindingFlags.Instance);
                        
                        if (activeEditorsProperty != null)
                        {
                            var activeEditors = activeEditorsProperty.GetValue(tracker) as Editor[];
                            if (activeEditors != null && activeEditors.Length > 0)
                            {
                                return activeEditors[0];
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"[InspectorEnhancer] 获取Inspector编辑器失败: {ex.Message}");
            }
            
            return null;
        }

        #endregion

        #region 调试功能

        [MenuItem("Tools/Asset Pipeline/Debug Inspector State")]
        public static void DebugInspectorState()
        {
            Debug.Log($"[InspectorEnhancer] 调试信息:");
            Debug.Log($"  增强功能激活: {isEnhancementActive}");
            Debug.Log($"  当前导入器: {(currentImporter != null ? currentImporter.assetPath : "无")}");
            Debug.Log($"  SafeInspectorManager锁定启用: {SafeInspectorManager.IsLockingEnabled}");
            Debug.Log($"  活动作用域: {string.Join(", ", SafeInspectorManager.ActiveScopes)}");
            
            if (currentImporter != null)
            {
                var lockedProperties = SafeInspectorManager.GetLockedProperties(currentImporter.assetPath, "inspector");
                Debug.Log($"  锁定属性数量: {lockedProperties.Count}");
                foreach (var prop in lockedProperties)
                {
                    Debug.Log($"    - {prop}");
                }
            }
        }

        #endregion
    }
}
