using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using L10.Editor.AssetPipeline.Sqlite;
using Logger = AssetPipeline.Core.Logger;

namespace AssetPipeline.Database
{
    /// <summary>
    /// SQLite仓储实现
    /// </summary>
    internal class SqliteRepository<T> : IRepository<T> where T : class, new()
    {
        private readonly SQLiteConnection _connection;
        private readonly object _lock;
        private readonly string _typeName = typeof(T).Name;

        public SqliteRepository(SQLiteConnection connection, object dbLock)
        {
            _connection = connection ?? throw new ArgumentNullException(nameof(connection));
            _lock = dbLock ?? throw new ArgumentNullException(nameof(dbLock));
        }

        public void Add(T entity)
        {
            if (entity == null) throw new ArgumentNullException(nameof(entity));
            
            try
            {
                lock (_lock)
                {
                    _connection.Insert(entity);
                }
            }
            catch (Exception ex)
            {
                Logger.Error(Core.LogModule.Database, $"Failed to add {_typeName}: {ex.Message}");
                throw;
            }
        }

        public void Add(IEnumerable<T> entities)
        {
            if (entities == null) throw new ArgumentNullException(nameof(entities));
            
            var entityList = entities.ToList();
            if (!entityList.Any()) return;

            try
            {
                lock (_lock)
                {
                    _connection.InsertAll(entityList);
                }
                Logger.Debug(Core.LogModule.Database, $"Successfully added {entityList.Count} {_typeName} entities");
            }
            catch (Exception ex)
            {
                Logger.Error(Core.LogModule.Database, $"Failed to batch add {entityList.Count} {_typeName} entities: {ex.Message}");
                throw;
            }
        }

        public void Update(T entity)
        {
            if (entity == null) throw new ArgumentNullException(nameof(entity));
            
            try
            {
                lock (_lock)
                {
                    var rowsAffected = _connection.Update(entity);
                    if (rowsAffected == 0)
                    {
                        Logger.Warning(Core.LogModule.Database, $"Update {_typeName}: No rows affected");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error(Core.LogModule.Database, $"Failed to update {_typeName}: {ex.Message}");
                throw;
            }
        }

        public void Update(IEnumerable<T> entities)
        {
            if (entities == null) throw new ArgumentNullException(nameof(entities));
            
            var entityList = entities.ToList();
            if (!entityList.Any()) return;

            try
            {
                lock (_lock)
                {
                    var rowsAffected = _connection.UpdateAll(entityList);
                    Logger.Debug(Core.LogModule.Database, $"Updated {rowsAffected} {_typeName} entities");
                }
            }
            catch (Exception ex)
            {
                Logger.Error(Core.LogModule.Database, $"Failed to batch update {entityList.Count} {_typeName} entities: {ex.Message}");
                throw;
            }
        }
        
        public void Delete(T entity)
        {
            if (entity == null) throw new ArgumentNullException(nameof(entity));
            
            try
            {
                lock (_lock)
                {
                    var rowsAffected = _connection.Delete(entity);
                    if (rowsAffected == 0)
                    {
                        Logger.Warning(Core.LogModule.Database, $"Delete {_typeName}: No rows affected");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error(Core.LogModule.Database, $"Failed to delete {_typeName}: {ex.Message}");
                throw;
            }
        }
        
        public void Delete(IEnumerable<T> entities)
        {
            if (entities == null) throw new ArgumentNullException(nameof(entities));
            
            var entityList = entities.ToList();
            if (!entityList.Any()) return;

            try
            {
                lock (_lock)
                {
                    // SQLite.cs ORM does not support DeleteAll, so we do it in a single transaction for better performance
                    _connection.RunInTransaction(() =>
                    {
                        foreach (var entity in entityList)
                        {
                            _connection.Delete(entity);
                        }
                    });
                }
                Logger.Debug(Core.LogModule.Database, $"Successfully deleted {entityList.Count} {_typeName} entities");
            }
            catch (Exception ex)
            {
                Logger.Error(Core.LogModule.Database, $"Failed to batch delete {entityList.Count} {_typeName} entities: {ex.Message}");
                throw;
            }
        }
        
        public void Delete(Expression<Func<T, bool>> predicate)
        {
            if (predicate == null) throw new ArgumentNullException(nameof(predicate));
            
            try
            {
                lock (_lock)
                {
                    var query = _connection.Table<T>().Where(predicate);
                    var deleteCount = 0;
                    
                    _connection.RunInTransaction(() =>
                    {
                        // 只在事务内部才ToList()，减少内存占用时间
                        foreach (var item in query.ToList())
                        {
                            _connection.Delete(item);
                            deleteCount++;
                        }
                    });
                    
                    Logger.Debug(Core.LogModule.Database, $"Deleted {deleteCount} {_typeName} entities by predicate");
                }
            }
            catch (Exception ex)
            {
                Logger.Error(Core.LogModule.Database, $"Failed to delete {_typeName} by predicate: {ex.Message}");
                throw;
            }
        }

        public T Get(object primaryKey)
        {
            if (primaryKey == null) throw new ArgumentNullException(nameof(primaryKey));
            
            try
            {
                lock (_lock)
                {
                    return _connection.Find<T>(primaryKey);
                }
            }
            catch (Exception ex)
            {
                Logger.Error(Core.LogModule.Database, $"Failed to get {_typeName} by key '{primaryKey}': {ex.Message}");
                throw;
            }
        }

        public IEnumerable<T> GetAll()
        {
            try
            {
                lock (_lock)
                {
                    return _connection.Table<T>().ToList();
                }
            }
            catch (Exception ex)
            {
                Logger.Error(Core.LogModule.Database, $"Failed to get all {_typeName}: {ex.Message}");
                throw;
            }
        }

        public IEnumerable<T> Find(Expression<Func<T, bool>> predicate)
        {
            if (predicate == null) throw new ArgumentNullException(nameof(predicate));
            
            try
            {
                lock (_lock)
                {
                    return _connection.Table<T>().Where(predicate).ToList();
                }
            }
            catch (Exception ex)
            {
                Logger.Error(Core.LogModule.Database, $"Failed to find {_typeName} by predicate: {ex.Message}");
                throw;
            }
        }

        public T FirstOrDefault(Expression<Func<T, bool>> predicate)
        {
            if (predicate == null) throw new ArgumentNullException(nameof(predicate));
            
            try
            {
                lock (_lock)
                {
                    return _connection.Table<T>().Where(predicate).FirstOrDefault();
                }
            }
            catch (Exception ex)
            {
                Logger.Error(Core.LogModule.Database, $"Failed to get first {_typeName} by predicate: {ex.Message}");
                throw;
            }
        }
        
        public int Count()
        {
            try
            {
                lock (_lock)
                {
                    return _connection.Table<T>().Count();
                }
            }
            catch (Exception ex)
            {
                Logger.Error(Core.LogModule.Database, $"Failed to count {_typeName}: {ex.Message}");
                throw;
            }
        }

        public int Count(Expression<Func<T, bool>> predicate)
        {
            if (predicate == null) throw new ArgumentNullException(nameof(predicate));
            
            try
            {
                lock (_lock)
                {
                    return _connection.Table<T>().Count(predicate);
                }
            }
            catch (Exception ex)
            {
                Logger.Error(Core.LogModule.Database, $"Failed to count {_typeName} by predicate: {ex.Message}");
                throw;
            }
        }

        public bool Exists(Expression<Func<T, bool>> predicate)
        {
            if (predicate == null) throw new ArgumentNullException(nameof(predicate));
            
            try
            {
                lock (_lock)
                {
                    return _connection.Table<T>().Where(predicate).Any();
                }
            }
            catch (Exception ex)
            {
                Logger.Error(Core.LogModule.Database, $"Failed to check existence of {_typeName} by predicate: {ex.Message}");
                throw;
            }
        }
    }
} 