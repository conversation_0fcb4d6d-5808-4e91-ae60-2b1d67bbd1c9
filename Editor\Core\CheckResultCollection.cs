using System;
using System.Collections.Generic;
using System.Linq;
using AssetPipeline.Config;
using UnityEngine;
using System.IO;
using System.Text;

namespace AssetPipeline.Core
{
    /// <summary>
    /// 检查结果集合
    /// 
    /// 处理流程中管理结果的临时辅助类，不是持久化数据
    /// 批量结果管理和聚合功能
    /// </summary>
    public class CheckResultCollection
    {
        #region 核心数据结构

        private readonly List<CheckResult> _results = new List<CheckResult>();
        private readonly Dictionary<string, CheckResult> _deduplicationMap = new Dictionary<string, CheckResult>();

        #endregion

        #region 基础操作
        
        /// <summary>
        /// 结果总数
        /// </summary>
        public int Count => _results.Count;
        public int ErrorCount => _results.Count(r => r.IsError);
        public int WarningCount => _results.Count(r => r.IsWarning);
        public int InfoCount => _results.Count(r => r.IsInfo);
        public int SuccessCount => _results.Count(r => r.IsSuccess);
        
        /// <summary>
        /// 是否为空
        /// </summary>
        public bool IsEmpty => _results.Count == 0;
        
        /// <summary>
        /// 获取所有结果
        /// </summary>
        public IReadOnlyList<CheckResult> Results => _results.AsReadOnly();
        
        /// <summary>
        /// 添加单个结果
        /// </summary>
        public void Add(CheckResult result)
        {
            if (result == null) return;
            
            if (AssetPipelineSettings.EnableResultDeduplication)
            {
                AddWithDeduplication(result);
            }
            else
            {
                _results.Add(result);
            }
        }
        
        /// <summary>
        /// 批量添加结果
        /// </summary>
        public void AddRange(IEnumerable<CheckResult> resultsToAdd)
        {
            if (resultsToAdd == null) return;
            
            foreach (var result in resultsToAdd.Where(r => r != null))
            {
                Add(result);
            }
        }
        
        /// <summary>
        /// 清空所有结果
        /// </summary>
        public void Clear()
        {
            _results.Clear();
            _deduplicationMap.Clear();
        }
        
        #endregion
        
        #region 查询和筛选
        
        /// <summary>
        /// 按类型获取结果
        /// </summary>
        public IEnumerable<CheckResult> GetByType(CheckResultType type)
        {
            return _results.Where(r => r.resultType == type);
        }
        
        /// <summary>
        /// 按处理器名称获取结果
        /// </summary>
        public IEnumerable<CheckResult> GetByProcessor(string processorName)
        {
            if (string.IsNullOrEmpty(processorName)) return Enumerable.Empty<CheckResult>();
            return _results.Where(r => string.Equals(r.processorName, processorName, StringComparison.OrdinalIgnoreCase));
        }
        
        /// <summary>
        /// 按资产路径获取结果
        /// </summary>
        public IEnumerable<CheckResult> GetByAssetPath(string assetPath)
        {
            if (string.IsNullOrEmpty(assetPath)) return Enumerable.Empty<CheckResult>();
            return _results.Where(r => string.Equals(r.assetPath, assetPath, StringComparison.OrdinalIgnoreCase));
        }
        
        /// <summary>
        /// 获取阻断性结果
        /// </summary>
        public IEnumerable<CheckResult> GetBlockingResults(ProcessContext context)
        {
            return _results.Where(r => r.ShouldBlock(context));
        }
        
        #endregion
        
        #region 统计信息
        
        /// <summary>
        /// 获取统计信息
        /// </summary>
        public CheckResultStatistics GetStatistics()
        {
            return new CheckResultStatistics
            {
                TotalCount = _results.Count,
                SuccessCount = SuccessCount,
                ErrorCount = ErrorCount,
                WarningCount = WarningCount,
                InfoCount = InfoCount
            };
        }
        
        /// <summary>
        /// 检查是否有阻断性结果
        /// </summary>
        public bool HasBlockingResults(ProcessContext context)
        {
            return _results.Any(r => r.ShouldBlock(context));
        }
        
        /// <summary>
        /// 检查是否有错误
        /// </summary>
        public bool HasErrors => _results.Any(r => r.IsError);
        
        /// <summary>
        /// 检查是否有警告
        /// </summary>
        public bool HasWarnings => _results.Any(r => r.IsWarning);
        
        /// <summary>
        /// 获取最严重的结果类型
        /// </summary>
        public CheckResultType GetMostSevereType()
        {
            if (HasErrors) return CheckResultType.Error;
            if (HasWarnings) return CheckResultType.Warning;
            if (_results.Any(r => r.IsInfo)) return CheckResultType.Info;
            return CheckResultType.Success;
        }
        
        #endregion
        
        #region 批量操作
        
        /// <summary>
        /// 按类型分组
        /// </summary>
        public Dictionary<CheckResultType, List<CheckResult>> GroupByType()
        {
            return _results.GroupBy(r => r.resultType)
                         .ToDictionary(g => g.Key, g => g.ToList());
        }
        
        /// <summary>
        /// 按处理器分组
        /// </summary>
        public Dictionary<string, List<CheckResult>> GroupByProcessor()
        {
            return _results.GroupBy(r => r.processorName ?? "Unknown")
                         .ToDictionary(g => g.Key, g => g.ToList());
        }
        
        /// <summary>
        /// 按资产路径分组
        /// </summary>
        public Dictionary<string, List<CheckResult>> GroupByAssetPath()
        {
            return _results.GroupBy(r => r.assetPath ?? "Unknown")
                         .ToDictionary(g => g.Key, g => g.ToList());
        }
        
        /// <summary>
        /// 获取摘要报告
        /// </summary>
        public string GetSummaryReport()
        {
            var stats = GetStatistics();
            var sb = new StringBuilder();
            
            sb.AppendLine("检查结果摘要:");
            sb.AppendLine($"  总计: {stats.TotalCount}");
            sb.AppendLine($"  成功: {stats.SuccessCount}");
            sb.AppendLine($"  错误: {stats.ErrorCount}");
            sb.AppendLine($"  警告: {stats.WarningCount}");
            sb.AppendLine($"  信息: {stats.InfoCount}");
            
            return sb.ToString();
        }
        
        #endregion

        #region 导出功能

        /// <summary>
        /// JSON导出包装器
        /// </summary>
        [Serializable]
        private class JsonExportWrapper
        {
            public List<CheckResult> Results;
        }

        /// <summary>
        /// 导出到文件
        /// </summary>
        public void ExportToFile(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            try
            {
                var directory = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                string content;

                switch (extension)
                {
                    case ".json":
                        content = ToJson();
                        break;
                    case ".txt":
                    case ".log":
                        content = ToTextReport();
                        break;
                    default:
                        content = ToTextReport();
                        break;
                }

                File.WriteAllText(filePath, content, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"导出失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 转换为JSON
        /// </summary>
        public string ToJson(bool prettyPrint = true)
        {
            var wrapper = new JsonExportWrapper { Results = _results };
            return JsonUtility.ToJson(wrapper, prettyPrint);
        }

        /// <summary>
        /// 转换为文本报告
        /// </summary>
        public string ToTextReport()
        {
            var sb = new StringBuilder();
            
            sb.AppendLine(GetSummaryReport());
            sb.AppendLine();
            sb.AppendLine("详细结果:");
            
            foreach (var group in GroupByType().OrderBy(g => g.Key))
            {
                if (!group.Value.Any()) continue;
                
                sb.AppendLine($"\n{group.Key} ({group.Value.Count}个):");
                
                foreach (var result in group.Value.OrderBy(r => r.assetPath))
                {
                    sb.AppendLine($"  - {result.message}");
                    if (!string.IsNullOrEmpty(result.assetPath))
                        sb.AppendLine($"    文件: {result.assetPath}");
                    if (!string.IsNullOrEmpty(result.processorName))
                        sb.AppendLine($"    处理器: {result.processorName}");
                }
            }
            
            return sb.ToString();
        }

        #endregion

        #region 内部辅助方法

        /// <summary>
        /// 带去重的添加方法
        /// </summary>
        private void AddWithDeduplication(CheckResult result)
        {
            var key = result.GetKey();
            if (!_deduplicationMap.ContainsKey(key))
            {
                _deduplicationMap[key] = result;
                _results.Add(result);
            }
        }

        /// <summary>
        /// 获取阻断摘要
        /// </summary>
        public string GetBlockingSummary(ProcessContext context)
        {
            var blockingResults = GetBlockingResults(context).ToList();
            return blockingResults.Any() ? $"发现{blockingResults.Count}个阻断性问题" : "无阻断性问题";
        }

        /// <summary>
        /// 通用查询方法
        /// </summary>
        public bool Any(Func<CheckResult, bool> predicate)
        {
            if (predicate == null) return false;
            return _results.Any(predicate);
        }

        #endregion
    }
}
