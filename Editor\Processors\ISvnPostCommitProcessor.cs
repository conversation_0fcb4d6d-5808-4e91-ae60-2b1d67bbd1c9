using System.Collections.Generic;
using AssetPipeline.Core;
using AssetPipeline.Pipelines.Context;

namespace AssetPipeline.Processors
{
    /// <summary>
    /// SVN提交后检查处理接口 (决定是否允许上传到SVN）
    /// </summary>
    public interface ISvnPostCommitProcessor : IProcessor
    {
        /// <summary>
        /// 在确认提交后调用。
        /// </summary>
        /// <param name="path">已提交资产的路径。</param>
        /// <param name="context">本次提交操作的上下文。</param>
        /// <returns>对提交后操作的检查结果集合。</returns>
        IEnumerable<CheckResult> OnPostCommit(string path, SvnCommitContext context);
    }
} 