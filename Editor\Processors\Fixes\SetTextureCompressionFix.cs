using UnityEditor;
using AssetPipeline.Core;

namespace AssetPipeline.Processors.Fixes
{
    public class SetTextureCompressionFix : IAutoFix
    {
        private readonly TextureImporterCompression _compression;

        public SetTextureCompressionFix(TextureImporterCompression compression)
        {
            _compression = compression;
        }

        public string GetDescription() => $"将压缩类型设置为 {_compression}";

        public bool CanFix(string assetPath)
        {
            return AssetImporter.GetAtPath(assetPath) is TextureImporter;
        }

        public bool DoFix(string assetPath)
        {
            if (AssetImporter.GetAtPath(assetPath) is TextureImporter importer)
            {
                importer.textureCompression = _compression;
                importer.SaveAndReimport();
                return true;
            }
            return false;
        }
    }
} 