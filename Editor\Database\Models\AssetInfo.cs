using System;
using L10.Editor.AssetPipeline.Sqlite;

namespace AssetPipeline.Database.Models
{
    [Table("AssetBaseInfo")]
    public class AssetInfo
    {
        [PrimaryKey]
        public string GUID { get; set; }
        public string Path { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public string Extension { get; set; }
        public long Size { get; set; }
        public string MD5 { get; set; }
        public DateTime ImportTime { get; set; }
        public DateTime ModifyTime { get; set; }
        public DateTime? CheckTime { get; set; }
        public int CheckResult { get; set; }
        public bool IsDeleted { get; set; }
    }
} 