using System;
using System.Collections.Concurrent;
using System.Threading;
using L10.Editor.AssetPipeline.Sqlite;
using Logger = AssetPipeline.Core.Logger;

namespace AssetPipeline.Database
{
    internal class DatabaseWriteQueue : IDisposable
    {
        private readonly string _databasePath;
        private readonly BlockingCollection<Action<SQLiteConnection>> _writeQueue = new BlockingCollection<Action<SQLiteConnection>>();
        private readonly Thread _workerThread;
        private volatile bool _shouldStop;

        public DatabaseWriteQueue(string databasePath)
        {
            _databasePath = databasePath;
            _workerThread = new Thread(ProcessQueue)
            {
                IsBackground = true,
                Name = "DatabaseWriteThread"
            };
            _workerThread.Start();
        }

        public void Enqueue(Action<SQLiteConnection> writeAction)
        {
            if (_shouldStop)
            {
                Logger.Warning(Core.LogModule.Database, "Database write queue is shutting down. Action ignored.");
                return;
            }
            _writeQueue.Add(writeAction);
        }

        private void ProcessQueue()
        {
            SQLiteConnection connection = null;
            try
            {
                // Create a single, long-lived connection for the worker thread.
                connection = new SQLiteConnection(_databasePath, SQLiteOpenFlags.ReadWrite | SQLiteOpenFlags.Create | SQLiteOpenFlags.SharedCache);
                //connection.EnableWriteAheadLogging();

                Logger.Info(Core.LogModule.Database, "Database writer thread started.");

                while (!_shouldStop)
                {
                    if (_writeQueue.TryTake(out var action, TimeSpan.FromMilliseconds(100)))
                    {
                        try
                        {
                            action(connection);
                        }
                        catch (Exception ex)
                        {
                            Logger.Error(Core.LogModule.Database, $"Error executing write action: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error(Core.LogModule.Database, $"Database writer thread failed: {ex.Message}");
            }
            finally
            {
                connection?.Close();
                Logger.Info(Core.LogModule.Database, "Database writer thread stopped.");
            }
        }

        public void Dispose()
        {
            _shouldStop = true;
            _writeQueue.CompleteAdding(); // Allow queue to empty
            _workerThread.Join(TimeSpan.FromSeconds(5)); // Wait for the worker to finish
        }
    }
} 