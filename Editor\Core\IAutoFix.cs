namespace AssetPipeline.Core
{
    /// <summary>
    /// 自动修复操作接口
    /// 用于实现各种资源问题的自动修复功能
    /// </summary>
    public interface IAutoFix
    {
        /// <summary>
        /// 获取修复操作的描述，用于UI显示
        /// </summary>
        string GetDescription();

        /// <summary>
        /// 检查是否可以执行修复操作
        /// </summary>
        /// <param name="assetPath">资产路径</param>
        /// <returns>是否可以修复</returns>
        bool CanFix(string assetPath);

        /// <summary>
        /// 执行修复操作
        /// </summary>
        /// <param name="assetPath">资产路径</param>
        /// <returns>修复是否成功</returns>
        bool DoFix(string assetPath);
    }
} 