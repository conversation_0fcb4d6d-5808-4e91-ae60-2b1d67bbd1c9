# Preset处理器使用指南 - 重新设计版本

## 🎯 概述

基于Unity Preset系统的新一代自定义AssetProcessor，提供Unity原生操作体验，支持选择性属性限制。

### 核心特性
- **Unity原生体验**：像配置Unity导入设置一样简单直观
- **选择性限制**：可以指定只限制Preset中的某些特定属性
- **通用处理器**：一个处理器支持所有AssetImporter类型
- **零重复配置**：所有设置都通过Unity Preset定义

### 处理模式
- **预设模式（PresetMode）**：仅在首次导入时应用预设，后续保留用户修改
- **锁死模式（LockMode）**：强制应用预设，覆盖任何用户修改

## 🏗️ 新架构设计

```
PresetAssetProcessor (重新设计的基类)
├── 内嵌Unity Preset Editor
├── 属性选择性限制机制
└── UniversalPresetProcessor (通用处理器)
```

## 📋 快速开始

### 1. 使用配置向导

最简单的方式是使用内置的配置向导：

```
菜单：Tools → Asset Pipeline → Setup Universal Preset Processors
```

向导会自动：
1. 创建通用Preset处理器
2. 生成对应的默认Preset资产
3. 配置基础设置

### 2. 手动创建处理器

如果需要更精确的控制：

```
右键菜单 → Create → Asset Pipeline → Processors → Universal Preset Processor
```

## 🔧 配置处理器

### 基础配置

1. **处理模式**：选择PresetMode或LockMode
2. **目标Preset**：拖入Unity Preset资产
3. **启用选择性限制**：是否只限制部分属性

### Unity原生Preset配置

处理器内嵌了Unity原生的Preset编辑器，可以直接在Inspector中：

1. 展开"Preset设置"折叠面板
2. 像配置普通Unity导入设置一样修改参数
3. 所有修改会自动保存到Preset资产中

### 选择性属性限制

这是新架构的核心特性：

1. **启用选择性限制**：勾选"启用选择性限制"
2. **选择属性**：在"属性选择器"中勾选要限制的属性
3. **精确控制**：只有勾选的属性会被强制应用

#### 选择性限制的优势

```
传统方式：强制应用所有Preset设置
新方式：只限制关键属性，其他保持灵活

例如：只限制贴图的最大尺寸为1024，但允许美术自由调整压缩格式
```

## 🎮 实际使用场景

### 场景1：项目标准化 - 选择性限制

**需求**：确保所有UI贴图最大尺寸不超过512，但允许美术调整其他设置

**配置**：
```
处理模式：LockMode
选择性限制：启用
限制属性：只勾选 "Max Texture Size"
应用范围：Assets/Art/UI/
```

**效果**：
- 强制所有UI贴图最大尺寸为512
- 美术可以自由调整压缩格式、sRGB等其他设置
- 既保证了性能要求，又保留了创作灵活性

### 场景2：新手友好 - 预设模式

**需求**：为新加入的美术提供标准设置，但允许后续调整

**配置**：
```
处理模式：PresetMode
选择性限制：禁用（应用完整Preset）
应用范围：Assets/Art/Characters/
```

**效果**：
- 新导入的角色资源自动应用标准设置
- 有经验的美术可以根据需要调整特殊资源
- 提供了良好的起点，减少了配置工作

### 场景3：性能优化 - 锁死关键属性

**需求**：项目后期强制所有模型禁用Read/Write以节省内存

**配置**：
```
处理模式：LockMode
选择性限制：启用
限制属性：只勾选 "Is Readable"
应用范围：Assets/Art/Models/
```

**效果**：
- 强制所有模型的Read/Write设置为false
- 其他导入设置（如动画、材质等）保持不变
- 精确的性能优化，不影响其他功能

## 🔍 属性选择器详解

### 属性发现机制

系统会自动分析Preset中的所有属性修改，并提供友好的选择界面：

1. **自动扫描**：读取Preset的PropertyModifications
2. **友好显示**：将技术属性名转换为可读名称
3. **分类管理**：按属性类型和功能分组
4. **搜索过滤**：支持快速查找特定属性

### 常用属性示例

#### 贴图相关属性
- `m_sRGBTexture` → "sRGB Texture"
- `m_IsReadable` → "Read/Write Enabled"
- `m_TextureCompression` → "Compression"
- `m_PlatformSettings.m_MaxTextureSize` → "Max Texture Size"

#### 模型相关属性
- `m_IsReadable` → "Read/Write Enabled"
- `m_OptimizeMesh` → "Optimize Mesh"
- `m_ImportAnimation` → "Import Animation"
- `m_MeshCompression` → "Mesh Compression"

### 批量操作

属性选择器支持批量操作：
- **全选**：选择所有可用属性
- **全不选**：清除所有选择
- **按类型选择**：选择特定类型的属性（计划中）

## ⚡ 性能优化

### 智能跳过机制

新架构包含多层优化：

1. **兼容性检查**：只处理兼容的导入器类型
2. **合规性检查**：跳过已经符合要求的资产
3. **选择性应用**：只修改需要限制的属性
4. **缓存机制**：避免重复的属性解析

### 内存优化

- **按需加载**：只在需要时创建Preset编辑器
- **智能缓存**：缓存属性解析结果
- **及时清理**：自动清理不再使用的编辑器实例

## 🚨 最佳实践

### 1. Preset设计原则

- **单一职责**：每个Preset专注于特定的使用场景
- **最小配置**：只配置必要的属性，避免过度限制
- **版本管理**：将Preset资产纳入版本控制

### 2. 选择性限制策略

- **关键属性优先**：优先限制影响性能和质量的关键属性
- **渐进式限制**：从少数属性开始，逐步增加限制范围
- **团队沟通**：与美术团队充分沟通限制的原因和影响

### 3. 处理器组织

- **按用途分类**：为不同用途创建专门的处理器
- **清晰命名**：使用描述性的处理器名称
- **文档记录**：为每个处理器编写使用说明

## 🔧 高级功能

### 自定义属性友好名称

可以通过扩展`GetFriendlyPropertyName`方法来自定义属性显示名称：

```csharp
private string GetFriendlyPropertyName(string propertyPath)
{
    // 自定义映射规则
    var friendlyNames = new Dictionary<string, string>
    {
        {"m_sRGBTexture", "sRGB纹理"},
        {"m_IsReadable", "读写权限"},
        // 添加更多映射...
    };
    
    return friendlyNames.TryGetValue(propertyPath, out var friendlyName) 
        ? friendlyName 
        : DefaultFriendlyName(propertyPath);
}
```

### 集成自动化流程

处理器可以与CI/CD流程集成：

```csharp
// 在构建脚本中验证资产合规性
[MenuItem("Build/Validate Asset Compliance")]
public static void ValidateAssetCompliance()
{
    // 检查所有资产是否符合Preset要求
    // 生成合规性报告
    // 在不合规时阻止构建
}
```

## 📚 迁移指南

### 从旧版本迁移

如果您正在使用旧版本的TexturePresetProcessor或ModelPresetProcessor：

1. **备份现有配置**：导出当前的处理器设置
2. **创建新处理器**：使用UniversalPresetProcessor替换
3. **配置Preset**：将旧的配置转换为Unity Preset
4. **设置选择性限制**：根据需要配置属性限制
5. **测试验证**：确保新处理器行为符合预期

### 配置转换工具

我们提供了自动转换工具（计划中）：

```
菜单：Tools → Asset Pipeline → Migrate Legacy Preset Processors
```

## 🎯 总结

新的Preset处理器架构提供了：

1. **更好的用户体验**：Unity原生操作感受
2. **更精确的控制**：选择性属性限制
3. **更高的灵活性**：通用处理器支持所有类型
4. **更简单的配置**：零重复配置，Preset驱动

这个重新设计的架构解决了原有设计的所有问题，为大型项目的资产管理提供了强大而灵活的解决方案。
