using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using AssetPipeline.Core;
using AssetPipeline.UI.Components;
using Logger = AssetPipeline.Core.Logger;

namespace AssetPipeline.UI.Windows
{
    public class CheckResultWindow : EditorWindow
    {
        private List<CheckResult> results = new List<CheckResult>();
        private string assetPath = "";
        private Vector2 scrollPosition;
        private bool showDetails = true;
        private CheckResultType filterType = CheckResultType.Unknown;

        // 修复助手相关
        private CheckResult selectedResult;
        private bool showFixAssistant = true;
        private float splitterPosition = 0.6f;
        
        [MenuItem("Asset Pipeline/Show Check Results")]
        public static void ShowWindow()
        {
            var window = GetWindow<CheckResultWindow>("检查结果");
            window.Show();
        }
        
        /// <summary>
        /// 显示检查结果
        /// </summary>
        public static void ShowResults(string assetPath, List<CheckResult> results)
        {
            var window = GetWindow<CheckResultWindow>("检查结果");
            window.SetResults(assetPath, results);
            window.Show();
            window.Focus();
        }
        
        public void SetResults(string path, List<CheckResult> checkResults)
        {
            assetPath = path;
            results = checkResults ?? new List<CheckResult>();
            Repaint();
        }
        
        void OnGUI()
        {
            EditorGUILayout.BeginVertical();

            // 标题和基本信息
            DrawHeader();

            // 过滤器
            DrawFilter();

            // 主内容区域 - 分栏布局
            EditorGUILayout.BeginHorizontal();

            // 左侧：结果列表
            EditorGUILayout.BeginVertical(GUILayout.Width(position.width * splitterPosition));
            DrawResults();
            EditorGUILayout.EndVertical();

            // 分割线
            GUILayout.Box("", GUILayout.Width(2), GUILayout.ExpandHeight(true));
            

            EditorGUILayout.EndHorizontal();

            // 操作按钮
            DrawActions();

            EditorGUILayout.EndVertical();
        }
        
        private void DrawHeader()
        {
            EditorGUILayout.LabelField("检查结果", EditorStyles.boldLabel);
            
            if (!string.IsNullOrEmpty(assetPath))
            {
                EditorGUILayout.LabelField("资源路径:", assetPath);
            }
            
            var errorCount = results.Count(r => r.resultType == CheckResultType.Error);
            var warningCount = results.Count(r => r.resultType == CheckResultType.Warning);
            var infoCount = results.Count(r => r.resultType == CheckResultType.Info);
            
            EditorGUILayout.LabelField($"统计: 错误 {errorCount}, 警告 {warningCount}, 信息 {infoCount}");
            
            EditorGUILayout.Space();
        }
        
        private void DrawFilter()
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("过滤:", GUILayout.Width(40));
            
            var newFilterType = (CheckResultType)EditorGUILayout.EnumPopup(filterType, GUILayout.Width(100));
            if (newFilterType != filterType)
            {
                filterType = newFilterType;
            }
            
            showDetails = EditorGUILayout.Toggle("显示详情", showDetails);

            GUILayout.FlexibleSpace();
            showFixAssistant = EditorGUILayout.Toggle("修复助手", showFixAssistant);

            EditorGUILayout.EndHorizontal();
            EditorGUILayout.Space();
        }
        
        private void DrawResults()
        {
            var filteredResults = results;
            if (filterType != CheckResultType.Unknown)
            {
                filteredResults = results.Where(r => r.resultType == filterType).ToList();
            }
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            
            foreach (var result in filteredResults)
            {
                DrawResult(result);
            }
            
            EditorGUILayout.EndScrollView();
        }
        
        private void DrawResult(CheckResult result)
        {
            // 检查是否选中
            bool isSelected = selectedResult == result;
            var bgColor = isSelected ? new Color(0.3f, 0.5f, 1f, 0.3f) : Color.clear;

            if (isSelected)
            {
                var rect = EditorGUILayout.BeginVertical("box");
                EditorGUI.DrawRect(rect, bgColor);
            }
            else
            {
                EditorGUILayout.BeginVertical("box");
            }

            // 可点击区域用于选择
            var clickRect = GUILayoutUtility.GetLastRect();
            if (Event.current.type == EventType.MouseDown && clickRect.Contains(Event.current.mousePosition))
            {
                selectedResult = result;
                Event.current.Use();
                Repaint();
            }

            // 结果类型和消息
            var color = GetResultColor(result.resultType);
            var originalColor = GUI.color;
            GUI.color = color;

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"[{result.resultType}]", GUILayout.Width(80));
            EditorGUILayout.LabelField(result.message, EditorStyles.wordWrappedLabel);
            EditorGUILayout.EndHorizontal();

            GUI.color = originalColor;
            
            // 详细信息
            if (showDetails)
            {
                if (!string.IsNullOrEmpty(result.processorName))
                {
                    EditorGUILayout.LabelField($"处理器: {result.processorName}", EditorStyles.miniLabel);
                }
                
                if (!string.IsNullOrEmpty(result.detail))
                {
                    EditorGUILayout.LabelField("详情:", EditorStyles.miniLabel);
                    EditorGUILayout.TextArea(result.detail, EditorStyles.wordWrappedMiniLabel);
                }
            }
            
            // 自动修复按钮
            if (result.HasAutoFix)
            {
                EditorGUILayout.BeginHorizontal();
                GUILayout.FlexibleSpace();
                
                if (GUILayout.Button("自动修复", GUILayout.Width(80)))
                {
                    try
                    {
                        if (result.TryAutoFix())
                            EditorUtility.DisplayDialog("修复完成", "自动修复已执行", "确定");
                        
                        // 移除已修复的结果
                        results.Remove(result);
                        Repaint();
                    }
                    catch (System.Exception e)
                    {
                        EditorUtility.DisplayDialog("修复失败", $"自动修复执行失败: {e.Message}", "确定");
                    }
                }
                
                EditorGUILayout.EndHorizontal();
            }
            
            EditorGUILayout.EndVertical();
            EditorGUILayout.Space();
        }

        private void DrawActions()
        {
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("清空结果"))
            {
                results.Clear();
                Repaint();
            }
            
            if (GUILayout.Button("刷新"))
            {
                Repaint();
            }
            
            var autoFixableCount = results.Count(r => r.HasAutoFix);
            if (autoFixableCount > 0)
            {
                if (GUILayout.Button($"全部自动修复 ({autoFixableCount})"))
                {
                    ExecuteAllAutoFixes();
                }
            }
            
            EditorGUILayout.EndHorizontal();
        }
        
        private void ExecuteAllAutoFixes()
        {
            var autoFixableResults = results.Where(r => r.HasAutoFix).ToList();
            var successCount = 0;
            var failCount = 0;
            
            foreach (var result in autoFixableResults)
            {
                try
                {
                    if (result.TryAutoFix())
                        EditorUtility.DisplayDialog("修复完成", $"已成功修复: {result.message}", "确定");
                    else
                        EditorUtility.DisplayDialog("修复失败", $"无法自动修复: {result.message}", "确定");
                    results.Remove(result);
                    successCount++;
                }
                catch (System.Exception e)
                {
                    Logger.Error(LogModule.UI, $"自动修复失败: {result.message} - {e.Message}");
                    failCount++;
                }
            }
            
            EditorUtility.DisplayDialog("批量修复完成", 
                $"成功修复 {successCount} 个问题，失败 {failCount} 个", "确定");
            
            Repaint();
        }
        
        private Color GetResultColor(CheckResultType resultType)
        {
            switch (resultType)
            {
                case CheckResultType.Error:
                    return Color.red;
                case CheckResultType.Warning:
                    return Color.yellow;
                case CheckResultType.Info:
                    return Color.cyan;
                case CheckResultType.Success:
                    return Color.green;
                default:
                    return Color.white;
            }
        }
    }
} 