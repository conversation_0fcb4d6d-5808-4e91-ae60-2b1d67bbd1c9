using System;
using L10.Editor.AssetPipeline.Sqlite;

namespace AssetPipeline.Database.Models
{
    [Table("GUIDHistory")]
    public class GUIDHistoryRecord
    {
        [PrimaryKey, AutoIncrement]
        public int Id { get; set; }
        
        public string GUID { get; set; }
        public string Path { get; set; }
        public string Operation { get; set; }
        public DateTime Timestamp { get; set; }
        public string SourcePath { get; set; }
    }
} 