using System;
using System.Collections.Generic;
using System.Linq;
using AssetPipeline.Config;
using UnityEngine;
using UnityEditor;
using AssetPipeline.Core;
using Logger = AssetPipeline.Core.Logger;

namespace AssetPipeline.Pipelines
{
    /// <summary>
    /// 修改检查上下文
    /// </summary>
    public class ModificationContext : PipelineContext
    {
        // 阻断操作管理
        private readonly Dictionary<string, string> blockedAssetReasons = new Dictionary<string, string>();
        
        /// <summary>
        /// 当前被阻止的路径及其原因（只读）
        /// </summary>
        public IReadOnlyDictionary<string, string> BlockedAssets => blockedAssetReasons;

        /// <summary>
        /// 是否有被阻止的操作
        /// </summary>
        public bool HasBlockedOperations => blockedAssetReasons.Count > 0;

        public ModificationContext(IReadOnlyList<string> assetPaths = null)
        {
            AssetPaths = assetPaths ?? new List<string>();
        }

        /// <summary>
        /// 阻止资源操作，并自动添加一条错误结果
        /// </summary>
        public void BlockAsset(string assetPath, string reason)
        {
            if (string.IsNullOrEmpty(assetPath)) return;
            
            reason = reason ?? "未指定原因";
            if (!blockedAssetReasons.ContainsKey(assetPath))
            {
                blockedAssetReasons[assetPath] = reason;
                
                var blockResult = CheckResult.Error($"操作被阻止: {reason}")
                    .WithAssetPath(assetPath);
                
                results.Add(blockResult);
                
                Logger.Warning(LogModule.Pipeline, $"阻止操作: {assetPath} - {reason}");
            }
        }
        
        /// <summary>
        /// 检查资源是否被阻止
        /// </summary>
        public bool IsBlocked(string assetPath)
        {
            return blockedAssetReasons.ContainsKey(assetPath);
        }
        
        /// <summary>
        /// 完成处理时的钩子
        /// </summary>
        protected override void OnComplete()
        {
            var stats = $"总计检查: {results.Count}个, 阻止: {blockedAssetReasons.Count}个操作";
            Logger.Info(LogModule.Pipeline, $"修改检查会话完成. {stats}");
            
            // 清理数据
            results.Clear();
            blockedAssetReasons.Clear();
        }
    }
} 