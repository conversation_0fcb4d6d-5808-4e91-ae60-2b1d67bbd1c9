using UnityEngine;
using UnityEditor;

namespace AssetPipeline.UI.Components
{
    public static class PipelineStatusIndicator
    {
        private static readonly string[] PipelineKeys = {
            "AssetImportPipeline.Enabled",
            "AssetModificationPipeline.Enabled",
            "SvnCommitPipeline.Enabled",
            "CheckRunnerPipeline.Enabled"
        };

        private static readonly string[] PipelineNames = {
            "AssetImportPipeline",
            "AssetModificationPipeline", 
            "SvnCommitPipeline",
            "CheckRunnerPipeline"
        };

        public static void DrawStatus()
        {
            UIHelper.DrawHeader("Pipeline状态", "⚡");
            
            UIHelper.DrawBox(() =>
            {
                for (int i = 0; i < PipelineKeys.Length; i++)
                {
                    DrawPipelineStatus(PipelineNames[i], GetPipelineEnabled(PipelineKeys[i]));
                }
                
                UIHelper.DrawSeparator();
                
                var memoryMB = System.GC.GetTotalMemory(false) / (1024.0 * 1024.0);
                var memoryColor = memoryMB > 500 ? Color.red : (memoryMB > 200 ? Color.yellow : Color.green);
                UIHelper.DrawMetric("内存使用", $"{memoryMB:F1} MB", memoryColor);
            });
        }

        public static void DrawCompactStatus()
        {
            EditorGUILayout.BeginHorizontal();
            
            var activeCount = GetActivePipelineCount();
            var statusText = activeCount > 0 ? $"运行中 ({activeCount})" : "已停止";
            
            UIHelper.DrawStatusText($"Pipeline: {statusText}", 
                activeCount > 0 ? MessageType.Info : MessageType.Warning);
            
            GUILayout.FlexibleSpace();
            
            var memoryMB = System.GC.GetTotalMemory(false) / (1024.0 * 1024.0);
            EditorGUILayout.LabelField($"内存: {memoryMB:F0}MB", EditorStyles.miniLabel);
            
            EditorGUILayout.EndHorizontal();
        }

        public static bool DrawStatusIndicator(string label, string prefKey, bool showToggle = true)
        {
            var enabled = EditorPrefs.GetBool(prefKey, true);
            
            EditorGUILayout.BeginHorizontal();
            
            if (showToggle)
            {
                var newEnabled = EditorGUILayout.Toggle(enabled, GUILayout.Width(20));
                if (newEnabled != enabled)
                {
                    EditorPrefs.SetBool(prefKey, newEnabled);
                    enabled = newEnabled;
                }
            }
            
            var oldColor = GUI.color;
            GUI.color = enabled ? Color.green : Color.red;
            GUILayout.Label("●", GUILayout.Width(15));
            GUI.color = oldColor;
            
            GUILayout.Label(label);
            
            var statusText = enabled ? "运行中" : "已停用";
            GUI.color = enabled ? Color.green : Color.gray;
            GUILayout.Label(statusText, EditorStyles.miniLabel, GUILayout.Width(50));
            GUI.color = oldColor;
            
            EditorGUILayout.EndHorizontal();
            
            return enabled;
        }

        public static (int active, int total) GetPipelineStats()
        {
            var activeCount = GetActivePipelineCount();
            return (activeCount, PipelineKeys.Length);
        }

        private static void DrawPipelineStatus(string name, bool enabled)
        {
            EditorGUILayout.BeginHorizontal();
            
            var icon = enabled ? "✅" : "❌";
            var status = enabled ? "启用" : "禁用";
            
            EditorGUILayout.LabelField($"{icon} {name}", GUILayout.Width(200));
            UIHelper.DrawStatusText(status, enabled ? MessageType.Info : MessageType.Warning);
            
            EditorGUILayout.EndHorizontal();
        }

        private static bool GetPipelineEnabled(string key)
        {
            return EditorPrefs.GetBool(key, true);
        }

        private static int GetActivePipelineCount()
        {
            var count = 0;
            foreach (var key in PipelineKeys)
            {
                if (GetPipelineEnabled(key)) count++;
            }
            return count;
        }
    }
} 