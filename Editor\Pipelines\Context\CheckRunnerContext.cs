using System;
using System.Collections.Generic;
using AssetPipeline.Config;
using UnityEngine;
using AssetPipeline.Core;
using Logger = AssetPipeline.Core.Logger;

namespace AssetPipeline.Pipelines
{
    /// <summary>
    /// 批量检查处理的上下文
    /// </summary>
    public class CheckRunnerContext : PipelineContext
    {
        public string CheckType { get; }
        public int TotalAssets { get; set; }
        public int ProcessedAssets { get; set; }
        
        public bool HasError { get; set; }
        public string ErrorMessage { get; set; }

        public CheckRunnerContext(string checkType, IReadOnlyList<string> assetPaths)
        {
            CheckType = checkType;
            AssetPaths = assetPaths ?? new List<string>();
            TotalAssets = AssetPaths.Count;
        }

        public string GetSummary()
        {
            return $"{CheckType}: {ProcessedAssets}/{TotalAssets}, 错误{Results.ErrorCount}, 警告{Results.WarningCount}, {Duration.TotalSeconds:F1}s";
        }
    }
} 