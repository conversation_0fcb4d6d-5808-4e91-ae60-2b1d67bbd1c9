using UnityEngine;
using UnityEditor;
using AssetPipeline.Core;
using AssetPipeline.Config;
using AssetPipeline.Processors;
using System.Linq;
using System.Collections.Generic;

namespace AssetPipeline.UI.Windows
{
    public class ConfigWindow : EditorWindow
    {
        private AssetProfile targetProfile;
        private Vector2 leftScrollPosition;
        private Vector2 rightScrollPosition;
        
        // 分栏比例
        private float splitRatio = 0.5f;
        private bool isResizing = false;
        
        private string newPathPattern = "";
        private string testPath = "";
        
        private string newFilterName = "";
        private AssetTypeFlag newFilterTypes = AssetTypeFlag.None;
        private int newFilterLayer = 0;
        private int newFilterPriority = 0;
        private bool showAddFilter = false;
        
        private AssetFilter selectedFilter;
        private AssetProcessor selectedProcessor;
        
        private Dictionary<AssetProcessor, bool> processorEditStates = new Dictionary<AssetProcessor, bool>();
        
        private string newCustomExtension = "";
        private string newSpecificPath = "";
        
        public static void ShowWindow(AssetProfile profile)
        {
            var window = GetWindow<ConfigWindow>("Profile 配置");
            window.targetProfile = profile;
            window.minSize = new Vector2(800, 600);
            window.Show();
        }
        
        void OnGUI()
        {
            if (targetProfile == null)
            {
                EditorGUILayout.HelpBox("没有选中的Profile", MessageType.Warning);
                if (GUILayout.Button("关闭"))
                {
                    Close();
                }
                return;
            }
            
            DrawHeader();
            DrawSplitView();
            DrawFooter();
        }
        
        void DrawHeader()
        {
            EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
            GUILayout.Label($"配置: {targetProfile.name}", EditorStyles.boldLabel);
            GUILayout.FlexibleSpace();
            EditorGUILayout.EndHorizontal();
        }
        
        void DrawSplitView()
        {
            EditorGUILayout.BeginHorizontal();
            
            // 左侧Filter配置面板
            EditorGUILayout.BeginVertical(GUILayout.Width(position.width * splitRatio - 2));
            DrawLeftPanel();
            EditorGUILayout.EndVertical();
            
            DrawSplitter();
            
            // 右侧Processor配置面板
            EditorGUILayout.BeginVertical();
            DrawRightPanel();
            EditorGUILayout.EndVertical();
            
            EditorGUILayout.EndHorizontal();
            
            HandleSplitterDrag();
        }
        
        void DrawSplitter()
        {
            var splitterRect = GUILayoutUtility.GetRect(4, 4, GUILayout.Width(4), GUILayout.ExpandHeight(true));
            EditorGUI.DrawRect(splitterRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));
            EditorGUIUtility.AddCursorRect(splitterRect, MouseCursor.ResizeHorizontal);
            
            if (Event.current.type == EventType.MouseDown && splitterRect.Contains(Event.current.mousePosition))
            {
                isResizing = true;
                Event.current.Use();
            }
        }
        
        void HandleSplitterDrag()
        {
            if (isResizing)
            {
                if (Event.current.type == EventType.MouseDrag)
                {
                    splitRatio = Event.current.mousePosition.x / position.width;
                    splitRatio = Mathf.Clamp(splitRatio, 0.3f, 0.7f);
                    Repaint();
                    Event.current.Use();
                }
                else if (Event.current.type == EventType.MouseUp)
                {
                    isResizing = false;
                    Event.current.Use();
                }
            }
        }
        
        void DrawLeftPanel()
        {
            EditorGUILayout.LabelField("🔍 Filter 配置面板", EditorStyles.boldLabel);
            
            leftScrollPosition = EditorGUILayout.BeginScrollView(leftScrollPosition);
            
            DrawPathConfiguration();
            EditorGUILayout.Space();
            
            DrawFilterConfiguration();
            
            EditorGUILayout.EndScrollView();
        }
        
        void DrawRightPanel()
        {
            EditorGUILayout.LabelField("⚙️ Processor 配置面板", EditorStyles.boldLabel);
            
            rightScrollPosition = EditorGUILayout.BeginScrollView(rightScrollPosition);
            
            if (selectedFilter == null)
            {
                EditorGUILayout.HelpBox("请先在左侧选择一个Filter", MessageType.Info);
            }
            else
            {
                DrawProcessorManagement();
            }
            
            EditorGUILayout.EndScrollView();
        }
        
        void DrawPathConfiguration()
        {
            EditorGUILayout.LabelField("📁 路径匹配配置", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("当前前缀:", GUILayout.Width(60));
            
            if (string.IsNullOrEmpty(targetProfile.PathPrefix))
            {
                EditorGUILayout.LabelField("未设置", EditorStyles.miniLabel);
            }
            else
            {
                EditorGUILayout.LabelField(targetProfile.PathPrefix, EditorStyles.miniLabel);
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.BeginHorizontal();
            newPathPattern = EditorGUILayout.TextField(newPathPattern, GUILayout.ExpandWidth(true));
            
            if (GUILayout.Button("设置", GUILayout.Width(40)))
            {
                SetPathPrefix();
            }
            if (GUILayout.Button("清除", GUILayout.Width(40)))
            {
                ClearPathPrefix();
            }
            EditorGUILayout.EndHorizontal();
            
            if (!string.IsNullOrEmpty(targetProfile.PathPrefix))
            {
                EditorGUILayout.BeginHorizontal();
                testPath = EditorGUILayout.TextField("测试:", testPath);
                if (GUILayout.Button("测试", GUILayout.Width(40)))
                {
                    TestPathMatch();
                }
                EditorGUILayout.EndHorizontal();
            }
            
            EditorGUILayout.EndVertical();
        }
        
        void DrawFilterConfiguration()
        {
            EditorGUILayout.LabelField("过滤器列表 (分层结构)", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            
            var filterCount = targetProfile.Filters?.Count ?? 0;
            
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"总数: {filterCount}", EditorStyles.miniLabel);
            GUILayout.FlexibleSpace();
            showAddFilter = GUILayout.Toggle(showAddFilter, "添加", EditorStyles.miniButton, GUILayout.Width(40));
            EditorGUILayout.EndHorizontal();
            
            if (showAddFilter)
            {
                DrawCompactAddFilter();
            }
            
            if (filterCount > 0)
            {
                DrawFiltersByLayer();
            }
            else
            {
                EditorGUILayout.LabelField("无过滤器", EditorStyles.centeredGreyMiniLabel);
            }
            
            EditorGUILayout.EndVertical();
        }
        
        void DrawCompactAddFilter()
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("名称:", GUILayout.Width(40));
            newFilterName = EditorGUILayout.TextField(newFilterName);
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("类型:", GUILayout.Width(40));
            newFilterTypes = (AssetTypeFlag)EditorGUILayout.EnumFlagsField(newFilterTypes);
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("层级:", GUILayout.Width(40));
            newFilterLayer = EditorGUILayout.IntField(newFilterLayer, GUILayout.Width(40));
            EditorGUILayout.LabelField("优先级:", GUILayout.Width(50));
            newFilterPriority = EditorGUILayout.IntField(newFilterPriority, GUILayout.Width(40));
            
            if (GUILayout.Button("添加"))
            {
                AddNewFilter();
                showAddFilter = false;
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.EndVertical();
        }
        
        void DrawFiltersByLayer()
        {
            // 按Layer分组
            var filtersByLayer = targetProfile.Filters
                .Where(f => f != null)
                .GroupBy(f => f.Layer)
                .OrderBy(g => g.Key);
            
            foreach (var layerGroup in filtersByLayer)
            {
                DrawLayerGroup(layerGroup.Key, layerGroup.ToList());
            }
        }
        
        void DrawLayerGroup(int layer, List<AssetFilter> filters)
        {
            // Layer标题
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"Layer {layer}", EditorStyles.boldLabel, GUILayout.Width(80));
            EditorGUILayout.LabelField($"({filters.Count}个, 按优先级互斥)", EditorStyles.miniLabel);
            EditorGUILayout.EndHorizontal();
            
            // 按优先级排序显示过滤器
            var sortedFilters = filters.OrderByDescending(f => f.Priority).ToList();
            
            EditorGUI.indentLevel++;
            for (int i = 0; i < sortedFilters.Count; i++)
            {
                DrawFilterItem(sortedFilters[i], targetProfile.Filters.IndexOf(sortedFilters[i]));
            }
            EditorGUI.indentLevel--;
            
            EditorGUILayout.EndVertical();
        }
        
        void DrawFilterItem(AssetFilter filter, int index)
        {
            bool isSelected = selectedFilter == filter;
            var bgColor = isSelected ? new Color(0.3f, 0.5f, 1f, 0.3f) : Color.clear;
            
            if (isSelected)
            {
                var rect = EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                EditorGUI.DrawRect(rect, bgColor);
            }
            else
            {
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            }
            
            var buttonRect = EditorGUILayout.BeginHorizontal();
            
            if (GUI.Button(buttonRect, "", GUIStyle.none))
            {
                selectedFilter = selectedFilter == filter ? null : filter;
                selectedProcessor = null;
            }
            
            var statusIcon = filter.Enabled ? "✅" : "❌";
            var expandIcon = isSelected ? "▼" : "▶";
            
            EditorGUILayout.LabelField($"{expandIcon} {statusIcon} {filter.DisplayName}", EditorStyles.boldLabel, GUILayout.Width(150));
            
            EditorGUILayout.BeginVertical();
            
            var typeText = GetFilterTypeDescription(filter);
            EditorGUILayout.LabelField(typeText, EditorStyles.miniLabel);
            
            var processorCount = filter.Processors?.Count ?? 0;
            EditorGUILayout.LabelField($"Layer:{filter.Layer} | Priority:{filter.Priority} | {processorCount}个处理器", EditorStyles.miniLabel);
            
            if (filter.FilePattern != null && !string.IsNullOrEmpty(filter.FilePattern.Pattern))
            {
                var patternDesc = filter.FilePattern.GetDescription();
                if (patternDesc.Length > 40)
                    patternDesc = patternDesc.Substring(0, 37) + "...";
                EditorGUILayout.LabelField($"匹配: {patternDesc}", EditorStyles.miniLabel);
            }
            
            EditorGUILayout.EndVertical();
            
            GUILayout.FlexibleSpace();
            
            if (GUILayout.Button("高级", EditorStyles.miniButton, GUILayout.Width(35)))
            {
                EditFilter(filter);
            }
            
            if (GUILayout.Button("删除", EditorStyles.miniButton, GUILayout.Width(35)))
            {
                DeleteFilter(index);
            }
            
            EditorGUILayout.EndHorizontal();
            
            // 选中时显示内联编辑
            if (isSelected)
            {
                EditorGUILayout.Space();
                DrawFilterInlineEdit(filter);
            }
            
            EditorGUILayout.EndVertical();
        }
        
        string GetFilterTypeDescription(AssetFilter filter)
        {
            var types = new List<string>();
            
            if ((filter.AssetTypes & AssetTypeFlag.Texture) != 0) types.Add("贴图");
            if ((filter.AssetTypes & AssetTypeFlag.Model) != 0) types.Add("模型");
            if ((filter.AssetTypes & AssetTypeFlag.Audio) != 0) types.Add("音频");
            if ((filter.AssetTypes & AssetTypeFlag.Animation) != 0) types.Add("动画");
            if ((filter.AssetTypes & AssetTypeFlag.Material) != 0) types.Add("材质");
            if ((filter.AssetTypes & AssetTypeFlag.Prefab) != 0) types.Add("预制体");
            if ((filter.AssetTypes & AssetTypeFlag.Script) != 0) types.Add("脚本");
            if ((filter.AssetTypes & AssetTypeFlag.Other) != 0) types.Add("自定义");
            
            if (types.Count == 0 || filter.AssetTypes == AssetTypeFlag.All)
                return "类型: 全部";
            
            var result = "类型: " + string.Join(", ", types);
            if (result.Length > 35)
                result = result.Substring(0, 32) + "...";
            
            return result;
        }
        
        void DrawFilterInlineEdit(AssetFilter filter)
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("快速编辑", EditorStyles.miniLabel);
            
            EditorGUI.BeginChangeCheck();
            
            // 基础属性
            filter.Enabled = EditorGUILayout.Toggle("启用", filter.Enabled);
            filter.DisplayName = EditorGUILayout.TextField("名称", filter.DisplayName);
            filter.Priority = EditorGUILayout.IntField("优先级", filter.Priority);
            
            // 资源类型配置
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("资源类型配置", EditorStyles.miniLabel);
            var newAssetTypes = (AssetTypeFlag)EditorGUILayout.EnumFlagsField("资源类型", filter.AssetTypes);
            if (newAssetTypes != filter.AssetTypes)
            {
                var typesField = typeof(AssetFilter).GetField("assetTypes", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                typesField?.SetValue(filter, newAssetTypes);
            }
            
            // 自定义扩展名配置
            if ((newAssetTypes & AssetTypeFlag.Other) != 0)
            {
                EditorGUILayout.Space();
                EditorGUILayout.LabelField("自定义扩展名", EditorStyles.miniLabel);
                
                for (int i = 0; i < filter.CustomExtensions.Count; i++)
                {
                    EditorGUILayout.BeginHorizontal();
                    filter.CustomExtensions[i] = EditorGUILayout.TextField($"扩展名 {i + 1}", filter.CustomExtensions[i]);
                    if (GUILayout.Button("X", GUILayout.Width(20)))
                    {
                        filter.CustomExtensions.RemoveAt(i);
                        break;
                    }
                    EditorGUILayout.EndHorizontal();
                }
                
                EditorGUILayout.BeginHorizontal();
                newCustomExtension = EditorGUILayout.TextField("新扩展名", newCustomExtension);
                if (GUILayout.Button("+", GUILayout.Width(20)) && !string.IsNullOrEmpty(newCustomExtension))
                {
                    if (!newCustomExtension.StartsWith("."))
                        newCustomExtension = "." + newCustomExtension;
                    filter.CustomExtensions.Add(newCustomExtension);
                    newCustomExtension = "";
                }
                EditorGUILayout.EndHorizontal();
            }
            
            // 文件匹配配置
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("文件匹配配置", EditorStyles.miniLabel);
            
            if (filter.FilePattern != null)
            {
                EditorGUI.indentLevel++;
                
                var modeField = typeof(PathMatcher).GetField("mode", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var patternField = typeof(PathMatcher).GetField("pattern", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var ignoreCaseField = typeof(PathMatcher).GetField("ignoreCase", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                var currentMode = (PathMatcher.MatchMode)modeField?.GetValue(filter.FilePattern);
                var currentPattern = (string)patternField?.GetValue(filter.FilePattern) ?? "";
                var currentIgnoreCase = (bool)ignoreCaseField?.GetValue(filter.FilePattern);
                
                var newMode = (PathMatcher.MatchMode)EditorGUILayout.EnumPopup("匹配模式", currentMode);
                var newPattern = EditorGUILayout.TextField("匹配模式", currentPattern);
                var newIgnoreCase = EditorGUILayout.Toggle("忽略大小写", currentIgnoreCase);
                
                if (!newMode.Equals(currentMode))
                    modeField?.SetValue(filter.FilePattern, newMode);
                if (newPattern != currentPattern)
                    patternField?.SetValue(filter.FilePattern, newPattern);
                if (newIgnoreCase != currentIgnoreCase)
                    ignoreCaseField?.SetValue(filter.FilePattern, newIgnoreCase);
                
                if (!string.IsNullOrEmpty(newPattern))
                {
                    EditorGUILayout.LabelField($"描述: {filter.FilePattern.GetDescription()}", EditorStyles.miniLabel);
                }
                
                EditorGUI.indentLevel--;
            }
            
            // 特定路径配置
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("特定路径配置", EditorStyles.miniLabel);
            EditorGUILayout.LabelField("(空时匹配所有路径)", EditorStyles.miniLabel);
            
            for (int i = 0; i < filter.SpecificPaths.Count; i++)
            {
                EditorGUILayout.BeginHorizontal();
                filter.SpecificPaths[i] = EditorGUILayout.TextField($"路径 {i + 1}", filter.SpecificPaths[i]);
                if (GUILayout.Button("X", GUILayout.Width(20)))
                {
                    filter.SpecificPaths.RemoveAt(i);
                    break;
                }
                EditorGUILayout.EndHorizontal();
            }
            
            EditorGUILayout.BeginHorizontal();
            newSpecificPath = EditorGUILayout.TextField("新路径", newSpecificPath);
            if (GUILayout.Button("+", GUILayout.Width(20)) && !string.IsNullOrEmpty(newSpecificPath))
            {
                filter.SpecificPaths.Add(newSpecificPath);
                newSpecificPath = "";
            }
            EditorGUILayout.EndHorizontal();
            
            // 排除规则配置
            if (filter.FileExclusions.Count > 0)
            {
                EditorGUILayout.Space();
                EditorGUILayout.LabelField("排除规则", EditorStyles.miniLabel);
                
                for (int i = 0; i < filter.FileExclusions.Count; i++)
                {
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField($"排除 {i + 1}", GUILayout.Width(50));
                    
                    var exclusion = filter.FileExclusions[i];
                    if (exclusion != null)
                    {
                        var patternField = typeof(PathMatcher).GetField("pattern", 
                            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                        var currentPattern = (string)patternField?.GetValue(exclusion) ?? "";
                        var newPattern = EditorGUILayout.TextField(currentPattern);
                        
                        if (newPattern != currentPattern)
                        {
                            patternField?.SetValue(exclusion, newPattern);
                        }
                    }
                    
                    if (GUILayout.Button("X", GUILayout.Width(20)))
                    {
                        filter.FileExclusions.RemoveAt(i);
                        break;
                    }
                    EditorGUILayout.EndHorizontal();
                }
            }
            
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("添加排除规则", GUILayout.Width(100)))
            {
                filter.FileExclusions.Add(new PathMatcher());
            }
            EditorGUILayout.EndHorizontal();
            
            if (EditorGUI.EndChangeCheck())
            {
                EditorUtility.SetDirty(filter);
            }
            
            EditorGUILayout.EndVertical();
        }
        
        void SetPathPrefix()
        {
            if (string.IsNullOrEmpty(newPathPattern))
            {
                EditorUtility.DisplayDialog("错误", "路径前缀不能为空", "确定");
                return;
            }
            
            try
            {
                // 规范化路径
                var normalizedPath = newPathPattern.Replace('\\', '/');
                if (!normalizedPath.EndsWith("/") && !normalizedPath.EndsWith("*"))
                {
                    normalizedPath += "/";
                }
                
                // 设置到Profile
                targetProfile.PathPrefix = normalizedPath;
                EditorUtility.SetDirty(targetProfile);
                AssetDatabase.SaveAssets();
                
                Debug.Log($"设置路径前缀成功: {normalizedPath}");
                newPathPattern = "";
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"设置路径前缀失败: {ex.Message}");
                EditorUtility.DisplayDialog("错误", "设置路径前缀失败，请查看Console了解详情。", "确定");
            }
        }
        
        void ClearPathPrefix()
        {
            if (EditorUtility.DisplayDialog("确认", "确定要清除当前路径前缀吗？", "确定", "取消"))
            {
                targetProfile.PathPrefix = "";
                EditorUtility.SetDirty(targetProfile);
                AssetDatabase.SaveAssets();
                Debug.Log("已清除路径前缀");
            }
        }
        
        void TestPathMatch()
        {
            if (string.IsNullOrEmpty(targetProfile.PathPrefix))
            {
                EditorUtility.DisplayDialog("测试结果", "没有配置路径前缀", "确定");
                return;
            }
            
            bool matches = targetProfile.IsMatch(testPath);
            EditorUtility.DisplayDialog("测试结果", 
                $"路径: {testPath}\n前缀: {targetProfile.PathPrefix}\n结果: {(matches ? "匹配" : "不匹配")}", 
                "确定");
        }
        
        void AddNewFilter()
        {
            if (string.IsNullOrEmpty(newFilterName))
            {
                EditorUtility.DisplayDialog("错误", "过滤器名称不能为空", "确定");
                return;
            }
            
            try
            {
                // 创建新的过滤器
                var newFilter = CreateInstance<AssetFilter>();
                newFilter.DisplayName = newFilterName;
                newFilter.name = $"Filter_{System.DateTime.Now:HHmmss}";
                
                // 设置Layer和Priority
                var layerField = typeof(AssetFilter).GetField("layer", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (layerField != null)
                {
                    layerField.SetValue(newFilter, newFilterLayer);
                }
                
                var priorityField = typeof(AssetFilter).GetField("priority", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (priorityField != null)
                {
                    priorityField.SetValue(newFilter, newFilterPriority);
                }
                
                // 设置资源类型
                var assetTypesField = typeof(AssetFilter).GetField("assetTypes", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (assetTypesField != null)
                {
                    assetTypesField.SetValue(newFilter, newFilterTypes);
                }
                
                // 确保目录存在
                var filterDir = "Assets/Editor/AssetPipeline/Data/Filters";
                if (!System.IO.Directory.Exists(filterDir))
                {
                    System.IO.Directory.CreateDirectory(filterDir);
                }
                
                // 保存为资源文件
                var filterPath = AssetDatabase.GenerateUniqueAssetPath($"{filterDir}/{newFilter.name}.asset");
                AssetDatabase.CreateAsset(newFilter, filterPath);
                
                // 添加到Profile
                if (targetProfile.Filters == null)
                {
                    targetProfile.Filters = new List<AssetFilter>();
                }
                targetProfile.Filters.Add(newFilter);
                EditorUtility.SetDirty(targetProfile);
                AssetDatabase.SaveAssets();
                
                Debug.Log($"成功添加过滤器: {newFilter.DisplayName}");
                
                // 重置输入
                newFilterName = "";
                newFilterTypes = AssetTypeFlag.None;
                newFilterLayer = 0;
                newFilterPriority = 0;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"添加过滤器失败: {ex.Message}");
                EditorUtility.DisplayDialog("添加失败", "添加过滤器时发生错误，请查看Console了解详情。", "确定");
            }
        }
        
        void EditFilter(AssetFilter filter)
        {
            // 在Inspector中显示过滤器以进行详细编辑
            Selection.activeObject = filter;
            EditorGUIUtility.PingObject(filter);
        }
        
        void DeleteFilter(int index)
        {
            if (index < 0 || index >= targetProfile.Filters.Count) return;
            
            var filter = targetProfile.Filters[index];
            if (EditorUtility.DisplayDialog("确认删除", 
                $"确定要删除过滤器 '{filter.DisplayName}' 吗？这将同时删除关联的资产文件。", "删除", "取消"))
            {
                try
                {
                    // 删除资产文件
                    var filterPath = AssetDatabase.GetAssetPath(filter);
                    if (!string.IsNullOrEmpty(filterPath))
                    {
                        AssetDatabase.DeleteAsset(filterPath);
                    }
                    
                    // 从Profile中移除
                    targetProfile.Filters.RemoveAt(index);
                    EditorUtility.SetDirty(targetProfile);
                    AssetDatabase.SaveAssets();
                    
                    Debug.Log($"成功删除过滤器: {filter.DisplayName}");
                }
                catch (System.Exception ex)
                {
                    Debug.LogError($"删除过滤器失败: {ex.Message}");
                }
            }
        }
        
        void ShowAddProcessorMenu(AssetFilter filter)
        {
            var menu = new GenericMenu();
            
            // 获取所有可用的处理器类型
            var processorTypes = GetAvailableProcessorTypes();
            
            foreach (var type in processorTypes)
            {
                var displayName = GetProcessorDisplayName(type);
                menu.AddItem(new GUIContent(displayName), false, () => CreateAndAddProcessor(filter, type));
            }
            
            if (processorTypes.Length == 0)
            {
                menu.AddDisabledItem(new GUIContent("无可用处理器"));
            }
            
            menu.ShowAsContext();
        }
        
        System.Type[] GetAvailableProcessorTypes()
        {
            // 获取所有继承自AssetProcessor的类型
            var types = System.AppDomain.CurrentDomain.GetAssemblies()
                .SelectMany(assembly => assembly.GetTypes())
                .Where(type => 
                    type.IsSubclassOf(typeof(AssetProcessor)) && 
                    !type.IsAbstract && 
                    type.IsPublic)
                .ToArray();
            
            return types;
        }
        
        string GetProcessorDisplayName(System.Type type)
        {
            // 尝试获取ProcessorAttribute中的显示名称
            var attr = type.GetCustomAttributes(typeof(ProcessorAttribute), false).FirstOrDefault() as ProcessorAttribute;
            return attr?.DisplayName ?? type.Name;
        }
        
        void CreateAndAddProcessor(AssetFilter filter, System.Type processorType)
        {
            try
            {
                var processor = ScriptableObject.CreateInstance(processorType) as AssetProcessor;
                if (processor == null)
                {
                    Debug.LogError($"无法创建处理器: {processorType.Name}");
                    return;
                }
                
                processor.name = $"{processorType.Name}_{System.DateTime.Now:HHmmss}";
                
                // 确保目录存在
                var processorDir = "Assets/Editor/AssetPipeline/Data/Processors";
                if (!System.IO.Directory.Exists(processorDir))
                {
                    System.IO.Directory.CreateDirectory(processorDir);
                }
                
                // 保存处理器资源
                var processorPath = AssetDatabase.GenerateUniqueAssetPath($"{processorDir}/{processor.name}.asset");
                AssetDatabase.CreateAsset(processor, processorPath);
                
                // 添加到过滤器
                filter.AddProcessor(processor);
                EditorUtility.SetDirty(filter);
                AssetDatabase.SaveAssets();
                
                Debug.Log($"成功添加处理器: {GetProcessorDisplayName(processorType)}");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"添加处理器失败: {ex.Message}");
                EditorUtility.DisplayDialog("添加失败", "添加处理器时发生错误，请查看Console了解详情。", "确定");
            }
        }
        
        void RemoveProcessor(AssetFilter filter, int index)
        {
            var processor = filter.Processors[index];
            if (EditorUtility.DisplayDialog("确认删除", 
                $"确定要删除处理器 '{processor.DisplayName ?? processor.GetType().Name}' 吗？", "删除", "取消"))
            {
                try
                {
                    // 清理编辑状态
                    if (processorEditStates.ContainsKey(processor))
                    {
                        processorEditStates.Remove(processor);
                    }
                    
                    // 删除处理器资源文件
                    var processorPath = AssetDatabase.GetAssetPath(processor);
                    if (!string.IsNullOrEmpty(processorPath))
                    {
                        AssetDatabase.DeleteAsset(processorPath);
                    }
                    
                    // 从过滤器中移除
                    filter.RemoveProcessor(processor);
                    EditorUtility.SetDirty(filter);
                    AssetDatabase.SaveAssets();
                    
                    Debug.Log($"成功删除处理器: {processor.DisplayName ?? processor.GetType().Name}");
                }
                catch (System.Exception ex)
                {
                    Debug.LogError($"删除处理器失败: {ex.Message}");
                }
            }
        }
        
        void DrawProcessorManagement()
        {
            EditorGUILayout.LabelField($"当前Filter: {selectedFilter.DisplayName}", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            
            // Filter信息和快速设置
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("启用:", GUILayout.Width(40));
            var newEnabled = EditorGUILayout.Toggle(selectedFilter.Enabled, GUILayout.Width(20));
            if (newEnabled != selectedFilter.Enabled)
            {
                selectedFilter.Enabled = newEnabled;
                EditorUtility.SetDirty(selectedFilter);
            }
            
            GUILayout.Space(20);
            EditorGUILayout.LabelField($"Layer: {selectedFilter.Layer}  Priority: {selectedFilter.Priority}", EditorStyles.miniLabel);
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.EndVertical();
            
            EditorGUILayout.Space();
            
            // 处理器列表管理
            EditorGUILayout.LabelField("处理器列表", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            
            // 添加处理器
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("+ 添加处理器", EditorStyles.miniButton, GUILayout.Width(80)))
            {
                ShowAddProcessorMenu(selectedFilter);
            }
            GUILayout.FlexibleSpace();
            
            var processorCount = selectedFilter.Processors?.Count ?? 0;
            EditorGUILayout.LabelField($"总数: {processorCount}", EditorStyles.miniLabel);
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            // 处理器列表
            if (processorCount > 0)
            {
                for (int i = 0; i < selectedFilter.Processors.Count; i++)
                {
                    var processor = selectedFilter.Processors[i];
                    if (processor != null)
                    {
                        DrawProcessorItem(processor, i);
                    }
                }
            }
            else
            {
                EditorGUILayout.LabelField("暂无处理器", EditorStyles.centeredGreyMiniLabel);
            }
            
            EditorGUILayout.EndVertical();
        }
        
        void DrawProcessorItem(AssetProcessor processor, int index)
        {
            bool isSelected = selectedProcessor == processor;
            var bgColor = isSelected ? new Color(1f, 0.5f, 0.3f, 0.3f) : Color.clear;
            
            if (isSelected)
            {
                var rect = EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                EditorGUI.DrawRect(rect, bgColor);
            }
            else
            {
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            }
            
            var buttonRect = EditorGUILayout.BeginHorizontal();
            
            if (GUI.Button(buttonRect, "", GUIStyle.none))
            {
                selectedProcessor = selectedProcessor == processor ? null : processor;
            }
            
            var statusIcon = processor.Enabled ? "✅" : "❌";
            var expandIcon = isSelected ? "▼" : "▶";
            var displayName = processor.DisplayName ?? processor.GetType().Name;
            
            EditorGUILayout.LabelField($"{expandIcon} {statusIcon} {displayName}", EditorStyles.boldLabel, GUILayout.Width(180));
            
            EditorGUILayout.BeginVertical();
            
            var typeInfo = GetProcessorTypeDescription(processor);
            EditorGUILayout.LabelField(typeInfo, EditorStyles.miniLabel);
            
            var stats = processor.Statistics;
            var statsText = stats.ProcessedCount > 0 ? 
                $"Priority:{processor.Priority} | 已处理:{stats.ProcessedCount} | 成功率:{(float)stats.SuccessCount/stats.ProcessedCount*100:F0}%" :
                $"Priority:{processor.Priority} | 未使用";
            EditorGUILayout.LabelField(statsText, EditorStyles.miniLabel);
            
            if (!string.IsNullOrEmpty(processor.Description))
            {
                var desc = processor.Description;
                if (desc.Length > 50)
                    desc = desc.Substring(0, 47) + "...";
                EditorGUILayout.LabelField($"描述: {desc}", EditorStyles.miniLabel);
            }
            
            EditorGUILayout.EndVertical();
            
            GUILayout.FlexibleSpace();
            
            if (GUILayout.Button("高级", EditorStyles.miniButton, GUILayout.Width(35)))
            {
                AssetProcessorEditorWindow.ShowWindow(processor);
            }
            
            if (GUILayout.Button("删除", EditorStyles.miniButton, GUILayout.Width(35)))
            {
                RemoveProcessor(selectedFilter, index);
                if (selectedProcessor == processor)
                {
                    selectedProcessor = null;
                }
            }
            
            EditorGUILayout.EndHorizontal();
            
            if (isSelected)
            {
                EditorGUILayout.Space();
                DrawProcessorInlineConfig(processor);
            }
            
            EditorGUILayout.EndVertical();
        }
        
        string GetProcessorTypeDescription(AssetProcessor processor)
        {
            var typeName = processor.GetType().Name;
            
            // 简化常见处理器类型名称
            if (typeName.Contains("Texture"))
                return $"类型: 纹理处理器 | v{processor.Version}";
            else if (typeName.Contains("Commit"))
                return $"类型: 提交检查器 | v{processor.Version}";
            else if (typeName.Contains("Import"))
                return $"类型: 导入处理器 | v{processor.Version}";
            else if (typeName.Contains("Audio"))
                return $"类型: 音频处理器 | v{processor.Version}";
            else if (typeName.Contains("Model"))
                return $"类型: 模型处理器 | v{processor.Version}";
            else
                return $"类型: {typeName} | v{processor.Version}";
        }
        
        void DrawProcessorInlineConfig(AssetProcessor processor)
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("快速配置", EditorStyles.miniLabel);
            
            EditorGUI.BeginChangeCheck();
            
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("启用:", GUILayout.Width(40));
            processor.Enabled = EditorGUILayout.Toggle(processor.Enabled, GUILayout.Width(20));
            
            GUILayout.Space(20);
            EditorGUILayout.LabelField("优先级:", GUILayout.Width(50));
            processor.Priority = EditorGUILayout.IntField(processor.Priority, GUILayout.Width(50));
            EditorGUILayout.EndHorizontal();
            
            // if (processor is ConfigurableTextureProcessor textureProcessor)
            // {
            //     DrawTextureProcessorInlineConfig(textureProcessor);
            // }
            // else if (processor is ConfigurableCommitProcessor commitProcessor)
            // {
            //     DrawCommitProcessorInlineConfig(commitProcessor);
            // }
            // else
            // {
            //     EditorGUILayout.LabelField("通用处理器配置", EditorStyles.miniLabel);
            //     EditorGUILayout.LabelField($"类型: {processor.GetType().Name}", EditorStyles.miniLabel);
            //     EditorGUILayout.LabelField($"描述: {processor.Description}", EditorStyles.wordWrappedMiniLabel);
            // }
            
            if (EditorGUI.EndChangeCheck())
            {
                EditorUtility.SetDirty(processor);
            }
            
            EditorGUILayout.EndVertical();
        }
        
        // void DrawTextureProcessorInlineConfig(ConfigurableTextureProcessor processor)
        // {
        //     EditorGUILayout.LabelField("纹理处理配置", EditorStyles.miniLabel);
        //     
        //     var maxSizeField = typeof(ConfigurableTextureProcessor).GetField("maxTextureSize", 
        //         System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        //     if (maxSizeField != null)
        //     {
        //         EditorGUILayout.BeginHorizontal();
        //         EditorGUILayout.LabelField("最大尺寸:", GUILayout.Width(60));
        //         var currentValue = (int)maxSizeField.GetValue(processor);
        //         var newValue = EditorGUILayout.IntField(currentValue, GUILayout.Width(60));
        //         if (newValue != currentValue)
        //         {
        //             maxSizeField.SetValue(processor, newValue);
        //         }
        //         EditorGUILayout.EndHorizontal();
        //     }
        // }
        //
        // void DrawCommitProcessorInlineConfig(ConfigurableCommitProcessor processor)
        // {
        //     EditorGUILayout.LabelField("提交检查配置", EditorStyles.miniLabel);
        //     
        //     var checkFileSizeField = typeof(ConfigurableCommitProcessor).GetField("checkFileSize", 
        //         System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        //     if (checkFileSizeField != null)
        //     {
        //         EditorGUILayout.BeginHorizontal();
        //         EditorGUILayout.LabelField("检查文件大小:", GUILayout.Width(80));
        //         var currentValue = (bool)checkFileSizeField.GetValue(processor);
        //         var newValue = EditorGUILayout.Toggle(currentValue, GUILayout.Width(20));
        //         if (newValue != currentValue)
        //         {
        //             checkFileSizeField.SetValue(processor, newValue);
        //         }
        //         EditorGUILayout.EndHorizontal();
        //     }
        // }
        
        void DrawFooter()
        {
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("保存配置"))
            {
                EditorUtility.SetDirty(targetProfile);
                AssetDatabase.SaveAssets();
                Debug.Log("配置已保存");
            }
            
            GUILayout.FlexibleSpace();
            
            if (GUILayout.Button("关闭"))
            {
                Close();
            }
            
            EditorGUILayout.EndHorizontal();
        }
    }
} 