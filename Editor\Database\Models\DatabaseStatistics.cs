namespace AssetPipeline.Database.Models
{
    public class DatabaseStatistics
    {
        public bool IsEnabled { get; set; }
        public string DatabasePath { get; set; }
        public long DatabaseSize { get; set; }
        public DatabasePerformanceStats PerformanceStats { get; set; }
        
        public override string ToString()
        {
            return $"Path: {DatabasePath}, Size: {DatabaseSize / 1024}KB, Enabled: {IsEnabled}";
        }
    }

    public class DatabasePerformanceStats
    {
        // ... (内容待定)
    }
} 