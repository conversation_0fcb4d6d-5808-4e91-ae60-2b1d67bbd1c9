using AssetPipeline.Core;

namespace AssetPipeline.Processors
{
    /// <summary>
    /// 所有处理器的基础接口。
    /// </summary>
    public interface IProcessor
    {
        /// <summary>
        /// 检查当前处理器是否应该处理指定的资产。
        /// </summary>
        /// <param name="assetPath">要检查的资产路径。</param>
        /// <returns>如果应处理该资产，则为true；否则为false。</returns>
        bool CanProcess(string assetPath);
        
        /// <summary>
        /// 当处理器被添加到规则中时调用，用于一次性初始化。
        /// </summary>
        void OnAdded();
        
        /// <summary>
        /// 在处理管线开始时调用，用于执行预处理或验证。
        /// </summary>
        /// <returns>如果启动成功，则返回true；如果启动失败（例如配置无效），则返回false，管线将中止此处理器的执行。</returns>
        bool OnStart();
        
        /// <summary>
        /// 在管线处理完所有资产后调用，用于执行清理工作。
        /// </summary>
        void OnCompleted();
    }
}
