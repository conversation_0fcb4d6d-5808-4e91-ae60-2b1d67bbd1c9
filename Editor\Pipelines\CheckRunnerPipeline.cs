using System;
using System.Collections.Generic;
using System.Linq;
using AssetPipeline.Config;
using UnityEditor;
using AssetPipeline.Core;
using AssetPipeline.Processors;
using AssetPipeline.UI.Windows;
using Logger = AssetPipeline.Core.Logger;

namespace AssetPipeline.Pipelines
{
    /// <summary>
    /// 主动检查管线
    /// </summary>
    public static class CheckRunnerPipeline
    {
        private const string PIPELINE_NAME = "checkrunner";
        
        public static CheckRunnerContext Run(string[] paths, string type)
        {
            var context = new CheckRunnerContext(type, paths);
            
            if (!AssetPipelineConfig.IsPipelineEnabled(PIPELINE_NAME))
            {
                context.ErrorMessage = "Check Runner Pipeline is disabled in settings.";
                Logger.Debug(LogModule.Pipeline, $"[{PIPELINE_NAME}] Pipeline disabled.");
                context.Complete();
                return context;
            }
            
            if (context.AssetPaths.Count == 0)
            {
                context.Complete();
                return context;
            }

            try
            {
                EditorUtility.DisplayProgressBar("Running Checks", "Collecting processors...", 0.1f);
                
                var resultsByPath = PipelineHelper.ExecuteProcessors<ICheckRunnerProcessor>(
                    context.AssetPaths,
                    (processor, path) => processor.RunCheck(path, context)
                );

                context.Results.AddRange(resultsByPath.SelectMany(kvp => kvp.Value));
                context.ProcessedAssets = context.AssetPaths.Count;
            }
            catch (Exception e)
            {
                context.HasError = true;
                context.ErrorMessage = e.Message;
                Logger.Error(LogModule.Pipeline, $"[{PIPELINE_NAME}] An exception occurred during check: {e.Message}");
            }
            finally
            {
                if (context.Results.HasErrors)
                {
                    context.HasError = true;
                }

                if (AssetPipelineSettings.AutoOpenResultWindow && context.Results.Count > 0)
                {
                    CheckResultWindow.ShowResults(context.CheckType, context.Results.Results.ToList());
                }

                context.Complete();
                EditorUtility.ClearProgressBar();
            }

            return context;
        }
    }
} 