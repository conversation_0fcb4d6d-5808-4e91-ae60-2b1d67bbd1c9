using UnityEditor;
using UnityEditor.IMGUI.Controls;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System;
using System.IO;

namespace AssetPipeline.UI
{
    public class AssetListTreeView : TreeView
    {
        enum Columns
        {
            Name,
            Type,
            Size,
            Path
        }

        enum SortOption
        {
            Name,
            Type,
            Size,
            Path
        }

        SortOption[] m_SortOptions = 
        {
            SortOption.Name,
            SortOption.Type,
            SortOption.Size,
            SortOption.Path,
        };

        const float kHeaderHeight = 20f;
        string m_SearchString = string.Empty;
        
        public string searchString
        {
            get { return m_SearchString; }
            set 
            { 
                if (m_SearchString != value)
                {
                    m_SearchString = value;
                    Reload();
                }
            }
        }

        public AssetListTreeView(TreeViewState state, MultiColumnHeader multiColumnHeader) 
            : base(state, multiColumnHeader)
        {
            rowHeight = 16f;
            showAlternatingRowBackgrounds = true;
            showBorder = true;
            
            multiColumnHeader.sortingChanged += OnSortingChanged;
            multiColumnHeader.height = kHeaderHeight;
            
            Reload();
        }

        void OnSortingChanged(MultiColumnHeader multiColumnHeader)
        {
            if (m_AssetPaths == null || m_AssetPaths.Count <= 1)
                return;

            var sortedColumns = multiColumnHeader.state.sortedColumns;
            if (sortedColumns.Length == 0)
                return;

            m_AssetPaths.Sort((a, b) =>
            {
                for (int i = 0; i < sortedColumns.Length; i++)
                {
                    int column = sortedColumns[i];
                    bool ascending = multiColumnHeader.IsSortedAscending(column);
                    int result = CompareByColumn(a, b, (Columns)column);
                    if (result != 0)
                    {
                        return ascending ? result : -result;
                    }
                }
                return 0;
            });

            Reload();
        }

        int CompareByColumn(string pathA, string pathB, Columns column)
        {
            switch (column)
            {
                case Columns.Name:
                    return string.Compare(
                        Path.GetFileName(pathA), 
                        Path.GetFileName(pathB), 
                        StringComparison.OrdinalIgnoreCase);
                    
                case Columns.Type:
                    var typeA = AssetDatabase.GetMainAssetTypeAtPath(pathA)?.Name ?? "";
                    var typeB = AssetDatabase.GetMainAssetTypeAtPath(pathB)?.Name ?? "";
                    return string.Compare(typeA, typeB, StringComparison.OrdinalIgnoreCase);
                    
                case Columns.Size:
                    var sizeA = new FileInfo(pathA).Length;
                    var sizeB = new FileInfo(pathB).Length;
                    return sizeA.CompareTo(sizeB);
                    
                case Columns.Path:
                    return string.Compare(pathA, pathB, StringComparison.OrdinalIgnoreCase);
                    
                default:
                    return 0;
            }
        }

        public static MultiColumnHeaderState CreateDefaultMultiColumnHeaderState(float treeViewWidth)
        {
            var columns = new[]
            {
                new MultiColumnHeaderState.Column
                {
                    headerContent = new GUIContent("Name"),
                    headerTextAlignment = TextAlignment.Left,
                    sortedAscending = true,
                    sortingArrowAlignment = TextAlignment.Center,
                    width = 200,
                    minWidth = 100,
                    autoResize = true,
                    allowToggleVisibility = false
                },
                new MultiColumnHeaderState.Column
                {
                    headerContent = new GUIContent("Type"),
                    headerTextAlignment = TextAlignment.Left,
                    sortedAscending = true,
                    sortingArrowAlignment = TextAlignment.Center,
                    width = 100,
                    minWidth = 50,
                    autoResize = false,
                    canSort = true
                },
                new MultiColumnHeaderState.Column
                {
                    headerContent = new GUIContent("Size"),
                    headerTextAlignment = TextAlignment.Left,
                    sortedAscending = true,
                    sortingArrowAlignment = TextAlignment.Center,
                    width = 100,
                    minWidth = 50,
                    autoResize = false,
                    canSort = true
                },
                new MultiColumnHeaderState.Column
                {
                    headerContent = new GUIContent("Path"),
                    headerTextAlignment = TextAlignment.Left,
                    sortedAscending = true,
                    sortingArrowAlignment = TextAlignment.Center,
                    width = 300,
                    minWidth = 100,
                    autoResize = true,
                    canSort = true
                }
            };

            return new MultiColumnHeaderState(columns);
        }

        List<string> m_AssetPaths = new List<string>();

        public void SetAssets(IEnumerable<string> assetPaths)
        {
            m_AssetPaths = assetPaths?
                .Where(path => !Directory.Exists(path))
                .ToList() ?? new List<string>();
            Reload();
        }

        protected override TreeViewItem BuildRoot()
        {
            var root = new TreeViewItem { id = 0, depth = -1, displayName = "Root" };
            var allItems = new List<TreeViewItem>();

            for (int i = 0; i < m_AssetPaths.Count; i++)
            {
                var path = m_AssetPaths[i];
                var item = new AssetTreeViewItem(i + 1, 0, Path.GetFileName(path), path);
                allItems.Add(item);
            }

            SetupParentsAndChildrenFromDepths(root, allItems);

            return root;
        }

        protected override void RowGUI(RowGUIArgs args)
        {
            var item = args.item as AssetTreeViewItem;
            if (item == null) return;
            
            for (int i = 0; i < args.GetNumVisibleColumns(); ++i)
            {
                CellGUI(args.GetCellRect(i), item.assetPath, (Columns)args.GetColumn(i));
            }
        }

        void CellGUI(Rect cellRect, string assetPath, Columns column)
        {
            switch (column)
            {
                case Columns.Name:
                    var icon = AssetDatabase.GetCachedIcon(assetPath);
                    var iconRect = new Rect(cellRect.x, cellRect.y, cellRect.height, cellRect.height);
                    if (icon != null)
                        GUI.DrawTexture(iconRect, icon);
                    
                    var label = Path.GetFileName(assetPath);
                    EditorGUI.LabelField(new Rect(cellRect.x + cellRect.height, cellRect.y, cellRect.width - cellRect.height, cellRect.height), label);
                    break;
                    
                case Columns.Type:
                    EditorGUI.LabelField(cellRect, AssetDatabase.GetMainAssetTypeAtPath(assetPath)?.Name ?? "Unknown");
                    break;
                    
                case Columns.Size:
                    var fileInfo = new FileInfo(assetPath);
                    var size = fileInfo.Exists ? EditorUtility.FormatBytes(fileInfo.Length) : "Unknown";
                    EditorGUI.LabelField(cellRect, size);
                    break;
                    
                case Columns.Path:
                    EditorGUI.LabelField(cellRect, assetPath);
                    break;
            }
        }

        protected override void DoubleClickedItem(int id)
        {
            var assetPath = m_AssetPaths[id - 1];
            var asset = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(assetPath);
            if (asset != null)
            {
                Selection.activeObject = asset;
                EditorGUIUtility.PingObject(asset);
            }
        }

        protected override bool DoesItemMatchSearch(TreeViewItem item, string search)
        {
            if (string.IsNullOrEmpty(search))
                return true;

            var assetItem = item as AssetTreeViewItem;
            if (assetItem == null)
                return false;

            // 解析搜索字符串
            var searchParts = search.ToLower().Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
            var typeFilter = string.Empty;
            var nameFilter = new List<string>();

            // 分离类型筛选和名称筛选
            foreach (var part in searchParts)
            {
                if (part.StartsWith("t:"))
                {
                    typeFilter = part.Substring(2);
                }
                else
                {
                    nameFilter.Add(part);
                }
            }

            // 检查类型匹配
            if (!string.IsNullOrEmpty(typeFilter))
            {
                var assetType = AssetDatabase.GetMainAssetTypeAtPath(assetItem.assetPath)?.Name;
                if (assetType == null || !assetType.ToLower().Contains(typeFilter))
                    return false;
            }

            // 检查名称匹配
            if (nameFilter.Count > 0)
            {
                var fileName = Path.GetFileName(assetItem.assetPath).ToLower();
                return nameFilter.All(filter => fileName.Contains(filter));
            }

            return true;
        }

        protected override IList<TreeViewItem> BuildRows(TreeViewItem root)
        {
            if (string.IsNullOrEmpty(searchString))
            {
                return base.BuildRows(root);
            }

            var rows = new List<TreeViewItem>();
            foreach (var child in root.children)
            {
                if (DoesItemMatchSearch(child, searchString))
                {
                    rows.Add(child);
                }
            }
            return rows;
        }

        protected override void SearchChanged(string newSearch)
        {
            base.SearchChanged(newSearch);
            Reload();
        }
    }

    class AssetTreeViewItem : TreeViewItem
    {
        public string assetPath { get; }

        public AssetTreeViewItem(int id, int depth, string displayName, string assetPath) 
            : base(id, depth, displayName)
        {
            this.assetPath = assetPath;
        }
    }
} 