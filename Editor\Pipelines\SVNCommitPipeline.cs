using System;
using System.Collections.Generic;
using System.Linq;
using AssetPipeline.Config;
using UnityEditor;
using AssetPipeline.Core;
using AssetPipeline.Pipelines.Context;
using AssetPipeline.Processors;
using AssetPipeline.UI.Windows;
using Logger = AssetPipeline.Core.Logger;

namespace AssetPipeline.Pipelines
{
    /// <summary>
    /// SVN提交检查管线 
    /// </summary>
    public class SvnCommitPipeline
    {
        private const string PIPELINE_NAME = "svncommit";
        private readonly SvnCommitContext context;

        private SvnCommitPipeline(SvnCommitContext context)
        {
            this.context = context;
        }

        #region Public Static Entry Points
        
        /// <summary>
        /// 执行提交前检查，在打开SVN提交窗口前，由用户在编辑器内手动触发。
        /// </summary>
        /// <param name="commitPaths">所有待提交的文件路径</param>
        /// <returns>如果检查通过，返回true；否则返回false，并阻断提交。</returns>
        public static bool RunPreCommitCheck(string[] commitPaths)
        {
            return RunSvnPipeline(commitPaths, pipeline => pipeline.ExecutePreCommit());
        }
        
        /// <summary>
        /// 执行提交后操作，用来检查那些确认被提交的资源
        /// </summary>
        /// <param name="committedPaths">已成功提交的文件路径</param>
        public static bool RunPostCommit(string[] committedPaths)
        {
            return RunSvnPipeline(committedPaths, pipeline => pipeline.ExecutePostCommit());
        }
        
        private static bool RunSvnPipeline(string[] paths, Func<SvnCommitPipeline, bool> executeAction)
        {
            if (!AssetPipelineConfig.IsPipelineEnabled(PIPELINE_NAME))
            {
                Logger.Debug(LogModule.Pipeline, $"[{PIPELINE_NAME}] Pipeline disabled, operation allowed.");
                return true;
            }

            var validAssetPaths = GetValidAssetPaths(paths);
            if (!validAssetPaths.Any())
            {
                Logger.Debug(LogModule.Pipeline, $"[{PIPELINE_NAME}] No valid assets to check.");
                return true;
            }
            
            var commitContext = new SvnCommitContext(validAssetPaths);
            var pipeline = new SvnCommitPipeline(commitContext);
            var success = executeAction(pipeline);
            commitContext.Complete();
            return success;
        }

        #endregion

        #region Instance-based Execution

        private bool ExecutePreCommit()
        {
            try
            {
                EditorUtility.DisplayProgressBar("SVN Commit Check", "Running pre-commit checks...", 0.1f);

                // 一次性执行所有处理
                var resultsByPath = PipelineHelper.ExecuteProcessors<ISvnPreCommitProcessor>(
                    context.CommitPaths,
                    (processor, path) => processor.OnPreCommit(path, context)
                );

                // 统一添加结果
                context.Results.AddRange(resultsByPath.SelectMany(kvp => kvp.Value));

                return ProcessResults();
            }
            catch (Exception e)
            {
                Logger.Error(LogModule.Pipeline, $"[{PIPELINE_NAME}] An exception occurred during pre-commit check: {e.Message}");
                ShowCommitErrorDialog("Commit Check Failed", $"An unexpected error occurred: {e.Message}\n\nCheck logs for details.");
                return false; 
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }
        }
        
        private bool ExecutePostCommit()
        {
            Logger.Debug(LogModule.Pipeline, $"[{PIPELINE_NAME}] Running post-commit actions for {context.CommitPaths.Count} assets.");
            
            // 一次性执行所有处理
            var resultsByPath = PipelineHelper.ExecuteProcessors<ISvnPostCommitProcessor>(
                context.CommitPaths,
                (processor, assetPath) => processor.OnPostCommit(assetPath, context)
            );

            // 统一添加结果
            context.Results.AddRange(resultsByPath.SelectMany(kvp => kvp.Value));
            
            return ProcessResults();
        }
        
        #endregion

        #region Helpers

        private static List<string> GetValidAssetPaths(string[] paths)
        {
            return paths?.Where(p => !string.IsNullOrEmpty(p) && p.StartsWith("Assets/") && !p.EndsWith(".meta")).ToList() 
                   ?? new List<string>();
        }
        
        private bool ProcessResults()
        {
            if (context.Results.HasBlockingResults(ProcessContext.Commit))
            {
                var blockingResults = context.Results.GetBlockingResults(ProcessContext.Commit).ToList();
                Logger.Error(LogModule.Pipeline, $"[{PIPELINE_NAME}] Commit blocked. Found {blockingResults.Count} blocking results.");
                ShowCommitBlockedResults(blockingResults);
                return false;
            }
            
            var warnings = context.Results.GetByType(CheckResultType.Warning)
                                  .Where(r => !r.ShouldBlock(ProcessContext.Commit))
                                  .ToList();
            if (warnings.Any() && AssetPipelineSettings.ShowCommitWarnings)
            {
                 ShowCommitWarningResults(warnings);
            }

            return true;
        }

        private static void ShowCommitBlockedResults(List<CheckResult> blockingResults)
        {
            CheckResultWindow.ShowResults("Commit Blocked", blockingResults);
        }

        private static void ShowCommitWarningResults(List<CheckResult> warningResults)
        {
            CheckResultWindow.ShowResults("Commit Warnings", warningResults);
        }

        private static void ShowCommitErrorDialog(string title, string message)
        {
            EditorUtility.DisplayDialog(title, message, "OK");
        }
        
        #endregion
    }
} 