using AssetPipeline.Core;
using System.Collections.Generic;
using AssetPipeline.Pipelines;

namespace AssetPipeline.Processors
{
    /// <summary>
    /// 为主动检查管线（如手动触发或CI流程）提供处理能力的接口。
    /// 实现此接口的处理器能够对指定的资源执行按需检查。
    /// </summary>
    public interface ICheckRunnerProcessor : IProcessor
    {
        /// <summary>
        /// 对单个资源执行检查。
        /// </summary>
        /// <param name="path">要检查的资源路径。</param>
        /// <param name="context">本次检查任务的上下文。</param>
        /// <returns>包含检查结果的对象。</returns>
        IEnumerable<CheckResult> RunCheck(string path, CheckRunnerContext context);
    }
} 