using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using AssetPipeline.Core;
using UnityEngine;
using Logger = AssetPipeline.Core.Logger;

namespace AssetPipeline.Config
{
    /// <summary>
    /// 资产类型标志枚举
    /// None: 默认初始值
    /// Other: 自定义扩展名开关，勾选后才能配置自定义扩展名
    /// </summary>
    [Flags]
    public enum AssetTypeFlag
    {
        None = 0,
        Texture = 1 << 0,
        Model = 1 << 1,
        Audio = 1 << 2,
        Video = 1 << 3,
        Material = 1 << 4,
        Shader = 1 << 5,
        Script = 1 << 6,
        Prefab = 1 << 7,
        Scene = 1 << 8,
        Animation = 1 << 9,
        Font = 1 << 10,
        Text = 1 << 11,
        Other = 1 << 12,
        All = ~0
    }

    /// <summary>
    /// 资产类型定义
    /// </summary>
    [System.Serializable]
    public class AssetTypeDefinition
    {
        [Header("基本信息")]
        public string name = "";
        public AssetTypeFlag typeFlag = AssetTypeFlag.Other;
        
        [Header("文件扩展名")]
        public List<string> extensions = new List<string>();

        /// <summary>
        /// 检查扩展名是否匹配
        /// </summary>
        public bool MatchesExtension(string extension)
        {
            if (string.IsNullOrEmpty(extension))
                return false;
                
            var normalizedExt = extension.ToLower();
            if (!normalizedExt.StartsWith("."))
                normalizedExt = "." + normalizedExt;
                
            return extensions.Any(ext => 
                string.Equals(ext.ToLower(), normalizedExt, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 检查资源路径是否匹配此类型
        /// </summary>
        public bool MatchesAsset(string assetPath)
        {
            if (string.IsNullOrEmpty(assetPath))
                return false;
                
            var extension = Path.GetExtension(assetPath);
            return MatchesExtension(extension);
        }
    }

    /// <summary>
    /// 资产类型
    /// 
    /// 提供资产类型判断的静态方法
    /// </summary>
    public static class AssetType
    {
        /// <summary>
        /// 根据资源路径获取资产类型标志
        /// </summary>
        public static AssetTypeFlag GetAssetTypeFlag(string assetPath)
        {
            if (string.IsNullOrEmpty(assetPath))
                return AssetTypeFlag.None;

            var extension = Path.GetExtension(assetPath).ToLower();

            // 首先尝试从配置中获取
            var assetTypeDefinitions = AssetPipelineConfig.AssetTypeDefinitions;
            if (assetTypeDefinitions != null)
            {
                foreach (var assetType in assetTypeDefinitions)
                {
                    if (assetType.MatchesExtension(extension))
                    {
                        return assetType.typeFlag;
                    }
                }
            }
            return AssetTypeFlag.Other;
        }
        
        /// <summary>
        /// 默认资产类型定义
        /// </summary>
        public static List<AssetTypeDefinition> CreateDefault()
        {
            return  new List<AssetTypeDefinition>
            {
                new AssetTypeDefinition
                {
                    name = "Texture",
                    typeFlag = AssetTypeFlag.Texture,
                    extensions = new List<string> { 
                        ".png", ".jpg", ".jpeg", ".tga", ".bmp", ".tiff", ".gif", 
                        ".psd", ".exr", ".hdr", ".dds", ".pvr", ".ktx", ".astc" 
                    },
                },
                new AssetTypeDefinition
                {
                    name = "Model",
                    typeFlag = AssetTypeFlag.Model,
                    extensions = new List<string> { 
                        ".fbx", ".obj", ".dae", ".3ds", ".blend", ".ma", ".mb", 
                        ".max", ".c4d", ".lwo", ".lws", ".dxf", ".ply" 
                    },
                },
                new AssetTypeDefinition
                {
                    name = "Audio",
                    typeFlag = AssetTypeFlag.Audio,
                    extensions = new List<string> { 
                        ".wav", ".mp3", ".ogg", ".aiff", ".aif", ".flac", 
                        ".mod", ".it", ".s3m", ".xm", ".m4a", ".wma" 
                    },
                },
                new AssetTypeDefinition
                {
                    name = "Video",
                    typeFlag = AssetTypeFlag.Video,
                    extensions = new List<string> { 
                        ".mov", ".mpg", ".mpeg", ".mp4", ".avi", ".asf", 
                        ".wmv", ".flv", ".webm", ".ogv", ".mkv" 
                    },
                },
                new AssetTypeDefinition
                {
                    name = "Material",
                    typeFlag = AssetTypeFlag.Material,
                    extensions = new List<string> { ".mat" },
                },
                new AssetTypeDefinition
                {
                    name = "Shader",
                    typeFlag = AssetTypeFlag.Shader,
                    extensions = new List<string> { 
                        ".shader", ".cginc", ".hlsl", ".glsl", ".compute" 
                    },
                },
                new AssetTypeDefinition
                {
                    name = "Script",
                    typeFlag = AssetTypeFlag.Script,
                    extensions = new List<string> { ".cs", ".lua" },
                },
                new AssetTypeDefinition
                {
                    name = "Prefab",
                    typeFlag = AssetTypeFlag.Prefab,
                    extensions = new List<string> { ".prefab" },
                },
                new AssetTypeDefinition
                {
                    name = "Scene",
                    typeFlag = AssetTypeFlag.Scene,
                    extensions = new List<string> { ".unity" },
                },
                new AssetTypeDefinition
                {
                    name = "Animation",
                    typeFlag = AssetTypeFlag.Animation,
                    extensions = new List<string> { ".anim", ".controller" },
                },
                new AssetTypeDefinition
                {
                    name = "Font",
                    typeFlag = AssetTypeFlag.Font,
                    extensions = new List<string> { ".ttf", ".otf", ".dfont", ".fon", ".fnt" },
                },
                new AssetTypeDefinition
                {
                    name = "Text",
                    typeFlag = AssetTypeFlag.Text,
                    extensions = new List<string> { 
                        ".txt", ".json", ".xml", ".csv", ".asset", ".bytes",
                        ".yaml", ".yml", ".ini", ".cfg", ".conf" },
                },
            };
        }
    }
} 