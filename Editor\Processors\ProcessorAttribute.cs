using System;

namespace AssetPipeline.Processors
{
    /// <summary>
    /// 处理器描述属性 - 提供处理器元数据
    /// </summary>
    [AttributeUsage(AttributeTargets.Class, AllowMultiple = false)]
    public class ProcessorAttribute : Attribute
    {
        public string DisplayName { get; }
        public uint Version { get; }
        public string Author { get; }
        public string Description { get; }
        public string Category { get; }
        
        public ProcessorAttribute(string displayName, string description = "", uint version = 1, string category = "General", string author = "")
        {
            DisplayName = displayName;
            Version = version;
            Author = author;
            Description = description;
            Category = category;
        }
    }
} 