using System;
using System.Text.RegularExpressions;
using UnityEngine;

namespace AssetPipeline.Core
{
    /// <summary>
    /// 统一的路径匹配组件
    /// </summary>
    [System.Serializable]
    public class PathMatcher
    {
        public enum MatchMode
        {
            /// <summary>完全匹配</summary>
            Equals,
            /// <summary>包含</summary>
            Contains,
            /// <summary>开始于</summary>
            StartsWith,
            /// <summary>结束于</summary>
            EndsWith,
            /// <summary>通配符 (*, ?)</summary>
            Wildcard,
            /// <summary>正则表达式</summary>
            Regex
        }

        [Header("匹配配置")]
        [SerializeField] private MatchMode mode = MatchMode.Contains;
        [SerializeField] private string pattern = "";
        [SerializeField] private bool ignoreCase = true;

        public MatchMode Mode => mode;
        public string Pattern => pattern;
        public bool IgnoreCase => ignoreCase;

        // 统一的正则表达式缓存（支持正则和通配符模式）
        private Regex _cachedRegex;
        private string _cachedPattern;
        private bool _cachedIgnoreCase;
        private MatchMode _cachedMode;

        #region 构造函数

        public PathMatcher() : this(MatchMode.Contains, "")
        {
        }

        public PathMatcher(MatchMode mode, string pattern, bool ignoreCase = true)
        {
            this.mode = mode;
            this.pattern = NormalizePath(pattern ?? "");
            this.ignoreCase = ignoreCase;
        }

        #endregion

        #region 核心匹配API

        /// <summary>
        /// 主要匹配方法
        /// </summary>
        public bool IsMatch(string path)
        {
            if (string.IsNullOrEmpty(path) || string.IsNullOrEmpty(pattern))
                return false;

            // 标准化路径
            var normalizedPath = NormalizePath(path);
            var normalizedPattern = pattern; // pattern在OnValidate中已经标准化

            switch (mode)
            {
                case MatchMode.Equals:
                    return string.Equals(normalizedPath, normalizedPattern, GetStringComparison());

                case MatchMode.Contains:
                    return normalizedPath.IndexOf(normalizedPattern, GetStringComparison()) >= 0;

                case MatchMode.StartsWith:
                    return normalizedPath.StartsWith(normalizedPattern, GetStringComparison());

                case MatchMode.EndsWith:
                    return normalizedPath.EndsWith(normalizedPattern, GetStringComparison());

                case MatchMode.Wildcard:
                    return WildcardMatch(normalizedPath, normalizedPattern);

                case MatchMode.Regex:
                    return RegexMatch(normalizedPath, normalizedPattern);

                default:
                    return false;
            }
        }

        /// <summary>
        /// 通配符匹配（支持 * 和 ? ）- 统一缓存版本
        /// </summary>
        private bool WildcardMatch(string input, string pattern)
        {
            try
            {
                // 检查统一缓存是否有效（通配符模式）
                if (_cachedRegex == null ||
                    _cachedPattern != pattern ||
                    _cachedIgnoreCase != ignoreCase ||
                    _cachedMode != MatchMode.Wildcard)
                {
                    // 转换通配符为正则表达式
                    var regexPattern = "^" + Regex.Escape(pattern)
                        .Replace("\\*", ".*")
                        .Replace("\\?", ".") + "$";

                    var options = GetRegexOptions() | RegexOptions.Compiled;
                    _cachedRegex = new Regex(regexPattern, options);
                    _cachedPattern = pattern;
                    _cachedIgnoreCase = ignoreCase;
                    _cachedMode = MatchMode.Wildcard;
                }

                return _cachedRegex.IsMatch(input);
            }
            catch (Exception e)
            {
                Debug.LogError($"[PathMatcher] 通配符匹配失败: {pattern} - {e.Message}");
                return false;
            }
        }

        /// <summary>
        /// 正则表达式匹配（统一缓存版本）
        /// </summary>
        private bool RegexMatch(string input, string pattern)
        {
            try
            {
                // 检查统一缓存是否有效（正则模式）
                if (_cachedRegex == null ||
                    _cachedPattern != pattern ||
                    _cachedIgnoreCase != ignoreCase ||
                    _cachedMode != MatchMode.Regex)
                {
                    // 重新编译正则表达式
                    var options = GetRegexOptions() | RegexOptions.Compiled;
                    _cachedRegex = new Regex(pattern, options);
                    _cachedPattern = pattern;
                    _cachedIgnoreCase = ignoreCase;
                    _cachedMode = MatchMode.Regex;
                }

                return _cachedRegex.IsMatch(input);
            }
            catch (Exception e)
            {
                Debug.LogError($"[PathMatcher] 正则表达式匹配失败: {pattern} - {e.Message}");
                return false;
            }
        }

        public PathMatcher Clone()
        {
            return new PathMatcher(mode, pattern, ignoreCase);
        }

        /// <summary>
        /// 清理缓存 - 配置变化时调用
        /// </summary>
        public void ClearCache()
        {
            _cachedRegex = null;
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 路径标准化处理
        /// </summary>
        public static string NormalizePath(string path)
        {
            if (string.IsNullOrEmpty(path))
                return path;

            // 统一使用正斜杠，移除重复的斜杠
            return path.Replace('\\', '/').Replace("//", "/");
        }

        /// <summary>
        /// 获取字符串比较方式
        /// </summary>
        private StringComparison GetStringComparison()
        {
            return ignoreCase ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal;
        }

        /// <summary>
        /// 获取正则表达式选项
        /// </summary>
        private RegexOptions GetRegexOptions()
        {
            return ignoreCase ? RegexOptions.IgnoreCase : RegexOptions.None;
        }

        #endregion

        #region 配置验证

        /// <summary>
        /// 验证配置有效性
        /// </summary>
        public bool ValidateConfiguration()
        {
            if (string.IsNullOrEmpty(pattern))
            {
                Debug.LogWarning("[PathMatcher] 匹配模式为空");
                return false;
            }

            // 验证正则表达式
            if (mode == MatchMode.Regex)
            {
                try
                {
                    var options = GetRegexOptions();
                    new Regex(pattern, options);
                }
                catch (Exception e)
                {
                    Debug.LogError($"[PathMatcher] 正则表达式无效: {pattern} - {e.Message}");
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 获取匹配器描述（用于调试和UI显示）
        /// </summary>
        public string GetDescription()
        {
            if (string.IsNullOrEmpty(pattern))
                return "空匹配器";

            var caseDesc = ignoreCase ? "忽略大小写" : "区分大小写";
            switch (mode)
            {
                case MatchMode.Equals:
                    return $"完全匹配 '{pattern}' ({caseDesc})";
                case MatchMode.Contains:
                    return $"包含 '{pattern}' ({caseDesc})";
                case MatchMode.StartsWith:
                    return $"开始于 '{pattern}' ({caseDesc})";
                case MatchMode.EndsWith:
                    return $"结束于 '{pattern}' ({caseDesc})";
                case MatchMode.Wildcard:
                    return $"通配符 '{pattern}' ({caseDesc})";
                case MatchMode.Regex:
                    return $"正则表达式 '{pattern}' ({caseDesc})";
                default:
                    return $"未知模式 '{pattern}'";
            }
        }

        #endregion

        #region Unity生命周期

        void OnValidate()
        {
            if (!string.IsNullOrEmpty(pattern))
            {
                pattern = NormalizePath(pattern);
            }

            // 清理正则表达式缓存
            ClearCache();

            ValidateConfiguration();
        }

        #endregion
    }
} 