using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;
using UnityEditor;
using UnityEditor.Presets;
using AssetPipeline.Core;
using AssetPipeline.Pipelines;

namespace AssetPipeline.Processors
{
    /// <summary>
    /// 统一的Preset处理器 - 合并版本
    /// 提供Unity原生操作体验，支持选择性属性限制
    /// 现实场景：大型项目中统一资产导入设置的核心工具
    /// </summary>
    [CreateAssetMenu(fileName = "PresetProcessor", menuName = "Asset Pipeline/Processors/Preset Processor")]
    [ProcessorAttribute("Preset处理器", "基于Unity Preset系统统一资产导入设置")]
    public class PresetProcessor : ImportProcessor
    {
        #region 配置枚举

        /// <summary>
        /// 处理模式：锁死模式 vs 预设模式
        /// </summary>
        public enum ProcessingMode
        {
            /// <summary>
            /// 预设模式：仅在首次导入时应用预设，后续保留用户修改
            /// 现实场景：新手友好，允许有经验美术自定义设置
            /// </summary>
            PresetMode,
            
            /// <summary>
            /// 锁死模式：强制应用预设，覆盖任何用户修改
            /// 现实场景：项目后期性能优化，强制统一规范
            /// </summary>
            LockMode
        }

        #endregion

        #region 核心配置

        [Header("基础配置")]
        [SerializeField]
        [Tooltip("处理模式：PresetMode仅首次导入应用，LockMode强制覆盖用户设置")]
        private ProcessingMode processingMode = ProcessingMode.PresetMode;

        [SerializeField]
        [Tooltip("要应用的Unity Preset资产")]
        private Preset targetPreset;

        [Header("生产环境灵活性控制")]
        [SerializeField]
        [Tooltip("允许Generate Mip Maps设置的灵活性")]
        private bool allowMipMapsFlexibility = false;

        [SerializeField]
        [Tooltip("允许Read/Write Enabled设置的灵活性")]
        private bool allowReadWriteFlexibility = false;

        [SerializeField]
        [Tooltip("允许平台特定的Max Texture Size设置灵活性")]
        private bool allowPlatformTextureSizeFlexibility = false;

        [SerializeField]
        [Tooltip("允许平台特定的Texture Format/Compression设置灵活性")]
        private bool allowPlatformTextureFormatFlexibility = false;

        [Header("高级选项")]
        [SerializeField]
        [Tooltip("是否在控制台输出详细日志")]
        private bool enableDetailedLogging = false;

        [SerializeField]
        [Tooltip("保留原始userData")]
        private bool preserveUserData = true;

        #endregion

        #region 私有字段和缓存

        // 属性映射缓存（性能优化）
        private Dictionary<string, string> presetPropertyCache;
        private HashSet<string> restrictedPropertySet;
        private bool cacheInitialized = false;

        // 平台设置缓存
        private List<PlatformSettingsManager.PlatformSetting> platformSettings;
        private bool platformCacheInitialized = false;

        #endregion

        #region 属性访问器

        /// <summary>
        /// 当前处理模式
        /// </summary>
        public ProcessingMode Mode => processingMode;

        /// <summary>
        /// 目标预设
        /// </summary>
        public Preset TargetPreset => targetPreset;

        /// <summary>
        /// 是否允许Mip Maps灵活性
        /// </summary>
        public bool AllowMipMapsFlexibility => allowMipMapsFlexibility;

        /// <summary>
        /// 是否允许Read/Write灵活性
        /// </summary>
        public bool AllowReadWriteFlexibility => allowReadWriteFlexibility;

        /// <summary>
        /// 是否允许平台纹理尺寸灵活性
        /// </summary>
        public bool AllowPlatformTextureSizeFlexibility => allowPlatformTextureSizeFlexibility;

        /// <summary>
        /// 是否允许平台纹理格式灵活性
        /// </summary>
        public bool AllowPlatformTextureFormatFlexibility => allowPlatformTextureFormatFlexibility;

        /// <summary>
        /// 是否仅首次导入处理
        /// 根据处理模式动态决定
        /// </summary>
        public override bool OnlyFirstImport => processingMode == ProcessingMode.PresetMode;

        #endregion

        #region 缓存管理系统

        /// <summary>
        /// 初始化性能缓存（生产环境模式）
        /// </summary>
        private void InitializeCache()
        {
            if (cacheInitialized || targetPreset == null)
                return;

            presetPropertyCache = new Dictionary<string, string>();

            // 预构建属性映射，只遍历一次PropertyModifications
            var modifications = targetPreset.PropertyModifications;
            foreach (var modification in modifications)
            {
                presetPropertyCache[modification.propertyPath] = modification.value;
            }

            // 构建限制属性集合（默认锁定所有，根据灵活性设置排除特定属性）
            BuildRestrictedPropertySet();

            cacheInitialized = true;
            LogInfo($"属性缓存已初始化: {presetPropertyCache.Count} 个预设属性, {restrictedPropertySet.Count} 个锁定属性");
        }

        /// <summary>
        /// 构建限制属性集合（生产环境模式）
        /// </summary>
        private void BuildRestrictedPropertySet()
        {
            restrictedPropertySet = new HashSet<string>();

            // 默认锁定所有预设属性
            foreach (var propertyPath in presetPropertyCache.Keys)
            {
                bool shouldLock = true;

                // 检查是否应该给予灵活性
                if (IsFlexibleProperty(propertyPath))
                {
                    shouldLock = false;
                }

                if (shouldLock)
                {
                    restrictedPropertySet.Add(propertyPath);
                }
            }
        }

        /// <summary>
        /// 检查属性是否应该具有灵活性（生产环境常用属性）
        /// </summary>
        private bool IsFlexibleProperty(string propertyPath)
        {
            // Mip Maps相关属性
            if (allowMipMapsFlexibility)
            {
                if (propertyPath == "m_MipMapMode" ||           // Generate Mip Maps
                    propertyPath == "m_MipMapFadeDistanceStart" || // Fadeout Mip Maps
                    propertyPath == "m_MipMapFadeDistanceEnd")     // Fadeout Mip Maps
                    return true;
            }

            // Read/Write相关属性
            if (allowReadWriteFlexibility)
            {
                if (propertyPath == "m_IsReadable")             // Read/Write Enabled
                    return true;
            }

            // 纹理基础属性（可能需要根据项目需求调整）
            // 注意：这些属性通常应该保持锁定，只在特殊情况下开放
            // if (allowAdvancedTextureFlexibility) // 如果将来需要更多灵活性
            // {
            //     if (propertyPath == "m_TextureType" ||        // Texture Type
            //         propertyPath == "m_FilterMode" ||         // Filter Mode
            //         propertyPath == "m_WrapU" ||              // Wrap Mode U
            //         propertyPath == "m_WrapV" ||              // Wrap Mode V
            //         propertyPath == "m_WrapW" ||              // Wrap Mode W
            //         propertyPath == "m_sRGBTexture" ||        // sRGB
            //         propertyPath == "m_AlphaSource" ||        // Alpha Source
            //         propertyPath == "m_AlphaIsTransparency")  // Alpha Is Transparency
            //         return true;
            // }

            return false;
        }

        /// <summary>
        /// 初始化平台设置缓存
        /// </summary>
        private void InitializePlatformCache(AssetImporter importer)
        {
            if (platformCacheInitialized || !(importer is TextureImporter textureImporter))
                return;

            try
            {
                // 获取当前平台设置
                platformSettings = PlatformSettingsManager.GetTexturePlatformSettings(textureImporter);

                // 从Preset中提取平台设置的期望值
                if (targetPreset != null)
                {
                    ExtractPlatformSettingsFromPreset();
                }

                platformCacheInitialized = true;
                LogInfo($"平台设置缓存已初始化: {platformSettings?.Count ?? 0} 个平台配置");
            }
            catch (Exception ex)
            {
                LogWarning($"平台缓存初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从Preset中提取平台设置
        /// </summary>
        private void ExtractPlatformSettingsFromPreset()
        {
            if (targetPreset == null || platformSettings == null)
                return;

            // 创建临时导入器来获取Preset的平台设置
            try
            {
                var tempPath = "Assets/temp_preset_extraction.png";
                var tempTexture = new Texture2D(1, 1);
                var tempBytes = tempTexture.EncodeToPNG();
                System.IO.File.WriteAllBytes(tempPath, tempBytes);
                AssetDatabase.Refresh();

                var tempImporter = AssetImporter.GetAtPath(tempPath) as TextureImporter;
                if (tempImporter != null)
                {
                    // 应用Preset到临时导入器
                    targetPreset.ApplyTo(tempImporter);

                    // 提取平台设置作为期望值
                    var presetPlatformSettings = PlatformSettingsManager.GetTexturePlatformSettings(tempImporter);

                    // 将期望值映射到当前平台设置
                    foreach (var currentSetting in platformSettings)
                    {
                        var presetSetting = presetPlatformSettings.Find(p =>
                            p.platformName == currentSetting.platformName &&
                            p.propertyName == currentSetting.propertyName);

                        if (presetSetting != null)
                        {
                            currentSetting.expectedValue = presetSetting.currentValue;
                        }
                    }
                }

                // 清理临时文件
                AssetDatabase.DeleteAsset(tempPath);
                UnityEngine.Object.DestroyImmediate(tempTexture);
            }
            catch (Exception ex)
            {
                LogWarning($"从Preset提取平台设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除缓存（当配置变更时调用）
        /// </summary>
        public override void ClearCache()
        {
            presetPropertyCache?.Clear();
            restrictedPropertySet?.Clear();
            cacheInitialized = false;

            platformSettings?.Clear();
            platformCacheInitialized = false;
        }

        /// <summary>
        /// 检查属性是否应该被锁定（生产环境模式）
        /// </summary>
        private bool ShouldLockProperty(string propertyPath)
        {
            // 生产环境模式：默认锁定所有，只有明确标记为灵活的属性才不锁定
            return restrictedPropertySet.Contains(propertyPath);
        }

        /// <summary>
        /// 从缓存中获取属性的预设值
        /// </summary>
        private string GetPresetPropertyValue(string propertyPath)
        {
            presetPropertyCache.TryGetValue(propertyPath, out string value);
            return value;
        }

        #endregion

        #region 主要处理流程

        /// <summary>
        /// 预处理入口：应用Preset设置（优化版本 - 单次遍历）
        /// </summary>
        public override IEnumerable<CheckResult> OnPreprocessAsset(AssetImporter importer, ImportContext context)
        {
            if (processingMode == ProcessingMode.PresetMode)
            {
                InitializeCache();
                if (ValidatePrerequisites(importer, context))
                {
                    var result = ApplyPresetToImporter(importer, context);
                    if (result != null)
                        yield return result;
                }
            }
        }

        /// <summary>
        /// 后处理入口：验证最终设置（优化版本）
        /// </summary>
        public override IEnumerable<CheckResult> OnPostprocessAsset(AssetImporter importer, ImportContext context)
        {
            if (processingMode == ProcessingMode.LockMode)
            {
                InitializeCache();
                if (ValidatePrerequisites(importer, context))
                {
                    var result = ExecuteLockModeProcessing(importer, context);
                    if (result != null)
                        yield return result;
                }
            }
        }

        /// <summary>
        /// 执行锁定模式处理：强制应用预设设置
        /// </summary>
        private CheckResult ExecuteLockModeProcessing(AssetImporter importer, ImportContext context)
        {
            if (!cacheInitialized || targetPreset == null)
                return null;

            try
            {
                var beforeSettings = GetImporterSettingsSummary(importer);

                // 使用统一的属性应用方法
                var result = ApplyProperties(importer, restrictedPropertySet, "Lock mode");

                if (result.appliedCount > 0)
                {
                    var afterSettings = GetImporterSettingsSummary(importer);

                    LogDetailed($"Lock mode applied: {importer.assetPath}\n" +
                               $"  Checked: {result.checkedCount}, Applied: {result.appliedCount}\n" +
                               $"  Before: {beforeSettings}\n" +
                               $"  After: {afterSettings}");

                    return CheckResult.Info($"Lock mode settings applied: {result.appliedCount} properties")
                        .WithAssetPath(importer.assetPath);
                }
                else
                {
                    LogDetailed($"Lock mode compliant: {importer.assetPath} (checked {result.checkedCount} properties)");
                    return null;
                }
            }
            catch (Exception ex)
            {
                return CheckResult.Error($"Lock mode processing failed: {ex.Message}")
                    .WithAssetPath(importer.assetPath);
            }
        }

        /// <summary>
        /// 统一的属性应用结果
        /// </summary>
        private struct PropertyApplicationResult
        {
            public int checkedCount;
            public int appliedCount;
            public bool needsUpdate;
        }

        /// <summary>
        /// 统一的属性应用方法（消除代码重复）
        /// </summary>
        private PropertyApplicationResult ApplyProperties(AssetImporter importer, HashSet<string> propertiesToApply, string operationName)
        {
            var result = new PropertyApplicationResult();

            if (!cacheInitialized || targetPreset == null || propertiesToApply.Count == 0)
                return result;

            try
            {
                var importerSO = new SerializedObject(importer);

                // 单次遍历：检查和应用属性
                foreach (var propertyPath in propertiesToApply)
                {
                    var presetValue = GetPresetPropertyValue(propertyPath);
                    if (presetValue == null)
                        continue;

                    var property = importerSO.FindProperty(propertyPath);
                    if (property != null)
                    {
                        result.checkedCount++;
                        var currentValue = property.GetPropertyValueAsString();

                        if (currentValue != presetValue)
                        {
                            ApplyPropertyValue(property, presetValue);
                            result.appliedCount++;
                            result.needsUpdate = true;
                        }
                    }
                }

                if (result.needsUpdate)
                {
                    // 保存原始userData
                    var originalUserData = preserveUserData ? importer.userData : null;

                    importerSO.ApplyModifiedProperties();

                    // 恢复userData
                    if (preserveUserData && originalUserData != null)
                    {
                        importer.userData = originalUserData;
                    }

                    LogDetailed($"{operationName} 完成: 检查 {result.checkedCount} 个属性, 应用 {result.appliedCount} 个更改");
                }

                // 应用平台设置（如果适用）
                ApplyPlatformSettingsIfNeeded(importer, operationName, ref result);

                return result;
            }
            catch (Exception ex)
            {
                LogError($"{operationName} 属性应用失败: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 应用平台设置（如果需要）
        /// </summary>
        private void ApplyPlatformSettingsIfNeeded(AssetImporter importer, string operationName, ref PropertyApplicationResult result)
        {
            if (!(importer is TextureImporter textureImporter))
                return;

            try
            {
                // 初始化平台缓存
                InitializePlatformCache(importer);

                if (platformSettings == null || platformSettings.Count == 0)
                    return;

                // 检查并应用平台设置
                var platformSettingsToApply = new List<PlatformSettingsManager.PlatformSetting>();

                foreach (var setting in platformSettings)
                {
                    if (setting.expectedValue != null)
                    {
                        bool shouldApply = true;

                        // 检查平台设置的灵活性配置
                        if (IsPlatformSettingFlexible(setting))
                        {
                            shouldApply = false;
                        }

                        if (shouldApply)
                        {
                            // 检查当前值是否与期望值不同
                            if (!PlatformSettingsManager.AreValuesEqual(setting.currentValue, setting.expectedValue))
                            {
                                platformSettingsToApply.Add(setting);
                            }
                        }
                    }
                }

                if (platformSettingsToApply.Count > 0)
                {
                    var applied = PlatformSettingsManager.ApplyTexturePlatformSettings(textureImporter, platformSettingsToApply);
                    if (applied)
                    {
                        result.appliedCount += platformSettingsToApply.Count;
                        result.needsUpdate = true;

                        LogDetailed($"{operationName} 应用了 {platformSettingsToApply.Count} 个平台设置");
                    }
                }
            }
            catch (Exception ex)
            {
                LogWarning($"{operationName} 平台设置应用失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查平台设置是否应该具有灵活性
        /// </summary>
        private bool IsPlatformSettingFlexible(PlatformSettingsManager.PlatformSetting setting)
        {
            // Max Texture Size 灵活性
            if (allowPlatformTextureSizeFlexibility && setting.propertyName == "maxTextureSize")
                return true;

            // Texture Format/Compression 灵活性
            if (allowPlatformTextureFormatFlexibility &&
                (setting.propertyName == "textureFormat" ||
                 setting.propertyName == "compressionQuality" ||
                 setting.propertyName == "crunchedCompression"))
                return true;

            return false;
        }

        #endregion

        #region 属性应用和验证系统

        /// <summary>
        /// 验证前置条件
        /// </summary>
        private bool ValidatePrerequisites(AssetImporter importer, ImportContext context)
        {
            if (importer == null)
            {
                Log(LogLevel.Warning, "AssetImporter为空，跳过处理");
                return false;
            }

            if (targetPreset == null)
            {
                Log(LogLevel.Warning, "未配置目标Preset，跳过处理");
                return false;
            }

            if (!IsCompatibleImporter(importer))
            {
                LogDetailed($"导入器类型不匹配: {importer.GetType().Name}");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 检查导入器兼容性
        /// </summary>
        private bool IsCompatibleImporter(AssetImporter importer)
        {
            if (targetPreset == null || importer == null)
                return false;

            return targetPreset.CanBeAppliedTo(importer);
        }

        /// <summary>
        /// 应用Preset到导入器 - 支持选择性限制
        /// </summary>
        private CheckResult ApplyPresetToImporter(AssetImporter importer, ImportContext context)
        {
            try
            {
                var beforeSettings = GetImporterSettingsSummary(importer);
                
                // 保存原始userData
                var originalUserData = preserveUserData ? importer.userData : null;
                
                // 应用Preset（生产环境模式：默认完整应用）
                bool success = targetPreset.ApplyTo(importer);
                
                if (!success)
                {
                    return CheckResult.Error($"Preset应用失败: {importer.assetPath}")
                        .WithAssetPath(importer.assetPath);
                }

                // 恢复userData
                if (preserveUserData && originalUserData != null)
                {
                    importer.userData = originalUserData;
                }

                var afterSettings = GetImporterSettingsSummary(importer);
                
                LogDetailed($"Preset应用成功: {importer.assetPath}\n" +
                           $"  模式: {processingMode}\n" +
                           $"  灵活性配置: MipMaps={allowMipMapsFlexibility}, ReadWrite={allowReadWriteFlexibility}, PlatformSize={allowPlatformTextureSizeFlexibility}, PlatformFormat={allowPlatformTextureFormatFlexibility}\n" +
                           $"  变更前: {beforeSettings}\n" +
                           $"  变更后: {afterSettings}");

                return CheckResult.Info($"已应用{processingMode}模式Preset设置")
                    .WithAssetPath(importer.assetPath);
            }
            catch (Exception ex)
            {
                return CheckResult.Error($"Preset应用异常: {ex.Message}")
                    .WithAssetPath(importer.assetPath);
            }
        }



        /// <summary>
        /// 应用属性值（高性能版本）
        /// </summary>
        private void ApplyPropertyValue(SerializedProperty property, string value)
        {
            switch (property.propertyType)
            {
                case SerializedPropertyType.Boolean:
                    property.boolValue = value == "1";
                    break;
                case SerializedPropertyType.Integer:
                    if (int.TryParse(value, out int intValue))
                        property.intValue = intValue;
                    break;
                case SerializedPropertyType.Float:
                    if (float.TryParse(value, out float floatValue))
                        property.floatValue = floatValue;
                    break;
                case SerializedPropertyType.String:
                    property.stringValue = value;
                    break;
                case SerializedPropertyType.Enum:
                    if (int.TryParse(value, out int enumValue))
                        property.enumValueIndex = enumValue;
                    break;
                case SerializedPropertyType.Vector2:
                    if (TryParseVector2(value, out Vector2 v2))
                        property.vector2Value = v2;
                    break;
                case SerializedPropertyType.Vector3:
                    if (TryParseVector3(value, out Vector3 v3))
                        property.vector3Value = v3;
                    break;
                case SerializedPropertyType.Vector4:
                    if (TryParseVector4(value, out Vector4 v4))
                        property.vector4Value = v4;
                    break;
                case SerializedPropertyType.Color:
                    if (TryParseColor(value, out Color color))
                        property.colorValue = color;
                    break;
                // 可以根据需要添加更多类型支持
            }
        }
        
        /// <summary>
        /// 获取导入器设置摘要
        /// </summary>
        private string GetImporterSettingsSummary(AssetImporter importer)
        {
            if (importer == null)
                return "导入器为空";

            var summary = new StringBuilder();
            summary.AppendLine($"类型: {importer.GetType().Name}");
            summary.AppendLine($"路径: {importer.assetPath}");

            // 根据导入器类型提供特定信息
            switch (importer)
            {
                case TextureImporter textureImporter:
                    AppendTextureImporterSummary(summary, textureImporter);
                    break;
                case ModelImporter modelImporter:
                    AppendModelImporterSummary(summary, modelImporter);
                    break;
                case AudioImporter audioImporter:
                    AppendAudioImporterSummary(summary, audioImporter);
                    break;
                default:
                    summary.AppendLine($"通用导入器: {importer.GetType().Name}");
                    break;
            }

            return summary.ToString();
        }

        /// <summary>
        /// 添加贴图导入器摘要
        /// </summary>
        private void AppendTextureImporterSummary(StringBuilder summary, TextureImporter importer)
        {
            summary.AppendLine($"贴图类型: {importer.textureType}");
            summary.AppendLine($"sRGB: {importer.sRGBTexture}");
            summary.AppendLine($"可读: {importer.isReadable}");
            summary.AppendLine($"压缩: {importer.textureCompression}");

            var platformSettings = importer.GetDefaultPlatformTextureSettings();
            summary.AppendLine($"最大尺寸: {platformSettings.maxTextureSize}");
            summary.AppendLine($"格式: {platformSettings.format}");
        }

        /// <summary>
        /// 添加模型导入器摘要
        /// </summary>
        private void AppendModelImporterSummary(StringBuilder summary, ModelImporter importer)
        {
            summary.AppendLine($"网格压缩: {importer.meshCompression}");
            summary.AppendLine($"可读写: {importer.isReadable}");
            summary.AppendLine($"优化网格: {importer.optimizeMesh}");
            summary.AppendLine($"导入动画: {importer.importAnimation}");
            summary.AppendLine($"导入材质: {importer.importMaterials}");
            summary.AppendLine($"缩放因子: {importer.globalScale}");
        }

        /// <summary>
        /// 添加音频导入器摘要
        /// </summary>
        private void AppendAudioImporterSummary(StringBuilder summary, AudioImporter importer)
        {
            summary.AppendLine($"加载类型: {importer.defaultSampleSettings.loadType}");
            summary.AppendLine($"压缩格式: {importer.defaultSampleSettings.compressionFormat}");
            summary.AppendLine($"质量: {importer.defaultSampleSettings.quality}");
        }

        /// <summary>
        /// 标准化日志系统
        /// </summary>
        private void LogInfo(string message)
        {
            if (enableDetailedLogging)
            {
                Log(LogLevel.Info, $"[PresetProcessor] {message}");
            }
        }

        private void LogWarning(string message)
        {
            Log(LogLevel.Warning, $"[PresetProcessor] {message}");
        }

        private void LogError(string message)
        {
            Log(LogLevel.Error, $"[PresetProcessor] {message}");
        }

        private void LogDetailed(string message)
        {
            if (enableDetailedLogging)
            {
                Log(LogLevel.Debug, $"[PresetProcessor] {message}");
            }
        }

        #endregion

        #region 辅助工具方法

        /// <summary>
        /// 尝试解析Vector2
        /// </summary>
        private bool TryParseVector2(string value, out Vector2 result)
        {
            result = Vector2.zero;
            if (string.IsNullOrEmpty(value)) return false;

            var parts = value.Split(',');
            if (parts.Length == 2 &&
                float.TryParse(parts[0], out float x) &&
                float.TryParse(parts[1], out float y))
            {
                result = new Vector2(x, y);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 尝试解析Vector3
        /// </summary>
        private bool TryParseVector3(string value, out Vector3 result)
        {
            result = Vector3.zero;
            if (string.IsNullOrEmpty(value)) return false;

            var parts = value.Split(',');
            if (parts.Length == 3 &&
                float.TryParse(parts[0], out float x) &&
                float.TryParse(parts[1], out float y) &&
                float.TryParse(parts[2], out float z))
            {
                result = new Vector3(x, y, z);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 尝试解析Vector4
        /// </summary>
        private bool TryParseVector4(string value, out Vector4 result)
        {
            result = Vector4.zero;
            if (string.IsNullOrEmpty(value)) return false;

            var parts = value.Split(',');
            if (parts.Length == 4 &&
                float.TryParse(parts[0], out float x) &&
                float.TryParse(parts[1], out float y) &&
                float.TryParse(parts[2], out float z) &&
                float.TryParse(parts[3], out float w))
            {
                result = new Vector4(x, y, z, w);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 尝试解析Color
        /// </summary>
        private bool TryParseColor(string value, out Color result)
        {
            result = Color.white;
            if (string.IsNullOrEmpty(value)) return false;

            var parts = value.Split(',');
            if (parts.Length == 4 &&
                float.TryParse(parts[0], out float r) &&
                float.TryParse(parts[1], out float g) &&
                float.TryParse(parts[2], out float b) &&
                float.TryParse(parts[3], out float a))
            {
                result = new Color(r, g, b, a);
                return true;
            }
            return false;
        }

        #endregion

        #region 配置验证

        /// <summary>
        /// Validate processor configuration
        /// </summary>
        protected override bool ValidateConfiguration()
        {
            // Clear cache when configuration changes
            ClearCache();

            if (targetPreset == null)
            {
                Log(LogLevel.Error, "Target preset not configured");
                return false;
            }
            
            return true;
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// Get preset type name - Unity 2018.4 compatible version
        /// </summary>
        public string GetPresetTypeName()
        {
            return GetPresetTypeName(targetPreset);
        }

        /// <summary>
        /// 获取Preset类型名称 - Unity 2018.4兼容版本
        /// </summary>
        private string GetPresetTypeName(Preset preset)
        {
            if (preset == null)
                return null;

            try
            {
                // 通过反射获取Preset的目标类型信息
                var presetType = preset.GetType();
                var targetObjectsField = presetType.GetField("m_TargetObject", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                if (targetObjectsField != null)
                {
                    var targetObject = targetObjectsField.GetValue(preset);
                    if (targetObject != null)
                    {
                        return targetObject.GetType().Name;
                    }
                }

                // 备用方法：通过PropertyModifications推断类型
                var modifications = preset.PropertyModifications;
                if (modifications != null && modifications.Length > 0)
                {
                    // 根据属性路径推断导入器类型
                    foreach (var modification in modifications)
                    {
                        if (modification.propertyPath.Contains("m_sRGBTexture") || 
                            modification.propertyPath.Contains("m_TextureType"))
                        {
                            return "TextureImporter";
                        }
                        else if (modification.propertyPath.Contains("m_OptimizeMesh") || 
                                modification.propertyPath.Contains("m_ImportAnimation"))
                        {
                            return "ModelImporter";
                        }
                        else if (modification.propertyPath.Contains("m_DefaultSettings"))
                        {
                            return "AudioImporter";
                        }
                    }
                }

                return "UnknownImporter";
            }
            catch (Exception ex)
            {
                Log(LogLevel.Warning, $"获取Preset类型失败: {ex.Message}");
                return "UnknownImporter";
            }
        }

        /// <summary>
        /// 检查Preset是否与指定的导入器类型兼容
        /// </summary>
        public bool IsPresetCompatibleWithImporter(AssetImporter importer)
        {
            if (targetPreset == null || importer == null)
                return false;

            return targetPreset.CanBeAppliedTo(importer);
        }

        #endregion
    }

    /// <summary>
    /// SerializedProperty扩展方法
    /// </summary>
    public static class SerializedPropertyExtensions
    {
        /// <summary>
        /// 获取SerializedProperty的字符串表示值
        /// </summary>
        public static string GetPropertyValueAsString(this SerializedProperty property)
        {
            switch (property.propertyType)
            {
                case SerializedPropertyType.Boolean:
                    return property.boolValue ? "1" : "0";
                case SerializedPropertyType.Integer:
                    return property.intValue.ToString();
                case SerializedPropertyType.Float:
                    return property.floatValue.ToString();
                case SerializedPropertyType.String:
                    return property.stringValue;
                case SerializedPropertyType.Enum:
                    return property.enumValueIndex.ToString();
                case SerializedPropertyType.Vector2:
                    var v2 = property.vector2Value;
                    return $"{v2.x},{v2.y}";
                case SerializedPropertyType.Vector3:
                    var v3 = property.vector3Value;
                    return $"{v3.x},{v3.y},{v3.z}";
                case SerializedPropertyType.Vector4:
                    var v4 = property.vector4Value;
                    return $"{v4.x},{v4.y},{v4.z},{v4.w}";
                case SerializedPropertyType.Color:
                    var c = property.colorValue;
                    return $"{c.r},{c.g},{c.b},{c.a}";
                case SerializedPropertyType.ObjectReference:
                    return property.objectReferenceValue?.GetInstanceID().ToString() ?? "0";
                default:
                    return property.stringValue ?? "";
            }
        }
    }
}
