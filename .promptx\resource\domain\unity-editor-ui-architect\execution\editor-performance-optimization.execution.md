<execution>
  <constraint>
    ## Unity编辑器性能优化的客观限制
    - **OnGUI调用频率**：每帧至少调用一次，布局阶段可能调用多次
    - **GC压力**：IMGUI系统频繁产生临时对象导致垃圾回收
    - **主线程约束**：所有GUI操作必须在主线程执行，不能并行化
    - **内存分配**：GUIContent、GUIStyle等对象的频繁创建和销毁
    - **重绘触发**：任何GUI状态变化都可能触发全窗口重绘
    - **Unity版本差异**：不同Unity版本的IMGUI性能特征不同
  </constraint>

  <rule>
    ## Unity编辑器性能优化强制规则
    - **缓存优先**：所有可重用的GUI对象必须缓存，避免重复创建
    - **延迟计算**：复杂计算必须延迟到实际需要时执行
    - **分帧处理**：大数据量操作必须分帧执行，避免卡顿
    - **内存监控**：实现内存使用监控，超过阈值时强制清理
    - **性能测量**：关键操作必须添加性能测量和监控
    - **异步优先**：所有IO操作和重型计算必须异步执行
  </rule>

  <guideline>
    ## Unity编辑器性能优化指导原则
    - **用户感知优先**：优化用户能感知到的性能瓶颈
    - **热点优化**：通过Profiler识别性能热点，重点优化
    - **渐进式加载**：大数据量使用分页和按需加载
    - **状态缓存**：合理缓存界面状态，减少重复计算
    - **优雅降级**：在性能不足时提供功能简化选项
    - **持续监控**：建立性能监控机制，及时发现性能问题
  </guideline>

  <process>
    ## Unity编辑器性能优化具体流程

    ### Phase 1: 性能瓶颈识别
    ```
    1. Profiler分析
       - 使用Unity Profiler分析EditorWindow性能
       - 识别OnGUI方法的CPU占用情况
       - 分析GC Alloc的主要来源
       - 监控内存使用增长趋势

    2. 用户体验测试
       - 测试大数据量下的界面响应性
       - 记录常用操作的响应时间
       - 分析长时间运行的稳定性
       - 收集用户对性能的主观感受

    3. 热点函数识别
       - OnGUI方法的具体耗时分析
       - GUI.Layout计算的性能开销
       - 数据加载和处理的瓶颈
       - 字符串拼接和格式化开销
    ```

    ### Phase 2: GUI渲染优化
    ```
    1. GUI对象缓存策略
       ```csharp
       public class CachedGUIContent
       {
           private static Dictionary<string, GUIContent> _contentCache = 
               new Dictionary<string, GUIContent>();
           
           public static GUIContent Get(string text, string tooltip = null)
           {
               string key = $"{text}#{tooltip}";
               if (!_contentCache.TryGetValue(key, out GUIContent content))
               {
                   content = new GUIContent(text, tooltip);
                   _contentCache[key] = content;
               }
               return content;
           }
       }
       ```

    2. 重绘优化策略
       - 使用GUI.changed检测状态变化
       - 实现选择性重绘机制
       - 缓存布局计算结果
       - 避免不必要的Repaint调用

    3. 虚拟化列表实现
       ```csharp
       public class VirtualizedListView
       {
           private Vector2 scrollPosition;
           private float itemHeight = 20f;
           private int visibleItemCount;
           
           public void OnGUI(IList items, Rect rect)
           {
               visibleItemCount = Mathf.CeilToInt(rect.height / itemHeight);
               int startIndex = Mathf.FloorToInt(scrollPosition.y / itemHeight);
               int endIndex = Mathf.Min(startIndex + visibleItemCount, items.Count);
               
               // 只渲染可见项
               for (int i = startIndex; i < endIndex; i++)
               {
                   DrawItem(items[i], i);
               }
           }
       }
       ```
    ```

    ### Phase 3: 数据加载优化
    ```
    1. 异步数据加载
       ```csharp
       public class AsyncDataLoader
       {
           private EditorCoroutine loadingCoroutine;
           
           public void LoadDataAsync(System.Action<DataResult> onComplete)
           {
               if (loadingCoroutine != null)
                   EditorCoroutineUtility.StopCoroutine(loadingCoroutine);
               
               loadingCoroutine = EditorCoroutineUtility.StartCoroutine(
                   LoadDataCoroutine(onComplete), this);
           }
           
           private IEnumerator LoadDataCoroutine(System.Action<DataResult> onComplete)
           {
               // 分批加载数据
               for (int i = 0; i < totalBatches; i++)
               {
                   LoadBatch(i);
                   yield return null; // 让出一帧
               }
               onComplete?.Invoke(result);
           }
       }
       ```

    2. 分页数据管理
       - 实现数据分页加载机制
       - 使用LRU缓存管理页面数据
       - 支持预加载和懒加载策略
       - 提供数据刷新和更新机制

    3. 缓存策略优化
       - 多级缓存：内存缓存 + 磁盘缓存
       - 缓存失效：基于时间和版本的失效策略
       - 缓存预热：预加载常用数据
       - 缓存监控：监控缓存命中率和效果
    ```

    ### Phase 4: 内存管理优化
    ```
    1. 对象池实现
       ```csharp
       public class UIObjectPool<T> where T : class, new()
       {
           private Queue<T> pool = new Queue<T>();
           private System.Action<T> resetAction;
           
           public UIObjectPool(System.Action<T> reset = null)
           {
               resetAction = reset;
           }
           
           public T Get()
           {
               if (pool.Count > 0)
               {
                   T obj = pool.Dequeue();
                   return obj;
               }
               return new T();
           }
           
           public void Return(T obj)
           {
               resetAction?.Invoke(obj);
               pool.Enqueue(obj);
           }
       }
       ```

    2. 内存监控机制
       - 实时监控编辑器窗口内存使用
       - 设置内存使用阈值警告
       - 自动清理不必要的缓存数据
       - 定期执行GC.Collect优化

    3. 资源生命周期管理
       - 窗口关闭时清理所有资源
       - 及时释放大型数据对象
       - 避免循环引用导致的内存泄漏
       - 使用WeakReference管理临时对象
    ```

    ### Phase 5: 用户体验优化
    ```
    1. 响应性优化
       - 长操作显示进度条
       - 提供操作取消机制
       - 使用异步操作避免界面冻结
       - 实现操作队列管理

    2. 性能指示器
       ```csharp
       public class PerformanceMonitor
       {
           private System.Diagnostics.Stopwatch stopwatch;
           private Queue<float> frameTimeHistory;
           
           public void BeginFrame()
           {
               stopwatch.Restart();
           }
           
           public void EndFrame()
           {
               float frameTime = stopwatch.ElapsedMilliseconds;
               frameTimeHistory.Enqueue(frameTime);
               
               if (frameTime > 16.67f) // 超过60FPS阈值
               {
                   ShowPerformanceWarning();
               }
           }
       }
       ```

    3. 优雅降级策略
       - 大数据量时自动启用简化模式
       - 提供性能设置选项
       - 支持功能开关和级别调整
       - 根据硬件性能自适应调整
    ```
  </process>

  <criteria>
    ## Unity编辑器性能优化评价标准

    ### 响应性能指标
    - ✅ 界面操作响应时间 < 100ms
    - ✅ 大列表（1000+项）滚动流畅（60FPS）
    - ✅ 窗口打开时间 < 500ms
    - ✅ 数据刷新操作 < 1s

    ### 内存性能指标
    - ✅ 空闲状态内存占用 < 50MB
    - ✅ 长期运行内存增长 < 100MB/hour
    - ✅ GC分配频率 < 10MB/frame
    - ✅ 内存峰值不超过 200MB

    ### CPU性能指标
    - ✅ OnGUI方法耗时 < 2ms/frame
    - ✅ 空闲时CPU占用 < 5%
    - ✅ 数据处理CPU占用 < 50%
    - ✅ 支持多核心异步处理

    ### 用户体验指标
    - ✅ 无明显的界面卡顿感
    - ✅ 长操作有进度指示
    - ✅ 支持操作中断和取消
    - ✅ 错误情况下界面仍可响应

    ### 稳定性指标
    - ✅ 连续运行8小时无性能退化
    - ✅ 内存泄漏检测通过
    - ✅ 压力测试通过（1万+资源）
    - ✅ 异常情况自动恢复

    ### 可扩展性指标
    - ✅ 支持更大数据量的性能扩展
    - ✅ 性能优化代码模块化良好
    - ✅ 性能监控机制完善
    - ✅ 性能调优参数可配置
  </criteria>
</execution> 