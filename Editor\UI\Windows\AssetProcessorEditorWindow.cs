using UnityEngine;
using UnityEditor;
using AssetPipeline.Core;
using AssetPipeline.Processors;
using AssetPipeline.UI.Components;
using System.Collections.Generic;
using System.Linq;

namespace AssetPipeline.UI.Windows
{
    public class AssetProcessorEditorWindow : EditorWindow
    {
        private AssetProcessor targetProcessor;
        private Vector2 scrollPosition;
        private bool showBasicConfig = true;
        private bool showStatisticsConfig = true;
        private bool showTestConfig = true;
        
        private string testAssetPath = "";

        public static void ShowWindow(AssetProcessor processor)
        {
            var window = GetWindow<AssetProcessorEditorWindow>("AssetProcessor 编辑器");
            window.targetProcessor = processor;
            window.minSize = new Vector2(550, 500);
            window.Show();
        }

        void OnGUI()
        {
            if (targetProcessor == null)
            {
                EditorGUILayout.HelpBox("没有选中的AssetProcessor", MessageType.Warning);
                if (GUILayout.Button("关闭"))
                {
                    Close();
                }
                return;
            }

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            DrawHeader();
            EditorGUILayout.Space();
            
            DrawBasicConfig();
            EditorGUILayout.Space();
            
            DrawStatisticsConfig();
            EditorGUILayout.Space();
            
            DrawTestSection();

            EditorGUILayout.EndScrollView();
        }

        void DrawHeader()
        {
            UIHelper.DrawHeader($"编辑Processor: {targetProcessor.DisplayName}", "⚙️");
            
            UIHelper.DrawBox(() =>
            {
                UIHelper.DrawKeyValue("类型", targetProcessor.GetType().Name, 60);
                UIHelper.DrawKeyValue("版本", $"v{targetProcessor.Version}", 60);
                UIHelper.DrawKeyValue("GUID", targetProcessor.GUID, 60);
                UIHelper.DrawKeyValue("作者", targetProcessor.Author, 60);
                UIHelper.DrawKeyValue("分类", targetProcessor.Category, 60);
                
                if (!string.IsNullOrEmpty(targetProcessor.Description))
                {
                    EditorGUILayout.LabelField("描述:", EditorStyles.boldLabel);
                    EditorGUILayout.LabelField(targetProcessor.Description, EditorStyles.wordWrappedLabel);
                }
            });
        }

        void DrawBasicConfig()
        {
            showBasicConfig = UIHelper.DrawFoldout(ref showBasicConfig, "基础配置", "🔧", true);
            
            if (showBasicConfig)
            {
                UIHelper.DrawBox(() =>
                {
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField("启用状态", GUILayout.Width(60));
                    targetProcessor.Enabled = EditorGUILayout.Toggle(targetProcessor.Enabled);
                    EditorGUILayout.EndHorizontal();
                    
                    targetProcessor.Priority = EditorGUILayout.IntField("优先级", targetProcessor.Priority);
                    
                    UIHelper.DrawSeparator();
                    
                    EditorGUILayout.LabelField("处理器状态:", EditorStyles.boldLabel);
                    var statusText = targetProcessor.IsInitialized ? "已初始化" : "未初始化";
                    var statusColor = targetProcessor.IsInitialized ? Color.green : Color.yellow;
                    
                    var oldColor = GUI.color;
                    GUI.color = statusColor;
                    EditorGUILayout.LabelField(statusText, EditorStyles.miniLabel);
                    GUI.color = oldColor;
                    
                    EditorGUILayout.Space();
                    
                    EditorGUILayout.BeginHorizontal();
                    if (GUILayout.Button("重置为默认配置"))
                    {
                        if (EditorUtility.DisplayDialog("确认重置", 
                            "确定要重置处理器为默认配置吗？这将清除所有自定义设置。", "重置", "取消"))
                        {
                            targetProcessor.ResetToDefaults();
                        }
                    }
                    
                    if (GUILayout.Button("验证配置"))
                    {
                        ValidateProcessor();
                    }
                    EditorGUILayout.EndHorizontal();
                });
            }
        }

        void DrawStatisticsConfig()
        {
            showStatisticsConfig = UIHelper.DrawFoldout(ref showStatisticsConfig, "统计信息", "📊", true);
            
            if (showStatisticsConfig)
            {
                UIHelper.DrawBox(() =>
                {
                    var stats = targetProcessor.Statistics;
                    
                    EditorGUILayout.LabelField("处理统计:", EditorStyles.boldLabel);
                    UIHelper.DrawKeyValue("处理总数", stats.ProcessedCount.ToString(), 80);
                    UIHelper.DrawKeyValue("成功数量", stats.SuccessCount.ToString(), 80);
                    UIHelper.DrawKeyValue("警告数量", stats.WarningCount.ToString(), 80);
                    UIHelper.DrawKeyValue("错误数量", stats.ErrorCount.ToString(), 80);
                    
                    if (stats.ProcessedCount > 0)
                    {
                        var successRate = (float)stats.SuccessCount / stats.ProcessedCount * 100f;
                        UIHelper.DrawKeyValue("成功率", $"{successRate:F1}%", 80);
                    }
                    
                    EditorGUILayout.Space();
                    
                    if (stats.ProcessedCount > 0)
                    {
                        EditorGUILayout.LabelField("摘要:", EditorStyles.boldLabel);
                        EditorGUILayout.LabelField(stats.GetSummary(), EditorStyles.wordWrappedLabel);
                    }
                    
                    EditorGUILayout.Space();
                    
                    if (GUILayout.Button("重置统计"))
                    {
                        if (EditorUtility.DisplayDialog("确认重置", 
                            "确定要重置处理器统计信息吗？", "重置", "取消"))
                        {
                            stats.Reset();
                        }
                    }
                });
            }
        }

        void DrawTestSection()
        {
            showTestConfig = UIHelper.DrawFoldout(ref showTestConfig, "测试处理", "🧪", true);
            
            if (showTestConfig)
            {
                UIHelper.DrawBox(() =>
                {
                    EditorGUILayout.BeginHorizontal();
                    testAssetPath = EditorGUILayout.TextField("测试路径", testAssetPath);
                    if (GUILayout.Button("选择", GUILayout.Width(50)))
                    {
                        var path = EditorUtility.OpenFilePanel("选择测试文件", "Assets", "");
                        if (!string.IsNullOrEmpty(path) && path.StartsWith(Application.dataPath))
                        {
                            testAssetPath = "Assets" + path.Substring(Application.dataPath.Length);
                        }
                    }
                    EditorGUILayout.EndHorizontal();
                    
                    if (!string.IsNullOrEmpty(testAssetPath))
                    {
                        EditorGUILayout.Space();
                        
                        var canProcess = targetProcessor.CanProcess(testAssetPath);
                        var message = canProcess ? "✅ 可以处理" : "❌ 无法处理";
                        var messageType = canProcess ? MessageType.Info : MessageType.Warning;
                        
                        UIHelper.DrawStatusText(message, messageType);
                        
                        if (canProcess)
                        {
                            EditorGUILayout.LabelField("处理器信息:", EditorStyles.boldLabel);
                            EditorGUILayout.LabelField($"启用状态: {(targetProcessor.Enabled ? "是" : "否")}", EditorStyles.miniLabel);
                            EditorGUILayout.LabelField($"优先级: {targetProcessor.Priority}", EditorStyles.miniLabel);
                            EditorGUILayout.LabelField($"初始化状态: {(targetProcessor.IsInitialized ? "是" : "否")}", EditorStyles.miniLabel);
                        }
                        
                        if (System.IO.File.Exists(testAssetPath))
                        {
                            var asset = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(testAssetPath);
                            if (asset != null)
                            {
                                EditorGUILayout.LabelField("资产信息:", EditorStyles.boldLabel);
                                EditorGUILayout.LabelField($"资产类型: {asset.GetType().Name}", EditorStyles.miniLabel);
                                EditorGUILayout.LabelField($"文件大小: {GetFileSize(testAssetPath)}", EditorStyles.miniLabel);
                            }
                        }
                    }
                });
            }
        }

        void ValidateProcessor()
        {
            try
            {
                var isValid = targetProcessor.OnStart();
                var message = isValid ? "✅ 处理器配置有效" : "❌ 处理器配置无效";
                var messageType = isValid ? MessageType.Info : MessageType.Error;
                
                EditorUtility.DisplayDialog("验证结果", message, "确定");
            }
            catch (System.Exception ex)
            {
                EditorUtility.DisplayDialog("验证失败", $"验证过程中发生错误:\n{ex.Message}", "确定");
            }
        }

        string GetFileSize(string filePath)
        {
            try
            {
                var fileInfo = new System.IO.FileInfo(filePath);
                var size = fileInfo.Length;
                
                if (size < 1024)
                    return $"{size} B";
                else if (size < 1024 * 1024)
                    return $"{size / 1024.0:F1} KB";
                else
                    return $"{size / (1024.0 * 1024.0):F1} MB";
            }
            catch
            {
                return "未知";
            }
        }
    }
} 