using L10.Editor.AssetPipeline.Sqlite;

namespace AssetPipeline.Database.Models
{
    [Table("AssetReference")]
    public class AssetReference
    {
        [PrimaryKey, AutoIncrement]
        public int Id { get; set; }
        
        public string TargetGUID { get; set; }
        public string ReferencerGUID { get; set; }
        public string ReferenceType { get; set; }
        public int ReferenceCount { get; set; }
        
        [Ignore] public string TargetPath { get; set; }
        [Ignore] public string ReferencerPath { get; set; }
    }
} 