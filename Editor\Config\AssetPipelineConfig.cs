using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEditor;
using AssetPipeline.Core;
using Logger = AssetPipeline.Core.Logger;

namespace AssetPipeline.Config
{
    public class AssetPipelineConfig : ScriptableObject
    {
        public const string ASSET_PIPELINE_FOLDER = "Assets/Editor/AssetPipeline";
        public const string DATA_FOLDER = ASSET_PIPELINE_FOLDER + "/Data";
        public const string PROFILE_FOLDER = DATA_FOLDER + "/Profiles/";
        
        public const string CONFIG_FILE_PATH = DATA_FOLDER + "/AssetPipelineConfig.asset";
        public const string MAIN_TREE_PATH = DATA_FOLDER + "/MainAssetTree.asset";
        
        
        private static AssetPipelineConfig _instance;
        
        /// <summary>
        /// 标记配置是否正在初始化，防止递归调用
        /// </summary>
        private static bool _isInitializing = false;
        
        public static AssetPipelineConfig Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = LoadOrCreateInstance();
                }
                return _instance;
            }
        }
        

        #region 序列化字段

        [Header("核心资源规则")]
        [SerializeField] private List<AssetTypeDefinition> assetTypeDefinitions = new List<AssetTypeDefinition>();
        [SerializeField] private AssetTree mainAssetTree;

        [Header("配置元信息")]
        [SerializeField] private string configVersion = "1.0";
        [SerializeField] private string description = "";

        #endregion

        /// <summary>
        /// 资产类型定义列表
        /// </summary>
        public static List<AssetTypeDefinition> AssetTypeDefinitions => Instance?.assetTypeDefinitions ?? AssetType.CreateDefault();
        
        /// <summary>
        /// 主资源树
        /// </summary>
        public static AssetTree MainAssetTree
        {
            get
            {
                // 初始化期间防止递归调用
                if (_isInitializing)
                {
                    Logger.Debug(LogModule.Core, "[AssetPipelineConfig] 初始化期间跳过MainAssetTree访问，防止循环");
                    return null;
                }

                return Instance?.mainAssetTree;
            }
        }

        /// <summary>
        /// 配置版本
        /// </summary>
        public string ConfigVersion
        {
            get => configVersion;
            set => configVersion = value;
        }
        
        /// <summary>
        /// 描述信息
        /// </summary>
        public string Description
        {
            get => description;
            set => description = value;
        }

        void OnValidate()
        {
            mainAssetTree?.ClearCache();
        }
        
        /// <summary>
        /// 编辑器启动时自动清理缓存
        /// </summary>
        [InitializeOnLoadMethod]
        private static void OnEditorStartup()
        {
            EditorApplication.delayCall += () =>
            {
                Logger.Debug(LogModule.Core, "[CacheManager] 编辑器启动，自动清理缓存");
                ClearCache();
            };
        }

        /// <summary>
        /// 代码重新编译后自动清理缓存
        /// </summary>
        [UnityEditor.Callbacks.DidReloadScripts]
        private static void OnScriptsReloaded()
        {
            Logger.Debug(LogModule.Core, "[CacheManager] 代码重新编译，自动清理缓存");
            ClearCache();
        }

        /// <summary>
        /// 清理所有缓存 - 简化版本，逻辑清晰可靠
        /// </summary>
        public static void ClearCache()
        {
            MainAssetTree?.ClearCache();
            Logger.Info(LogModule.Core, "[AssetPipelineConfig] 配置缓存已清理");
        }

        #region 单例管理

        /// <summary>
        /// 加载或创建配置实例
        /// </summary>
        private static AssetPipelineConfig LoadOrCreateInstance()
        {
            var config = AssetDatabase.LoadAssetAtPath<AssetPipelineConfig>(CONFIG_FILE_PATH);
            
            if (config == null)
            {
                config = CreateDefault();
            }

            return config;
        }

        /// <summary>
        /// 创建默认配置实例
        /// </summary>
        private static AssetPipelineConfig CreateDefault()
        {
            // 设置初始化标记，防止死循环
            _isInitializing = true;
            
            try
            {
                if (!Directory.Exists(DATA_FOLDER))
                {
                    Directory.CreateDirectory(DATA_FOLDER);
                }
                
                var config = CreateInstance<AssetPipelineConfig>();
                config.configVersion = "1.0";
                config.description = "L10资产配置";
                config.assetTypeDefinitions = AssetType.CreateDefault();
                config.mainAssetTree = AssetTree.CreateDefault();
                AssetDatabase.CreateAsset(config.mainAssetTree, MAIN_TREE_PATH);
                AssetDatabase.CreateAsset(config, CONFIG_FILE_PATH);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                Logger.Info(LogModule.Config, $"[AssetPipelineConfig] 默认项目配置已创建: {CONFIG_FILE_PATH}");
                return config;
            }
            finally
            {
                // 清除初始化标记
                _isInitializing = false;
            }
        }
        
        #endregion

        #region 统一的管道管理功能
        
        /// <summary>
        /// 检查管道是否启用
        /// </summary>
        /// <param name="pipelineName">管道名称</param>
        /// <returns>是否启用</returns>
        public static bool IsPipelineEnabled(string pipelineName)
        {
            if (string.IsNullOrEmpty(pipelineName))
                return false;

            switch (pipelineName.ToLower())
            {
                case "import":
                    return AssetPipelineSettings.EnableImportPipeline;
                case "svncommit":
                case "commit":
                    return AssetPipelineSettings.EnableSvnCommitPipeline;
                case "checkrunner":
                case "check":
                    return AssetPipelineSettings.EnableCheckRunnerPipeline;
                case "modification":
                    return AssetPipelineSettings.EnableModificationPipeline;
                default:
                    return EditorPrefs.GetBool($"AssetPipeline.{pipelineName}.Enabled", true);
            }
        }

        /// <summary>
        /// 设置管道启用状态
        /// </summary>
        /// <param name="pipelineName">管道名称</param>
        /// <param name="enabled">是否启用</param>
        public static void SetPipelineEnabled(string pipelineName, bool enabled)
        {
            if (string.IsNullOrEmpty(pipelineName))
                return;

            switch (pipelineName.ToLower())
            {
                case "import":
                    AssetPipelineSettings.EnableImportPipeline = enabled;
                    break;
                case "svncommit":
                case "commit":
                    AssetPipelineSettings. EnableSvnCommitPipeline = enabled;
                    break;
                case "checkrunner":
                case "check":
                    AssetPipelineSettings.EnableCheckRunnerPipeline = enabled;
                    break;
                case "modification":
                    AssetPipelineSettings.EnableModificationPipeline = enabled;
                    break;
                default:
                    EditorPrefs.SetBool($"AssetPipeline.{pipelineName}.Enabled", enabled);
                    break;
            }

            Logger.Debug(LogModule.Core, $"[AssetPipelineConfig] 管道 {pipelineName} 设置为 {(enabled ? "启用" : "禁用")}");
        }

        #endregion

        public static string GenerateAssetPath(string folder, string filename)
        {
            var path = Path.Combine(folder, filename).Replace(@"\", "/");
            if (Path.GetExtension(path) != ".asset")
            {
                path += ".asset";
            }

            var folderPath = Path.GetDirectoryName(path);
            if (!Directory.Exists(folderPath))
            {
                Directory.CreateDirectory(folderPath);
            }

            path = AssetDatabase.GenerateUniqueAssetPath(path);
            return path;
        }
    }
} 