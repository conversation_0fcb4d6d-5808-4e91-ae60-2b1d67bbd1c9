using UnityEngine;
using UnityEditor;
using System;

namespace AssetPipeline.UI.Components
{
    public static class UIHelper
    {
        private static readonly GUIStyle _headerStyle;
        private static readonly GUIStyle _errorStyle;
        private static readonly GUIStyle _warningStyle;
        private static readonly GUIStyle _successStyle;

        static UIHelper()
        {
            _headerStyle = new GUIStyle(EditorStyles.boldLabel);
            _headerStyle.fontSize = 14;
            
            _errorStyle = new GUIStyle(EditorStyles.miniLabel);
            _errorStyle.normal.textColor = Color.red;
            
            _warningStyle = new GUIStyle(EditorStyles.miniLabel);
            _warningStyle.normal.textColor = Color.yellow;
            
            _successStyle = new GUIStyle(EditorStyles.miniLabel);
            _successStyle.normal.textColor = Color.green;
        }

        public static void DrawHeader(string text, string icon = "")
        {
            EditorGUILayout.LabelField($"{icon} {text}", _headerStyle);
        }

        public static void DrawStatusText(string text, MessageType type = MessageType.Info)
        {
            GUIStyle style;
            switch (type)
            {
                case MessageType.Error:
                    style = _errorStyle;
                    break;
                case MessageType.Warning:
                    style = _warningStyle;
                    break;
                case MessageType.Info:
                    style = _successStyle;
                    break;
                default:
                    style = EditorStyles.miniLabel;
                    break;
            }
            
            EditorGUILayout.LabelField(text, style);
        }

        public static bool DrawFoldout(ref bool foldout, string text, string icon = "", bool bold = false)
        {
            var style = bold ? EditorStyles.boldLabel : EditorStyles.label;
            foldout = EditorGUILayout.Foldout(foldout, $"{icon} {text}", true, style);
            return foldout;
        }

        public static void DrawSeparator()
        {
            EditorGUILayout.Space();
            var rect = EditorGUILayout.GetControlRect(false, 1);
            EditorGUI.DrawRect(rect, Color.gray);
            EditorGUILayout.Space();
        }

        public static bool DrawButton(string text, string icon = "", float width = 0)
        {
            var content = new GUIContent($"{icon} {text}");
            return width > 0 
                ? GUILayout.Button(content, GUILayout.Width(width))
                : GUILayout.Button(content);
        }

        public static void DrawBox(Action content, string title = "")
        {
            if (!string.IsNullOrEmpty(title))
            {
                EditorGUILayout.LabelField(title, EditorStyles.boldLabel);
            }
            
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            content?.Invoke();
            EditorGUILayout.EndVertical();
        }

        public static void DrawProgressBar(float progress, string label = "")
        {
            var rect = EditorGUILayout.GetControlRect();
            EditorGUI.ProgressBar(rect, progress, label);
        }

        public static void DrawKeyValue(string key, string value, float keyWidth = 100)
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(key, GUILayout.Width(keyWidth));
            EditorGUILayout.LabelField(value, EditorStyles.miniLabel);
            EditorGUILayout.EndHorizontal();
        }

        public static string DrawSearchField(string searchText)
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("🔍", GUILayout.Width(20));
            searchText = EditorGUILayout.TextField(searchText);
            EditorGUILayout.EndHorizontal();
            return searchText;
        }

        public static void DrawMetric(string name, string value, Color? color = null)
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(name, GUILayout.Width(100));
            
            if (color.HasValue)
            {
                var oldColor = GUI.color;
                GUI.color = color.Value;
                EditorGUILayout.LabelField(value, EditorStyles.boldLabel);
                GUI.color = oldColor;
            }
            else
            {
                EditorGUILayout.LabelField(value, EditorStyles.boldLabel);
            }
            
            EditorGUILayout.EndHorizontal();
        }

        public static void ShowQuickNotification(string message)
        {
            if (SceneView.lastActiveSceneView != null)
            {
                SceneView.lastActiveSceneView.ShowNotification(new GUIContent(message));
            }
        }
    }
} 