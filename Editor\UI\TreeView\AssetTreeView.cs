﻿using System;
using System.Collections.Generic;
using System.Linq;
using AssetPipeline.Core;
using NUnit.Framework;
using UnityEditor;
using UnityEditor.IMGUI.Controls;
using UnityEngine;

namespace AssetPipeline.UI
{
    internal class AssetTreeView : TreeViewWithTreeModel<AssetTreeElement>
    {
        const float kRowHeights = 20f;
        const float kToggleWidth = 18f;

        public event Action<IList<int>> selectionChanged;
        
        // 添加搜索功能
        string m_SearchString = string.Empty;
        
        public string searchString
        {
            get { return m_SearchString; }
            set 
            { 
                if (m_SearchString != value)
                {
                    m_SearchString = value;
                    Reload();
                }
            }
        }
        
        // 添加public访问器
        public new TreeViewItem rootItem
        {
            get { return base.rootItem; }
        }

        // All columns
        enum Columns
        {
            Name,
        }
        public enum SortOption
        {
            Name,
        }

        // Sort options per column
        SortOption[] m_SortOptions =
        {
            SortOption.Name,
        };

        public AssetTreeView(TreeViewState state, MultiColumnHeader multiColumnHeader, TreeModel<AssetTreeElement> model) : base(state, multiColumnHeader, model)
        {
            Assert.AreEqual(m_SortOptions.Length, Enum.GetValues(typeof(Columns)).Length, "Ensure number of sort options are in sync with number of Columns enum values");

            rowHeight = 16f;
            depthIndentWidth = 16f;
            
            customFoldoutYOffset = (rowHeight - EditorGUIUtility.singleLineHeight) * 0.5f;
            extraSpaceBeforeIconAndLabel = kToggleWidth;
            multiColumnHeader.height = 20f;
            multiColumnHeader.sortingChanged += OnSortingChanged;

            showAlternatingRowBackgrounds = true;
            showBorder = true;

            Reload();
        }

        // 重写SelectionChanged方法来触发事件
        protected override void SelectionChanged(IList<int> selectedIds)
        {
            base.SelectionChanged(selectedIds);
            selectionChanged?.Invoke(selectedIds);
        }

        // 添加public的FindItem方法
        public new TreeViewItem FindItem(int id, TreeViewItem searchFromThisItem)
        {
            return base.FindItem(id, searchFromThisItem);
        }

        public static void TreeToList(TreeViewItem root, IList<TreeViewItem> result)
        {
            if (root == null)
                throw new NullReferenceException("root");
            if (result == null)
                throw new NullReferenceException("result");

            result.Clear();

            if (root.children == null)
                return;

            Stack<TreeViewItem> stack = new Stack<TreeViewItem>();
            for (int i = root.children.Count - 1; i >= 0; i--)
                stack.Push(root.children[i]);

            while (stack.Count > 0)
            {
                TreeViewItem current = stack.Pop();
                result.Add(current);

                if (current.hasChildren && current.children[0] != null)
                {
                    for (int i = current.children.Count - 1; i >= 0; i--)
                    {
                        stack.Push(current.children[i]);
                    }
                }
            }
        }

        // Note we We only build the visible rows, only the backend has the full tree information. 
        // The treeview only creates info for the row list.
        protected override IList<TreeViewItem> BuildRows(TreeViewItem root)
        {
            var rows = base.BuildRows(root);
            
            // 如果有搜索文本，过滤行
            if (!string.IsNullOrEmpty(m_SearchString))
            {
                var filteredRows = new List<TreeViewItem>();
                foreach (var row in rows)
                {
                    var item = row as TreeViewItem<AssetTreeElement>;
                    if (item?.data?.Profile != null)
                    {
                        // 搜索Profile的displayName
                        if (item.data.Profile.DisplayName.IndexOf(m_SearchString, StringComparison.OrdinalIgnoreCase) >= 0)
                        {
                            filteredRows.Add(row);
                        }
                    }
                    else if (row.displayName.IndexOf(m_SearchString, StringComparison.OrdinalIgnoreCase) >= 0)
                    {
                        // 搜索节点名称
                        filteredRows.Add(row);
                    }
                }
                rows = filteredRows;
            }
            
            SortIfNeeded(root, rows);
            return rows;
        }

        void OnSortingChanged(MultiColumnHeader header)
        {
            SortIfNeeded(rootItem, GetRows());
        }

        void SortIfNeeded(TreeViewItem root, IList<TreeViewItem> rows)
        {
            if (rows.Count <= 1 || multiColumnHeader.sortedColumnIndex == -1)
                return;

            // Sort the roots of the existing tree items
            SortByMultipleColumns();
            TreeToList(root, rows);
            Repaint();
        }

        void SortByMultipleColumns()
        {
            var sortedColumns = multiColumnHeader.state.sortedColumns;

            if (sortedColumns.Length == 0)
                return;

            var myTypes = rootItem.children.Cast<TreeViewItem<AssetTreeElement>>();
            var orderedQuery = InitialOrder(myTypes, sortedColumns);
            for (int i = 1; i < sortedColumns.Length; i++)
            {
                SortOption sortOption = m_SortOptions[sortedColumns[i]];
                bool ascending = multiColumnHeader.IsSortedAscending(sortedColumns[i]);

                switch (sortOption)
                {
                    case SortOption.Name:
                        orderedQuery = orderedQuery.ThenBy(l => l.data.name, ascending);
                        break;
                }
            }

            rootItem.children = orderedQuery.Cast<TreeViewItem>().ToList();
        }

        IOrderedEnumerable<TreeViewItem<AssetTreeElement>> InitialOrder(IEnumerable<TreeViewItem<AssetTreeElement>> myTypes, int[] history)
        {
            SortOption sortOption = m_SortOptions[history[0]];
            bool ascending = multiColumnHeader.IsSortedAscending(history[0]);
            switch (sortOption)
            {
                case SortOption.Name:
                    return myTypes.Order(l => l.data.name, ascending);
                default:
                    Assert.IsTrue(false, "Unhandled enum");
                    break;
            }

            // default
            return myTypes.Order(l => l.data.name, ascending);
        }

        protected override void RowGUI(RowGUIArgs args)
        {
            var item = (TreeViewItem<AssetTreeElement>)args.item;
            item.data.SerializedObject.Update();

            EditorGUI.BeginChangeCheck();

            for (var i = 0; i < args.GetNumVisibleColumns(); i++)
            {
                CellGUI(args.GetCellRect(i), item, (Columns)args.GetColumn(i), ref args);
            }

            if (EditorGUI.EndChangeCheck())
            {
                item.data.SerializedObject.ApplyModifiedProperties();
                EditorUtility.SetDirty(item.data.Profile);
            }
        }

        void CellGUI(Rect cellRect, TreeViewItem<AssetTreeElement> item, Columns column, ref RowGUIArgs args)
        {
            CenterRectUsingSingleLineHeight(ref cellRect);

            switch (column)
            {
                case Columns.Name:
                    {
                        Rect toggleRect = cellRect;
                        toggleRect.x += GetContentIndent(item);
                        toggleRect.width = 16f;
                        if (toggleRect.xMax < cellRect.xMax)
                            EditorGUI.PropertyField(toggleRect, item.data.SerializedObject.FindProperty("enabled"), GUIContent.none);

                        args.rowRect = cellRect;
                        base.RowGUI(args);
                    }
                    break;
            }
        }

        protected override bool CanRename(TreeViewItem item)
        {
            // Only allow rename if we can show the rename overlay with a certain width (label might be clipped by other columns)
            Rect renameRect = GetRenameRect(treeViewRect, 0, item);
            return renameRect.width > 30;
        }

        protected override void RenameEnded(RenameEndedArgs args)
        {
            // Set the backend name and reload the tree to reflect the new model
            if (args.acceptedRename)
            {
                var element = treeModel.Find(args.itemID);
                element.Rename(args.newName);
                Reload();
            }
        }

        protected override Rect GetRenameRect(Rect rowRect, int row, TreeViewItem item)
        {
            Rect cellRect = GetCellRectForTreeFoldouts(rowRect);
            CenterRectUsingSingleLineHeight(ref cellRect);
            return base.GetRenameRect(cellRect, row, item);
        }

        protected override bool CanMultiSelect(TreeViewItem item)
        {
            return false;
        }

        new void CenterRectUsingSingleLineHeight(ref Rect rect)
        {
            var singleLineHeight = rowHeight;
            if (rect.height > singleLineHeight)
            {
                rect.y += (rect.height - singleLineHeight) * 0.5f;
                rect.height = singleLineHeight;
            }
        }

        //protected override void KeyEvent()
        //{
        //    if (Event.current.type == EventType.KeyDown && Event.current.keyCode == KeyCode.Delete && GetSelection().Count > 0)
        //    {
        //        RemoveSelection();
        //        return;
        //    }

        //    base.KeyEvent();
        //}

        //void RemoveSelection()
        //{
        //    var selections = GetSelection();
        //    treeModel.RemoveElements(selections);
        //    foreach (var selectedId in selections)
        //    {
        //        var selectedItem = FindItem(selectedId, rootItem);
        //        DeleteProfile(selectedItem);
        //    }

        //    Reload();
        //    Repaint();
        //}

        protected override void DoubleClickedItem(int id)
        {
            var rows = FindRows(new[] { id });
            var item = rows.FirstOrDefault();

            var tableItem = item as TreeViewItem<AssetTreeElement>;
            if (tableItem == null || tableItem.data.Profile == null)
            {
                return;
            }

            var userdata = new KeyValuePair<int, AssetProfile>(id, tableItem.data.Profile);
            OpenProfileEditor(userdata);
        }

        protected override void ContextClickedItem(int id)
        {
            var rows = FindRows(new[] { id });
            var item = rows.FirstOrDefault();

            var tableItem = item as TreeViewItem<AssetTreeElement>;
            if (tableItem == null || tableItem.data.Profile == null)
            {
                return;
            }
            var userdata = new KeyValuePair<int, AssetProfile>(id, tableItem.data.Profile);
            var menu = new GenericMenu();
            menu.AddItem(new GUIContent("Open Editor"), false, OpenProfileEditor, userdata);
            menu.AddItem(new GUIContent("Delete Profile"), false, DeleteProfile, userdata);
            menu.AddItem(new GUIContent("Rename Profile"), false, RenameProfile, userdata);
            menu.AddItem(new GUIContent("Add ChildProfile"), false, AddProfile, userdata);
            menu.AddItem(new GUIContent("Select ProfileAsset"), false, SelectProfileAsset, userdata);
            // menu.AddSeparator("");
            // menu.AddItem(new GUIContent("Execute"), false, o => ((KeyValuePair<int, AssetProfile>)o).Value?.Execute(), userdata);
            // menu.AddItem(new GUIContent("Reimport"), false, o => ((KeyValuePair<int, AssetProfile>)o).Value?.Reimport(), userdata);

            menu.ShowAsContext();
        }

        void OpenProfileEditor(object userdata)
        {
            var data = (KeyValuePair<int, AssetProfile>)userdata;
            var profile = data.Value;
            if (profile)
            {
                // AssetProfileWindow.ShowWindow(profile);
            }
        }

        void SelectProfileAsset(object userdata)
        {
            var data = (KeyValuePair<int, AssetProfile>)userdata;
            var profile = data.Value;
            if (!profile)
            {
                return;
            }

            Selection.activeObject = profile;
            EditorGUIUtility.PingObject(Selection.activeObject);
        }

        void DeleteProfile(object userdata)
        {
            var data = (KeyValuePair<int, AssetProfile>)userdata;
            var id = data.Key;
            var profile = data.Value;
            if (!profile)
            {
                return;
            }

            if (EditorUtility.DisplayDialog("删除AssetProfile", $"确定要删除 \"{profile.name}\" 吗?", "Yes", "No"))
            {
                treeModel.RemoveElements(new List<int>() { id }, false);
                AssetDatabase.DeleteAsset(AssetDatabase.GetAssetPath(profile));
                Reload();
                Repaint();
            }
        }

        void AddProfile(object userdata)
        {
            var data = (KeyValuePair<int, AssetProfile>)userdata;
            var selection = data.Key;
            TreeElement parent = (selection > 0 ? treeModel.Find(selection) : null) ?? treeModel.root;
            int depth = parent != null ? parent.depth + 1 : 0;
            int id = treeModel.GenerateUniqueID();
            var profile = AssetProfile.Create("AssetProfile");
            var element = new AssetTreeElement(profile.name, depth, id, profile);
            treeModel.AddElement(element, parent, 0);
            SetSelection(new[] { id }, TreeViewSelectionOptions.RevealAndFrame);
        }

        void RenameProfile(object userdata)
        {
            var data = (KeyValuePair<int, AssetProfile>)userdata;
            var rows = FindRows(new[] { data.Key });
            var item = rows.FirstOrDefault();

            var tableItem = item as TreeViewItem<AssetTreeElement>;
            if (tableItem == null || tableItem.data.Profile == null)
            {
                return;
            }
            BeginRename(tableItem);
        }

        public TreeViewItem<AssetTreeElement>[] GetSelectedItems()
        {
            var ids = GetSelection();
            if (ids.Count > 0)
            {
                return FindRows(ids).OfType<TreeViewItem<AssetTreeElement>>().ToArray();
            }

            return new TreeViewItem<AssetTreeElement>[0];
        }

        public static MultiColumnHeaderState CreateDefaultMultiColumnHeaderState(float treeViewWidth)
        {
            var columns = new[]
            {
                new MultiColumnHeaderState.Column
                {
                    headerContent = new GUIContent("Name"),
                    headerTextAlignment = TextAlignment.Left,
                    sortedAscending = true,
                    sortingArrowAlignment = TextAlignment.Center,
                    width = 200,
                    minWidth = 100,
                    autoResize = true,
                    allowToggleVisibility = false
                }
            };

            return new MultiColumnHeaderState(columns);
        }
    }
}