# AssetPipeline编辑器UI架构设计

## 1. 整体UI架构设计

### 1.1 主窗口架构模式
```
AssetPipelineMainWindow (主控制中心)
├── HeaderToolbar (顶部工具栏)
├── NavigationTabs (导航标签页)
├── ContentArea (主内容区域)
│   ├── ConfigurationPanel (配置面板)
│   ├── CheckResultPanel (检查结果面板) 
│   ├── DatabaseViewPanel (数据库视图面板)
│   └── SettingsPanel (设置面板)
├── SidePanel (右侧面板)
│   ├── PropertyInspector (属性检查器)
│   └── QuickActions (快速操作)
└── StatusBar (底部状态栏)
```

### 1.2 模块化窗口设计
```csharp
// 主窗口架构接口
public interface IMainWindowModule
{
    string ModuleName { get; }
    bool IsEnabled { get; set; }
    void OnGUI(Rect area);
    void OnEnable();
    void OnDisable();
}

// 具体模块实现
public class ConfigurationModule : IMainWindowModule
{
    public string ModuleName => "配置管理";
    public bool IsEnabled { get; set; } = true;
    
    public void OnGUI(Rect area)
    {
        // 绘制配置管理界面
        DrawAssetTreeEditor(area);
    }
}
```

## 2. 核心界面组件设计

### 2.1 AssetTree可视化编辑器
```csharp
public class AssetTreeEditor
{
    private TreeViewState treeViewState;
    private AssetTreeView treeView;
    private SearchField searchField;
    
    public void OnGUI(Rect rect)
    {
        // 顶部搜索栏
        var searchRect = new Rect(rect.x, rect.y, rect.width, 20);
        treeView.searchString = searchField.OnGUI(searchRect, treeView.searchString);
        
        // 树形视图区域
        var treeRect = new Rect(rect.x, rect.y + 25, rect.width * 0.6f, rect.height - 25);
        treeView.OnGUI(treeRect);
        
        // 右侧属性编辑区域
        var propertyRect = new Rect(rect.x + rect.width * 0.65f, rect.y + 25, rect.width * 0.35f, rect.height - 25);
        DrawSelectedNodeProperties(propertyRect);
    }
    
    private void DrawSelectedNodeProperties(Rect rect)
    {
        var selection = treeView.GetSelection();
        if (selection.Count == 1)
        {
            var item = treeView.FindItem(selection[0], treeView.rootItem);
            if (item is AssetProfileTreeItem profileItem)
            {
                DrawAssetProfileEditor(rect, profileItem.profile);
            }
        }
    }
}
```

### 2.2 检查结果展示界面
```csharp
public class CheckResultViewer
{
    private Vector2 scrollPosition;
    private CheckResultFilter currentFilter = CheckResultFilter.All;
    private string searchText = "";
    
    public void OnGUI(Rect rect)
    {
        // 顶部过滤器栏
        var filterRect = new Rect(rect.x, rect.y, rect.width, 30);
        DrawFilterBar(filterRect);
        
        // 结果列表区域
        var listRect = new Rect(rect.x, rect.y + 35, rect.width, rect.height - 35);
        DrawResultList(listRect);
    }
    
    private void DrawFilterBar(Rect rect)
    {
        GUILayout.BeginArea(rect);
        GUILayout.BeginHorizontal();
        
        // 过滤按钮组
        if (GUILayout.Toggle(currentFilter == CheckResultFilter.All, "全部", EditorStyles.toolbarButton))
            currentFilter = CheckResultFilter.All;
        if (GUILayout.Toggle(currentFilter == CheckResultFilter.Error, "错误", EditorStyles.toolbarButton))
            currentFilter = CheckResultFilter.Error;
        if (GUILayout.Toggle(currentFilter == CheckResultFilter.Warning, "警告", EditorStyles.toolbarButton))
            currentFilter = CheckResultFilter.Warning;
        
        GUILayout.FlexibleSpace();
        
        // 搜索框
        searchText = GUILayout.TextField(searchText, EditorStyles.toolbarSearchField, GUILayout.Width(200));
        
        GUILayout.EndHorizontal();
        GUILayout.EndArea();
    }
    
    private void DrawResultList(Rect rect)
    {
        var filteredResults = GetFilteredResults();
        
        scrollPosition = GUI.BeginScrollView(rect, scrollPosition, new Rect(0, 0, rect.width - 20, filteredResults.Count * 60));
        
        for (int i = 0; i < filteredResults.Count; i++)
        {
            var itemRect = new Rect(5, i * 60, rect.width - 25, 55);
            DrawResultItem(itemRect, filteredResults[i]);
        }
        
        GUI.EndScrollView();
    }
    
    private void DrawResultItem(Rect rect, CheckResult result)
    {
        // 状态图标
        var iconRect = new Rect(rect.x, rect.y + 5, 20, 20);
        GUI.DrawTexture(iconRect, GetStatusIcon(result.type));
        
        // 资源名称和路径
        var nameRect = new Rect(rect.x + 25, rect.y, rect.width - 100, 20);
        GUI.Label(nameRect, result.assetName, EditorStyles.boldLabel);
        
        var pathRect = new Rect(rect.x + 25, rect.y + 20, rect.width - 100, 15);
        GUI.Label(pathRect, result.assetPath, EditorStyles.miniLabel);
        
        // 问题描述
        var descRect = new Rect(rect.x + 25, rect.y + 35, rect.width - 100, 15);
        GUI.Label(descRect, result.message);
        
        // 操作按钮
        if (result.canAutoFix)
        {
            var fixRect = new Rect(rect.x + rect.width - 80, rect.y + 15, 70, 20);
            if (GUI.Button(fixRect, "一键修复"))
            {
                PerformAutoFix(result);
            }
        }
    }
}
```

### 2.3 数据库查询界面
```csharp
public class DatabaseQueryViewer
{
    private string sqlQuery = "";
    private Vector2 queryScrollPos;
    private Vector2 resultScrollPos;
    private DataTable queryResult;
    
    public void OnGUI(Rect rect)
    {
        // 查询输入区域
        var queryRect = new Rect(rect.x, rect.y, rect.width, rect.height * 0.3f);
        DrawQueryInput(queryRect);
        
        // 结果显示区域
        var resultRect = new Rect(rect.x, rect.y + rect.height * 0.35f, rect.width, rect.height * 0.65f);
        DrawQueryResult(resultRect);
    }
    
    private void DrawQueryInput(Rect rect)
    {
        GUILayout.BeginArea(rect);
        GUILayout.Label("SQL查询", EditorStyles.boldLabel);
        
        queryScrollPos = GUILayout.BeginScrollView(queryScrollPos, GUILayout.Height(rect.height - 50));
        sqlQuery = GUILayout.TextArea(sqlQuery, GUILayout.ExpandHeight(true));
        GUILayout.EndScrollView();
        
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("执行查询", GUILayout.Width(100)))
        {
            ExecuteQuery();
        }
        if (GUILayout.Button("清空", GUILayout.Width(60)))
        {
            sqlQuery = "";
        }
        GUILayout.EndHorizontal();
        
        GUILayout.EndArea();
    }
    
    private void DrawQueryResult(Rect rect)
    {
        if (queryResult == null) return;
        
        GUILayout.BeginArea(rect);
        GUILayout.Label($"查询结果 ({queryResult.Rows.Count} 行)", EditorStyles.boldLabel);
        
        resultScrollPos = GUILayout.BeginScrollView(resultScrollPos);
        
        // 绘制表格头部
        GUILayout.BeginHorizontal();
        foreach (DataColumn column in queryResult.Columns)
        {
            GUILayout.Label(column.ColumnName, EditorStyles.boldLabel, GUILayout.Width(120));
        }
        GUILayout.EndHorizontal();
        
        // 绘制表格内容
        foreach (DataRow row in queryResult.Rows)
        {
            GUILayout.BeginHorizontal();
            foreach (var item in row.ItemArray)
            {
                GUILayout.Label(item?.ToString() ?? "", GUILayout.Width(120));
            }
            GUILayout.EndHorizontal();
        }
        
        GUILayout.EndScrollView();
        GUILayout.EndArea();
    }
}
```

## 3. 高级UI组件

### 3.1 拖拽支持组件
```csharp
public class DragAndDropArea
{
    public static bool IsDropArea(Rect rect, out string[] droppedPaths)
    {
        droppedPaths = null;
        
        var currentEvent = Event.current;
        if (!rect.Contains(currentEvent.mousePosition))
            return false;
            
        switch (currentEvent.type)
        {
            case EventType.DragUpdated:
                DragAndDrop.visualMode = DragAndDropVisualMode.Copy;
                currentEvent.Use();
                return false;
                
            case EventType.DragPerform:
                DragAndDrop.AcceptDrag();
                droppedPaths = DragAndDrop.paths;
                currentEvent.Use();
                return true;
        }
        
        return false;
    }
    
    public static void DrawDropIndicator(Rect rect, string message = "拖拽文件到此处")
    {
        var style = new GUIStyle(GUI.skin.box)
        {
            alignment = TextAnchor.MiddleCenter,
            fontSize = 12
        };
        
        GUI.Box(rect, message, style);
    }
}
```

### 3.2 进度条组件
```csharp
public class ProgressBarOverlay
{
    private static ProgressBarOverlay _instance;
    public static ProgressBarOverlay Instance => _instance ?? (_instance = new ProgressBarOverlay());
    
    private bool isVisible;
    private string title;
    private string info;
    private float progress;
    private bool canCancel;
    private System.Action onCancel;
    
    public void Show(string title, string info, float progress, bool canCancel = false, System.Action onCancel = null)
    {
        this.isVisible = true;
        this.title = title;
        this.info = info;
        this.progress = Mathf.Clamp01(progress);
        this.canCancel = canCancel;
        this.onCancel = onCancel;
    }
    
    public void Hide()
    {
        isVisible = false;
    }
    
    public void OnGUI()
    {
        if (!isVisible) return;
        
        var rect = new Rect(Screen.width * 0.5f - 200, Screen.height * 0.5f - 50, 400, 100);
        
        GUI.Box(rect, "");
        
        var titleRect = new Rect(rect.x + 10, rect.y + 10, rect.width - 20, 20);
        GUI.Label(titleRect, title, EditorStyles.boldLabel);
        
        var infoRect = new Rect(rect.x + 10, rect.y + 30, rect.width - 20, 15);
        GUI.Label(infoRect, info);
        
        var progressRect = new Rect(rect.x + 10, rect.y + 50, rect.width - 20, 20);
        EditorGUI.ProgressBar(progressRect, progress, $"{progress:P0}");
        
        if (canCancel)
        {
            var cancelRect = new Rect(rect.x + rect.width - 80, rect.y + 75, 70, 20);
            if (GUI.Button(cancelRect, "取消"))
            {
                onCancel?.Invoke();
                Hide();
            }
        }
    }
}
```

### 3.3 上下文菜单系统
```csharp
public class ContextMenuBuilder
{
    private GenericMenu menu;
    
    public ContextMenuBuilder()
    {
        menu = new GenericMenu();
    }
    
    public ContextMenuBuilder AddItem(string itemName, bool isChecked, System.Action action)
    {
        menu.AddItem(new GUIContent(itemName), isChecked, () => action?.Invoke());
        return this;
    }
    
    public ContextMenuBuilder AddSeparator()
    {
        menu.AddSeparator("");
        return this;
    }
    
    public ContextMenuBuilder AddDisabledItem(string itemName)
    {
        menu.AddDisabledItem(new GUIContent(itemName));
        return this;
    }
    
    public void Show(Vector2 position)
    {
        menu.DropDown(new Rect(position, Vector2.zero));
    }
    
    // 扩展方法支持链式调用
    public static ContextMenuBuilder ForAsset(string assetPath)
    {
        var builder = new ContextMenuBuilder();
        
        builder.AddItem("打开文件夹", false, () => EditorUtility.RevealInFinder(assetPath))
               .AddItem("在Project中选择", false, () => {
                   var obj = AssetDatabase.LoadAssetAtPath<Object>(assetPath);
                   Selection.activeObject = obj;
                   EditorGUIUtility.PingObject(obj);
               })
               .AddSeparator()
               .AddItem("检查资源", false, () => AssetChecker.CheckAsset(assetPath))
               .AddItem("重新导入", false, () => AssetDatabase.ImportAsset(assetPath, ImportAssetOptions.ForceUpdate));
               
        return builder;
    }
}
```

## 4. 界面主题与样式

### 4.1 自定义样式系统
```csharp
public static class AssetPipelineStyles
{
    private static Dictionary<string, GUIStyle> styleCache = new Dictionary<string, GUIStyle>();
    
    public static GUIStyle HeaderStyle
    {
        get
        {
            if (!styleCache.TryGetValue("Header", out GUIStyle style))
            {
                style = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 14,
                    alignment = TextAnchor.MiddleLeft
                };
                styleCache["Header"] = style;
            }
            return style;
        }
    }
    
    public static GUIStyle ErrorStyle
    {
        get
        {
            if (!styleCache.TryGetValue("Error", out GUIStyle style))
            {
                style = new GUIStyle(EditorStyles.label)
                {
                    normal = { textColor = Color.red }
                };
                styleCache["Error"] = style;
            }
            return style;
        }
    }
    
    public static GUIStyle WarningStyle
    {
        get
        {
            if (!styleCache.TryGetValue("Warning", out GUIStyle style))
            {
                style = new GUIStyle(EditorStyles.label)
                {
                    normal = { textColor = new Color(1f, 0.5f, 0f) }
                };
                styleCache["Warning"] = style;
            }
            return style;
        }
    }
    
    public static GUIStyle SuccessStyle
    {
        get
        {
            if (!styleCache.TryGetValue("Success", out GUIStyle style))
            {
                style = new GUIStyle(EditorStyles.label)
                {
                    normal = { textColor = Color.green }
                };
                styleCache["Success"] = style;
            }
            return style;
        }
    }
}
```

### 4.2 图标资源管理
```csharp
public static class AssetPipelineIcons
{
    private static Dictionary<string, Texture2D> iconCache = new Dictionary<string, Texture2D>();
    
    public static Texture2D GetIcon(string iconName)
    {
        if (!iconCache.TryGetValue(iconName, out Texture2D icon))
        {
            icon = EditorGUIUtility.IconContent(iconName).image as Texture2D;
            if (icon != null)
                iconCache[iconName] = icon;
        }
        return icon;
    }
    
    public static Texture2D ErrorIcon => GetIcon("console.erroricon");
    public static Texture2D WarningIcon => GetIcon("console.warnicon");
    public static Texture2D InfoIcon => GetIcon("console.infoicon");
    public static Texture2D SuccessIcon => GetIcon("TestPassed");
    
    public static Texture2D FolderIcon => GetIcon("Folder Icon");
    public static Texture2D AssetIcon => GetIcon("Asset Icon");
    public static Texture2D SettingsIcon => GetIcon("Settings");
    public static Texture2D RefreshIcon => GetIcon("Refresh");
}
```

## 5. 性能优化策略

### 5.1 UI虚拟化实现
```csharp
public class VirtualizedScrollView
{
    private Vector2 scrollPosition;
    private float itemHeight;
    private int totalItemCount;
    private System.Func<int, object> getItemData;
    private System.Action<Rect, object> drawItem;
    
    public VirtualizedScrollView(float itemHeight, System.Func<int, object> getItemData, System.Action<Rect, object> drawItem)
    {
        this.itemHeight = itemHeight;
        this.getItemData = getItemData;
        this.drawItem = drawItem;
    }
    
    public void OnGUI(Rect rect, int totalItemCount)
    {
        this.totalItemCount = totalItemCount;
        
        var viewRect = new Rect(0, 0, rect.width - 16, totalItemCount * itemHeight);
        scrollPosition = GUI.BeginScrollView(rect, scrollPosition, viewRect);
        
        // 计算可见范围
        int startIndex = Mathf.FloorToInt(scrollPosition.y / itemHeight);
        int endIndex = Mathf.Min(startIndex + Mathf.CeilToInt(rect.height / itemHeight) + 1, totalItemCount);
        
        // 只绘制可见项
        for (int i = startIndex; i < endIndex; i++)
        {
            var itemRect = new Rect(0, i * itemHeight, rect.width - 16, itemHeight);
            var itemData = getItemData(i);
            drawItem(itemRect, itemData);
        }
        
        GUI.EndScrollView();
    }
}
```

### 5.2 数据缓存机制
```csharp
public class UIDataCache
{
    private Dictionary<string, CacheEntry> cache = new Dictionary<string, CacheEntry>();
    private float cacheTimeout = 60f; // 缓存60秒
    
    private class CacheEntry
    {
        public object data;
        public float timestamp;
    }
    
    public T GetCachedData<T>(string key, System.Func<T> dataProvider) where T : class
    {
        if (cache.TryGetValue(key, out CacheEntry entry))
        {
            if (Time.realtimeSinceStartup - entry.timestamp < cacheTimeout)
            {
                return entry.data as T;
            }
            else
            {
                cache.Remove(key);
            }
        }
        
        var data = dataProvider();
        cache[key] = new CacheEntry
        {
            data = data,
            timestamp = Time.realtimeSinceStartup
        };
        
        return data;
    }
    
    public void InvalidateCache(string key = null)
    {
        if (string.IsNullOrEmpty(key))
            cache.Clear();
        else
            cache.Remove(key);
    }
}
```

## 6. 界面状态管理

### 6.1 窗口状态持久化
```csharp
[System.Serializable]
public class WindowLayoutState
{
    public int selectedTab;
    public Vector2 scrollPosition;
    public bool[] panelVisibility;
    public float[] splitterPositions;
    public Dictionary<string, bool> expandedStates;
    
    public WindowLayoutState()
    {
        selectedTab = 0;
        scrollPosition = Vector2.zero;
        panelVisibility = new bool[4] { true, true, false, false };
        splitterPositions = new float[3] { 0.3f, 0.7f, 0.8f };
        expandedStates = new Dictionary<string, bool>();
    }
}

public class WindowStateManager
{
    private const string PREF_KEY = "AssetPipeline.WindowState";
    private WindowLayoutState currentState;
    
    public WindowLayoutState CurrentState
    {
        get
        {
            if (currentState == null)
                LoadState();
            return currentState;
        }
    }
    
    public void SaveState()
    {
        if (currentState != null)
        {
            var json = JsonUtility.ToJson(currentState);
            EditorPrefs.SetString(PREF_KEY, json);
        }
    }
    
    public void LoadState()
    {
        var json = EditorPrefs.GetString(PREF_KEY, "");
        if (!string.IsNullOrEmpty(json))
        {
            currentState = JsonUtility.FromJson<WindowLayoutState>(json);
        }
        else
        {
            currentState = new WindowLayoutState();
        }
    }
    
    public void ResetToDefault()
    {
        currentState = new WindowLayoutState();
        SaveState();
    }
}
```

## 7. 交互设计最佳实践

### 7.1 操作反馈设计
- **即时反馈**：按钮点击、拖拽操作立即提供视觉反馈
- **状态指示**：清晰显示当前操作状态（处理中、完成、错误）
- **进度显示**：长时间操作显示进度条和预估时间
- **错误处理**：友好的错误信息和解决建议

### 7.2 美术工作流适配
- **拖拽支持**：支持文件和文件夹的拖拽导入
- **右键菜单**：提供上下文相关的操作选项
- **快捷键**：常用操作的键盘快捷方式
- **批量操作**：支持多选和批量处理

### 7.3 可访问性设计
- **颜色辅助**：不仅依赖颜色，还使用图标和文字
- **字体大小**：支持用户自定义字体大小
- **键盘导航**：完整的键盘操作支持
- **工具提示**：重要功能提供详细的工具提示 