<execution>
  <constraint>
    ## Unity编辑器UI设计的客观限制
    - **IMGUI性能约束**：OnGUI方法每帧调用，布局计算开销较大
    - **EditorWindow生命周期**：窗口可能被意外关闭或重新创建
    - **Unity版本兼容性**：必须支持Unity 2018.4 LTS的API限制
    - **内存管理约束**：长期运行的编辑器窗口容易产生内存泄漏
    - **主线程限制**：所有UI操作必须在主线程执行
    - **平台差异性**：Windows/Mac的编辑器界面存在细微差异
  </constraint>

  <rule>
    ## Unity编辑器UI设计强制规则
    - **响应性保证**：任何用户操作的响应时间不得超过200ms
    - **内存增长控制**：长期运行内存增长不得超过100MB/hour
    - **错误处理强制**：所有用户操作必须有明确的错误反馈机制
    - **状态持久化**：用户的界面布局和偏好设置必须能够保存和恢复
    - **一致性原则**：界面交互必须与Unity标准编辑器保持一致
    - **无阻塞原则**：长时间操作必须提供进度指示和取消机制
  </rule>

  <guideline>
    ## Unity编辑器UI设计指导原则
    - **美术优先**：界面设计以美术人员的使用习惯为第一优先级
    - **渐进式披露**：复杂功能通过分层展示逐步呈现给用户
    - **即时反馈**：用户操作后立即提供视觉或文字反馈
    - **容错设计**：支持操作撤销，提供明确的确认机制
    - **性能可见**：长时间操作提供进度条和剩余时间估算
    - **帮助集成**：关键功能点提供内置帮助和操作指导
  </guideline>

  <process>
    ## Unity编辑器UI设计具体流程

    ### Phase 1: 需求分析和用户研究
    ```
    1. 用户角色分析
       - 主要用户：美术人员（模型师、贴图师、特效师）
       - 次要用户：技术美术、程序员
       - 使用场景：日常资源导入、批量处理、问题修复

    2. 工作流程调研
       - 观察美术的实际操作习惯
       - 记录高频操作和痛点
       - 分析现有工具的使用反馈

    3. 功能优先级排序
       - P0：基础资源检查和修复
       - P1：批量处理和规则配置
       - P2：高级分析和报告生成
       - P3：自定义扩展和集成功能
    ```

    ### Phase 2: 界面架构设计
    ```
    1. 窗口结构规划
       主窗口：AssetPipelineMainWindow (EditorWindow)
       ├── 工具栏区域：快速操作按钮
       ├── 导航区域：功能模块切换
       ├── 内容区域：主要工作区域
       ├── 侧边栏：属性面板和快速设置
       └── 状态栏：进度显示和消息提示

    2. 模块化设计
       - 配置管理模块：AssetTree和AssetProfile的可视化编辑
       - 检查结果模块：问题展示和修复操作界面
       - 数据分析模块：资源信息和依赖关系可视化
       - 系统设置模块：工具配置和用户偏好

    3. 数据流设计
       用户操作 → UI事件 → 数据更新 → 界面刷新
       后台处理 → 进度回调 → 状态更新 → 用户反馈
    ```

    ### Phase 3: 具体界面实现
    ```
    1. 主窗口实现（AssetPipelineMainWindow）
       ```csharp
       public class AssetPipelineMainWindow : EditorWindow
       {
           private int selectedTabIndex = 0;
           private Vector2 scrollPosition;
           private bool showAdvancedOptions = false;
           
           [MenuItem("Tools/Asset Pipeline/Main Window")]
           public static void ShowWindow()
           {
               var window = GetWindow<AssetPipelineMainWindow>();
               window.titleContent = new GUIContent("Asset Pipeline");
               window.minSize = new Vector2(800, 600);
           }
           
           private void OnGUI()
           {
               DrawToolbar();
               DrawTabNavigation();
               DrawMainContent();
               DrawStatusBar();
           }
       }
       ```

    2. 配置编辑界面实现
       - AssetTree的树形展示和编辑
       - AssetProfile的规则配置界面
       - AssetFilter的条件设置面板
       - 实时预览和验证反馈

    3. 检查结果界面实现
       - 问题分类列表（错误/警告/信息）
       - 详细问题描述和解决建议
       - 一键修复和批量操作按钮
       - 修复进度和结果反馈
    ```

    ### Phase 4: 性能优化实现
    ```
    1. UI渲染优化
       - 使用GUILayout.BeginVertical/EndVertical减少重绘
       - 实现虚拟化列表支持大数据量显示
       - 缓存GUI计算结果避免重复计算
       
    2. 数据加载优化
       - 实现异步数据加载机制
       - 使用EditorCoroutine避免主线程阻塞
       - 添加数据分页和按需加载

    3. 内存管理优化
       - 及时释放不再使用的GUI资源
       - 使用对象池管理频繁创建的UI元素
       - 定期清理缓存数据
    ```

    ### Phase 5: 用户体验优化
    ```
    1. 交互体验优化
       - 支持拖拽操作（文件、配置规则）
       - 提供右键上下文菜单
       - 实现键盘快捷键支持
       - 添加操作撤销/重做功能

    2. 视觉设计优化
       - 使用统一的颜色和图标系统
       - 提供清晰的状态指示
       - 实现平滑的动画过渡效果
       - 支持主题切换（浅色/深色）

    3. 帮助和指导
       - 集成上下文帮助系统
       - 提供操作向导和引导
       - 添加工具提示和说明文字
       - 创建操作录屏和文档
    ```
  </process>

  <criteria>
    ## Unity编辑器UI质量评价标准

    ### 性能指标
    - ✅ 界面响应时间 < 200ms
    - ✅ 大数据量（1000+项）渲染流畅
    - ✅ 长期运行内存稳定（增长 < 100MB/hour）
    - ✅ CPU占用率在空闲时 < 5%

    ### 用户体验指标
    - ✅ 新用户5分钟内能完成基本操作
    - ✅ 常用操作点击次数 ≤ 3次
    - ✅ 错误操作有明确的反馈和恢复机制
    - ✅ 界面布局符合美术人员的操作习惯

    ### 功能完整性
    - ✅ 覆盖PPT中描述的所有核心功能
    - ✅ 支持AssetTree/AssetProfile的完整可视化编辑
    - ✅ 提供检查结果的详细展示和修复功能
    - ✅ 集成数据库查询和分析功能

    ### 稳定性指标
    - ✅ 连续运行8小时不出现界面异常
    - ✅ 支持Unity编辑器的重新编译和刷新
    - ✅ 异常情况下能够自动恢复界面状态
    - ✅ 与Unity其他编辑器窗口兼容良好

    ### 可维护性
    - ✅ 代码结构清晰，模块化程度高
    - ✅ UI组件可复用，易于扩展新功能
    - ✅ 配置和数据分离，便于版本控制
    - ✅ 提供完整的开发文档和注释
  </criteria>
</execution> 