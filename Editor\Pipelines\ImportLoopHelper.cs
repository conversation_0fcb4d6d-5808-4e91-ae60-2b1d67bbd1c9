using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using AssetPipeline.Core;
using Logger = AssetPipeline.Core.Logger;

namespace AssetPipeline.Pipelines
{
    /// <summary>
    /// 导入循环检测辅助类
    /// </summary>
    public static class ImportLoopHelper
    {
        private const int MAX_IMPORT_ROUNDS = 100;
        private const int MODIFICATION_CHECK_THRESHOLD = 5;
        
        /// <summary>
        /// 统一的循环检测入口方法
        /// </summary>
        public static bool CheckLoop(string assetPath, AssetImporter importer, ImportContext context)
        {
            if (string.IsNullOrEmpty(assetPath) || importer == null || context == null) 
                return false;
            
            // 获取导入计数
            var currentRound = context.GetImportCount(assetPath);
            
            // 只有在导入轮次大于阈值时才检查修改记录
            if (currentRound <= MODIFICATION_CHECK_THRESHOLD)
            {
                Logger.Debug(LogModule.Pipeline, 
                    $"跳过循环检查: {assetPath} (轮次: {currentRound} <= 阈值: {MODIFICATION_CHECK_THRESHOLD})");
                return false;
            }
            
            // 检查操作次数是否超过最大值
            if (currentRound > MAX_IMPORT_ROUNDS)
            {
                Logger.Error(LogModule.Pipeline, 
                    $"检测到导入循环: {assetPath} (轮次: {currentRound} > 最大值: {MAX_IMPORT_ROUNDS})");
                return true;
            }
            
            try
            {
                return CheckModificationLoop(assetPath, importer, context, currentRound);
            }
            catch (Exception e)
            {
                Logger.LogException(LogModule.Pipeline, e, $"检查导入循环失败: {assetPath}");
                return false;
            }
        }
        
        /// <summary>
        /// 检查修改循环
        /// </summary>
        private static bool CheckModificationLoop(string assetPath, AssetImporter importer, ImportContext context, int currentRound)
        {
            // 序列化当前importer状态
            var currentSerialized = SerializeImporter(importer);
            
            // 获取缓存的序列化数据
            var cachedImporters = context.GetData<Dictionary<string, List<KeyValuePair<string, string>>>>("CachedSerializedImporters", 
                new Dictionary<string, List<KeyValuePair<string, string>>>());
            
            if (!cachedImporters.ContainsKey(assetPath))
            {
                // 第一次，直接缓存
                cachedImporters[assetPath] = currentSerialized;
                context.SetData("CachedSerializedImporters", cachedImporters);
                return false;
            }
            
            var cachedSerialized = cachedImporters[assetPath];
            
            // 比较属性变化
            var diffs = CompareProps(cachedSerialized, currentSerialized);
            
            if (diffs.Count > 0)
            {
                // 生成修改记录
                var revisionRecord = GenerateRevisionRecord(importer, diffs);
                
                // 获取修改历史
                var modificationHistory = context.GetData<Dictionary<string, List<string>>>("PathModificationHistory", 
                    new Dictionary<string, List<string>>());
                
                if (!modificationHistory.ContainsKey(assetPath))
                    modificationHistory[assetPath] = new List<string>();
                    
                var history = modificationHistory[assetPath];
                
                // 检查循环
                if (history.Any(r => r == revisionRecord))
                {
                    Logger.Error(LogModule.Pipeline, 
                        $"检测到导入循环: {assetPath} (轮次: {currentRound})");
                    
                    // 输出循环信息
                    OutputLoop(assetPath, history, revisionRecord);
                    return true;
                }
                
                // 添加新的修改记录
                history.Add(revisionRecord);
                modificationHistory[assetPath] = history;
                context.SetData("PathModificationHistory", modificationHistory);
                
                // 更新缓存
                cachedImporters[assetPath] = currentSerialized;
                context.SetData("CachedSerializedImporters", cachedImporters);
                
                Logger.Debug(LogModule.Pipeline, 
                    $"记录修改: {assetPath} (轮次: {currentRound}, 变化数: {diffs.Count})");
            }
            
            return false;
        }
        
        /// <summary>
        /// 序列化Importer
        /// </summary>
        public static List<KeyValuePair<string, string>> SerializeImporter(AssetImporter importer)
        {
            var result = new List<KeyValuePair<string, string>>();
            
            if (importer == null)
            {
                result.Add(new KeyValuePair<string, string>("Importer", "{Null}"));
                return result;
            }
            
            // 添加类型信息
            result.Add(new KeyValuePair<string, string>(importer.GetType().ToString(), "{NotNull}"));
            
            // 序列化对象属性
            Serialize(importer, "", result);
            
            return result;
        }
        
        /// <summary>
        /// 序列化对象
        /// </summary>
        private static void Serialize(object obj, string prefix, List<KeyValuePair<string, string>> result, bool getFields = false)
        {
            if (obj == null)
            {
                result.Add(new KeyValuePair<string, string>($"{prefix}", "{Null}"));
                return;
            }

            var type = obj.GetType();
            System.Reflection.MemberInfo[] members = getFields ? 
                type.GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance) as System.Reflection.MemberInfo[] : 
                type.GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance) as System.Reflection.MemberInfo[];

            foreach (var member in members)
            {
                if (!getFields && (!(member as System.Reflection.PropertyInfo).CanWrite || 
                    member.Name.Equals("generateMaterials", StringComparison.OrdinalIgnoreCase))) 
                    continue;

                var pObj = getFields ? (member as System.Reflection.FieldInfo).GetValue(obj) : (member as System.Reflection.PropertyInfo).GetValue(obj);
                var pType = getFields ? (member as System.Reflection.FieldInfo).FieldType : (member as System.Reflection.PropertyInfo).PropertyType;
                var pName = member.Name;

                SerializeValue(pObj, pType, $"{prefix}.{pName}", result);
            }

            // 特殊处理TextureImporter
            if (typeof(TextureImporter).IsAssignableFrom(type))
            {
                var textureImporter = obj as TextureImporter;
                SerializePlatformSettings(textureImporter, "Standalone", prefix, result);
                SerializePlatformSettings(textureImporter, "Android", prefix, result);
                SerializePlatformSettings(textureImporter, "iPhone", prefix, result);
            }
        }
        
        /// <summary>
        /// 序列化值
        /// </summary>
        private static void SerializeValue(object value, Type type, string path, List<KeyValuePair<string, string>> result)
        {
            if (type.IsPrimitive || type.IsEnum)
            {
                result.Add(new KeyValuePair<string, string>(path, value?.ToString() ?? "{Null}"));
            }
            else if (type == typeof(string))
            {
                result.Add(new KeyValuePair<string, string>(path, value == null ? "{Null}" : $"\"{value}\""));
            }
            else if (typeof(UnityEngine.Object).IsAssignableFrom(type))
            {
                result.Add(new KeyValuePair<string, string>(path, 
                    value == null ? "{Null}" : AssetDatabase.GetAssetPath(value as UnityEngine.Object)));
            }
            else if (type.IsArray)
            {
                SerializeArray(value, type, path, result);
            }
            else if (type.IsValueType) // Struct
            {
                result.Add(new KeyValuePair<string, string>(path, value?.ToString() ?? "{Null}"));
            }
            else
            {
                result.Add(new KeyValuePair<string, string>(path, value == null ? "{Null}" : "{Object}"));
            }
        }
        
        /// <summary>
        /// 序列化数组
        /// </summary>
        private static void SerializeArray(object array, Type arrayType, string path, List<KeyValuePair<string, string>> result)
        {
            if (array == null)
            {
                result.Add(new KeyValuePair<string, string>(path, "{Null}"));
                return;
            }

            var arr = array as Array;
            result.Add(new KeyValuePair<string, string>($"{path}.Length", arr.Length.ToString()));

            var elementType = arrayType.GetElementType();
            for (int i = 0; i < arr.Length; i++)
            {
                SerializeValue(arr.GetValue(i), elementType, $"{path}[{i}]", result);
            }
        }
        
        /// <summary>
        /// 序列化平台设置
        /// </summary>
        private static void SerializePlatformSettings(TextureImporter importer, string platform, string prefix, List<KeyValuePair<string, string>> result)
        {
            try
            {
                var settings = importer.GetPlatformTextureSettings(platform);
                if (settings != null)
                {
                    var settingsPath = $"{prefix}.PlatformSettings.{platform}";
                    Serialize(settings, settingsPath, result);
                }
            }
            catch (Exception e)
            {
                result.Add(new KeyValuePair<string, string>($"{prefix}.PlatformSettings.{platform}", $"{{Error: {e.Message}}}"));
            }
        }
        
        /// <summary>
        /// 比较属性变化
        /// </summary>
        public static HashSet<KeyValuePair<string, string>> CompareProps(List<KeyValuePair<string, string>> a, List<KeyValuePair<string, string>> b)
        {
            var diffs = new HashSet<KeyValuePair<string, string>>();
            if (a == null || b == null)
                return diffs;
            
            var dictA = a.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
            var dictB = b.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
            
            foreach (var kv in dictA)
            {
                if (dictB.TryGetValue(kv.Key, out var str) && kv.Value != str)
                {
                    diffs.Add(new KeyValuePair<string, string>(kv.Key, str));
                }
            }
            foreach (var kv in dictB)
            {
                if (dictA.TryGetValue(kv.Key, out var str) && kv.Value != str)
                {
                    diffs.Add(new KeyValuePair<string, string>(kv.Key, kv.Value));
                }
            }
            
            return diffs;
        }
        
        /// <summary>
        /// 生成修改记录
        /// </summary>
        public static string GenerateRevisionRecord(AssetImporter importer, HashSet<KeyValuePair<string, string>> diffs)
        {
            var importerType = importer?.GetType().Name ?? "Unknown";
            var changes = diffs.Select(kvp => $"{kvp.Key}={kvp.Value}").OrderBy(s => s).ToArray();
            
            return $"{importerType}:[{string.Join(",", changes)}]";
        }
        
        /// <summary>
        /// 输出循环信息
        /// </summary>
        public static void OutputLoop(string assetPath, List<string> history, string currentRevision)
        {
            Logger.Error(LogModule.Pipeline, 
                $"检测到导入循环: {assetPath}");
                
            Logger.Error(LogModule.Pipeline, "修改历史记录:");
            for (int i = 0; i < history.Count; i++)
            {
                Logger.Error(LogModule.Pipeline, $"  {i + 1}: {history[i]}");
            }
            
            Logger.Error(LogModule.Pipeline, $"当前修改: {currentRevision}");
        }
    }
} 