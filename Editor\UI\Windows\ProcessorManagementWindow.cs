// using System.Collections.Generic;
// using System.Linq;
// using UnityEngine;
// using UnityEditor;
// using AssetPipeline.Core;
// using AssetPipeline.Processors;
//
// namespace AssetPipeline.UI.Windows
// {
//     public class ProcessorManagementWindow : EditorWindow
//     {
//         private AssetFilter targetFilter;
//         private Vector2 scrollPosition;
//         
//         // 可用的处理器类型
//         private readonly System.Type[] availableProcessorTypes = {
//             // 这里需要根据实际项目中的处理器来配置
//             // typeof(TextureProcessor),
//             // typeof(ModelProcessor),
//             // typeof(AudioProcessor),
//         };
//         
//         public static void ShowWindow(AssetFilter filter)
//         {
//             var window = GetWindow<ProcessorManagementWindow>("处理器管理");
//             window.targetFilter = filter;
//             window.minSize = new Vector2(400, 300);
//             window.Show();
//         }
//         
//         void OnGUI()
//         {
//             if (targetFilter == null)
//             {
//                 EditorGUILayout.HelpBox("没有选中的过滤器", MessageType.Warning);
//                 if (GUILayout.Button("关闭"))
//                 {
//                     Close();
//                 }
//                 return;
//             }
//             
//             EditorGUILayout.LabelField($"管理过滤器: {targetFilter.DisplayName}", EditorStyles.boldLabel);
//             EditorGUILayout.Space();
//             
//             var processors = targetFilter.Processors;
//             EditorGUILayout.LabelField($"当前处理器数量: {processors?.Count ?? 0}");
//             
//             if (GUILayout.Button("在Inspector中编辑"))
//             {
//                 Selection.activeObject = targetFilter;
//                 EditorGUIUtility.PingObject(targetFilter);
//             }
//             
//             if (GUILayout.Button("关闭"))
//             {
//                 Close();
//             }
//         }
//         
//         #region Processor Management Methods
//         
//         void RemoveProcessor(int index)
//         {
//             if (index < 0 || index >= targetFilter.Processors.Count) return;
//             
//             var processor = targetFilter.Processors[index];
//             if (EditorUtility.DisplayDialog("确认删除", 
//                 $"确定要删除处理器 '{processor.GetType().Name}' 吗？", "删除", "取消"))
//             {
//                 targetFilter.RemoveProcessor(processor);
//                 
//                 // 删除处理器资源文件
//                 var processorPath = AssetDatabase.GetAssetPath(processor);
//                 if (!string.IsNullOrEmpty(processorPath))
//                 {
//                     AssetDatabase.DeleteAsset(processorPath);
//                 }
//                 
//                 Debug.Log($"[ProcessorManagement] 成功删除处理器: {processor.GetType().Name}");
//             }
//         }
//         
//         void AddTextureCompressionProcessor()
//         {
//             // 创建示例处理器（实际项目中需要根据具体的处理器类型来实现）
//             var processor = CreateSimpleProcessor("TextureCompressionProcessor", "处理贴图压缩设置");
//             AddProcessorToFilter(processor);
//         }
//         
//         void AddModelOptimizationProcessor()
//         {
//             var processor = CreateSimpleProcessor("ModelOptimizationProcessor", "优化模型设置");
//             AddProcessorToFilter(processor);
//         }
//         
//         void AddAudioCompressionProcessor()
//         {
//             var processor = CreateSimpleProcessor("AudioCompressionProcessor", "处理音频压缩设置");
//             AddProcessorToFilter(processor);
//         }
//         
//         void AddLogProcessor()
//         {
//             var processor = CreateSimpleProcessor("LogProcessor", "输出处理日志");
//             AddProcessorToFilter(processor);
//         }
//         
//         void AddValidationProcessor()
//         {
//             var processor = CreateSimpleProcessor("ValidationProcessor", "验证资源有效性");
//             AddProcessorToFilter(processor);
//         }
//         
//         void AddCustomProcessor(System.Type processorType)
//         {
//             try
//             {
//                 var processor = ScriptableObject.CreateInstance(processorType) as AssetProcessor;
//                 if (processor != null)
//                 {
//                     processor.name = processorType.Name;
//                     AddProcessorToFilter(processor);
//                 }
//                 else
//                 {
//                     Debug.LogError($"[ProcessorManagement] 无法创建处理器类型: {processorType.Name}");
//                 }
//             }
//             catch (System.Exception ex)
//             {
//                 Debug.LogError($"[ProcessorManagement] 创建自定义处理器失败: {ex.Message}");
//             }
//         }
//         
//         AssetProcessor CreateSimpleProcessor(string typeName, string description)
//         {
//             // 创建一个基础的处理器实例
//             // 实际项目中应该根据具体的处理器类型来创建
//             var processor = CreateInstance<AssetProcessor>();
//             processor.name = $"{typeName}_{System.DateTime.Now:HHmmss}";
//             
//             return processor;
//         }
//         
//         void AddProcessorToFilter(AssetProcessor processor)
//         {
//             try
//             {
//                 // 保存处理器资源
//                 var processorPath = AssetDatabase.GenerateUniqueAssetPath(
//                     $"Assets/Editor/AssetPipeline/Data/Profiles/Processors/{processor.name}.asset");
//                 
//                 var directory = System.IO.Path.GetDirectoryName(processorPath);
//                 if (!System.IO.Directory.Exists(directory))
//                 {
//                     System.IO.Directory.CreateDirectory(directory);
//                 }
//                 
//                 AssetDatabase.CreateAsset(processor, processorPath);
//                 
//                 // 添加到过滤器
//                 targetFilter.AddProcessor(processor);
//                 EditorUtility.SetDirty(targetFilter);
//                 AssetDatabase.SaveAssets();
//                 
//                 Debug.Log($"[ProcessorManagement] 成功添加处理器: {processor.GetType().Name}");
//             }
//             catch (System.Exception ex)
//             {
//                 Debug.LogError($"[ProcessorManagement] 添加处理器失败: {ex.Message}");
//             }
//         }
//         
//         #endregion
//     }
// } 