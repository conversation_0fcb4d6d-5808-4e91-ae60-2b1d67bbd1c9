using System;
using System.Text;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

namespace AssetPipeline.Core
{
    /// <summary>
    /// Asset Pipeline专用日志级别
    /// </summary>
    public enum LogLevel
    {
        Debug = 0,      // 调试信息
        Info = 1,       // 一般信息
        Warning = 2,    // 警告
        Error = 3,      // 错误
        None = 99       // 不输出日志
    }

    /// <summary>
    /// 日志模块分类
    /// </summary>
    [System.Flags]
    public enum LogModule
    {
        Core = 1 << 0,          // 核心系统
        Config = 1 << 1,        // 配置系统
        Pipeline = 1 << 2,      // 管线系统
        Database = 1 << 3,      // 数据库
        UI = 1 << 4,           // 用户界面
        AssetImport = 1 << 5,   // 资源导入
        Processor = 1 << 6,    // 资产处理器
        All = ~0               // 所有模块
    }

    /// <summary>
    /// 结构化日志数据
    /// </summary>
    public struct LogData
    {
        public LogLevel Level;
        public LogModule Module;
        public string Message;
        public DateTime Timestamp;
        public string StackTrace;
        public Dictionary<string, object> Context;
        
        public LogData(LogLevel level, LogModule module, string message)
        {
            Level = level;
            Module = module;
            Message = message;
            Timestamp = DateTime.Now;
            StackTrace = null;
            Context = null;
        }
    }

    /// <summary>
    /// Asset Pipeline统一日志系统 - 性能优化版
    /// 特性：缓存优化、字符串池、结构化日志、条件编译、批量处理
    /// </summary>
    public static class Logger
    {
        #region 常量定义
        
        private const string PREFS_GLOBAL_LEVEL = "AssetPipeline_Log_GlobalLevel";
        private const string PREFS_ENABLED_MODULES = "AssetPipeline_Log_EnabledModules";
        private const string PREFS_SHOW_TIMESTAMP = "AssetPipeline_Log_ShowTimestamp";
        private const string PREFS_SHOW_MODULE = "AssetPipeline_Log_ShowModule";
        private const string PREFS_SHOW_STACKTRACE = "AssetPipeline_Log_ShowStackTrace";
        private const string PREFS_ENABLE_FILE_LOG = "AssetPipeline_Log_EnableFileLog";
        private const string PREFS_MAX_FILE_SIZE = "AssetPipeline_Log_MaxFileSize";
        
        private const int DEFAULT_MAX_FILE_SIZE_MB = 10;
        private const int STRING_BUILDER_INITIAL_CAPACITY = 256;
        private const int BUFFER_QUEUE_CAPACITY = 100;
        
        #endregion
        
        #region 缓存字段
        
        // 设置缓存，减少EditorPrefs访问
        private static LogLevel s_globalLogLevel = LogLevel.Info;
        private static LogModule s_enabledModules = LogModule.All;
        private static bool s_showTimestamp = true;
        private static bool s_showModule = true;
        private static bool s_showStackTrace = false;
        private static bool s_enableFileLog = false;
        private static int s_maxFileSizeMB = DEFAULT_MAX_FILE_SIZE_MB;
        private static bool s_settingsLoaded = false;
        
        // 性能优化缓存
        private static readonly StringBuilder s_sharedStringBuilder = new StringBuilder(STRING_BUILDER_INITIAL_CAPACITY);
        private static readonly Dictionary<LogModule, string> s_moduleDisplayNameCache = new Dictionary<LogModule, string>();
        private static readonly object s_logLock = new object();
        
        // 文件日志相关
        private static string s_logFilePath;
        private static System.IO.StreamWriter s_fileWriter;
        private static readonly Queue<LogData> s_logBuffer = new Queue<LogData>(BUFFER_QUEUE_CAPACITY);
        private static bool s_isFileLogInitialized = false;
        
        // 事件支持
        public static event System.Action<LogLevel, LogModule, string> OnLogMessage;
        
        #endregion
        
        #region 设置管理
        
        /// <summary>
        /// 全局日志级别
        /// </summary>
        public static LogLevel GlobalLogLevel
        {
            get 
            { 
                LoadSettingsIfNeeded();
                return s_globalLogLevel; 
            }
            set 
            { 
                if (s_globalLogLevel != value)
                {
                    s_globalLogLevel = value;
                    EditorPrefs.SetInt(PREFS_GLOBAL_LEVEL, (int)value);
                }
            }
        }

        /// <summary>
        /// 启用的模块
        /// </summary>
        public static LogModule EnabledModules
        {
            get 
            { 
                LoadSettingsIfNeeded();
                return s_enabledModules; 
            }
            set 
            { 
                if (s_enabledModules != value)
                {
                    s_enabledModules = value;
                    EditorPrefs.SetInt(PREFS_ENABLED_MODULES, (int)value);
                }
            }
        }
        
        /// <summary>
        /// 是否显示时间戳
        /// </summary>
        public static bool ShowTimestamp
        {
            get
            {
                LoadSettingsIfNeeded();
                return s_showTimestamp;
            }
            set
            {
                if (s_showTimestamp != value)
                {
                    s_showTimestamp = value;
                    EditorPrefs.SetBool(PREFS_SHOW_TIMESTAMP, value);
                }
            }
        }
        
        /// <summary>
        /// 是否显示模块名称
        /// </summary>
        public static bool ShowModule
        {
            get
            {
                LoadSettingsIfNeeded();
                return s_showModule;
            }
            set
            {
                if (s_showModule != value)
                {
                    s_showModule = value;
                    EditorPrefs.SetBool(PREFS_SHOW_MODULE, value);
                }
            }
        }
        
        /// <summary>
        /// 是否显示堆栈跟踪
        /// </summary>
        public static bool ShowStackTrace
        {
            get
            {
                LoadSettingsIfNeeded();
                return s_showStackTrace;
            }
            set
            {
                if (s_showStackTrace != value)
                {
                    s_showStackTrace = value;
                    EditorPrefs.SetBool(PREFS_SHOW_STACKTRACE, value);
                }
            }
        }
        
        /// <summary>
        /// 是否启用文件日志
        /// </summary>
        public static bool EnableFileLog
        {
            get
            {
                LoadSettingsIfNeeded();
                return s_enableFileLog;
            }
            set
            {
                if (s_enableFileLog != value)
                {
                    s_enableFileLog = value;
                    EditorPrefs.SetBool(PREFS_ENABLE_FILE_LOG, value);
                    
                    if (value)
                        InitializeFileLog();
                    else
                        CleanupFileLog();
                }
            }
        }
        
        /// <summary>
        /// 日志文件最大大小（MB）
        /// </summary>
        public static int MaxFileSizeMB
        {
            get
            {
                LoadSettingsIfNeeded();
                return s_maxFileSizeMB;
            }
            set
            {
                if (s_maxFileSizeMB != value && value > 0)
                {
                    s_maxFileSizeMB = value;
                    EditorPrefs.SetInt(PREFS_MAX_FILE_SIZE, value);
                }
            }
        }
        
        /// <summary>
        /// 加载设置（仅在需要时）
        /// </summary>
        private static void LoadSettingsIfNeeded()
        {
            if (s_settingsLoaded) return;

            s_globalLogLevel = (LogLevel)EditorPrefs.GetInt(PREFS_GLOBAL_LEVEL, (int)LogLevel.Info);
            s_enabledModules = (LogModule)EditorPrefs.GetInt(PREFS_ENABLED_MODULES, (int)LogModule.All);
            s_showTimestamp = EditorPrefs.GetBool(PREFS_SHOW_TIMESTAMP, true);
            s_showModule = EditorPrefs.GetBool(PREFS_SHOW_MODULE, true);
            s_showStackTrace = EditorPrefs.GetBool(PREFS_SHOW_STACKTRACE, false);
            s_enableFileLog = EditorPrefs.GetBool(PREFS_ENABLE_FILE_LOG, false);
            s_maxFileSizeMB = EditorPrefs.GetInt(PREFS_MAX_FILE_SIZE, DEFAULT_MAX_FILE_SIZE_MB);
            
            s_settingsLoaded = true;
            
            // 初始化模块显示名称缓存
            InitializeModuleDisplayNameCache();
            
            // 如果启用了文件日志，初始化文件日志
            if (s_enableFileLog)
                InitializeFileLog();
        }
        
        /// <summary>
        /// 初始化模块显示名称缓存
        /// </summary>
        private static void InitializeModuleDisplayNameCache()
        {
            if (s_moduleDisplayNameCache.Count > 0) return;
            
            s_moduleDisplayNameCache[LogModule.Core] = "核心系统";
            s_moduleDisplayNameCache[LogModule.Config] = "配置系统";
            s_moduleDisplayNameCache[LogModule.Pipeline] = "管线系统";
            s_moduleDisplayNameCache[LogModule.Database] = "数据库";
            s_moduleDisplayNameCache[LogModule.UI] = "用户界面";
            s_moduleDisplayNameCache[LogModule.AssetImport] = "资源导入";
            s_moduleDisplayNameCache[LogModule.Processor] = "资产处理器";
            s_moduleDisplayNameCache[LogModule.All] = "所有模块";
        }
        
        /// <summary>
        /// 重置设置缓存（在设置变更时调用）
        /// </summary>
        public static void ReloadSettings()
        {
            s_settingsLoaded = false;
            LoadSettingsIfNeeded();
        }

        /// <summary>
        /// 重置为默认设置
        /// </summary>
        public static void ResetToDefaults()
        {
            GlobalLogLevel = LogLevel.Info;
            EnabledModules = LogModule.All;
            ShowTimestamp = true;
            ShowModule = true;
            ShowStackTrace = false;
            EnableFileLog = false;
            MaxFileSizeMB = DEFAULT_MAX_FILE_SIZE_MB;
        }

        #endregion
        
        #region 文件日志管理
        
        /// <summary>
        /// 初始化文件日志
        /// </summary>
        private static void InitializeFileLog()
        {
            if (s_isFileLogInitialized) return;
            
            try
            {
                var logDirectory = System.IO.Path.Combine(Application.dataPath, "../Logs/AssetPipeline");
                if (!System.IO.Directory.Exists(logDirectory))
                    System.IO.Directory.CreateDirectory(logDirectory);
                
                s_logFilePath = System.IO.Path.Combine(logDirectory, $"AssetPipeline_{DateTime.Now:yyyyMMdd_HHmmss}.log");
                s_fileWriter = new System.IO.StreamWriter(s_logFilePath, append: true, encoding: System.Text.Encoding.UTF8);
                s_fileWriter.AutoFlush = true;
                
                s_isFileLogInitialized = true;
                
                // 写入文件日志头
                WriteToFile($"=== AssetPipeline Log Session Started at {DateTime.Now:yyyy-MM-dd HH:mm:ss} ===");
            }
            catch (Exception e)
            {
                // 文件日志初始化失败时回退到控制台
                UnityEngine.Debug.LogError($"[AssetPipeline] 文件日志初始化失败: {e.Message}");
                s_enableFileLog = false;
            }
        }
        
        /// <summary>
        /// 清理文件日志
        /// </summary>
        private static void CleanupFileLog()
        {
            if (s_fileWriter != null)
            {
                try
                {
                    WriteToFile($"=== AssetPipeline Log Session Ended at {DateTime.Now:yyyy-MM-dd HH:mm:ss} ===");
                    s_fileWriter.Close();
                    s_fileWriter.Dispose();
                }
                catch { /* 忽略清理错误 */ }
                finally
                {
                    s_fileWriter = null;
                    s_isFileLogInitialized = false;
                }
            }
        }
        
        /// <summary>
        /// 写入文件日志
        /// </summary>
        private static void WriteToFile(string message)
        {
            if (!s_enableFileLog || s_fileWriter == null) return;
            
            try
            {
                // 检查文件大小，如果超过限制则轮转
                if (s_fileWriter.BaseStream.Length > s_maxFileSizeMB * 1024 * 1024)
                {
                    RotateLogFile();
                }
                
                s_fileWriter.WriteLine(message);
            }
            catch (Exception e)
            {
                // 文件写入失败，禁用文件日志避免性能影响
                UnityEngine.Debug.LogError($"[AssetPipeline] 文件日志写入失败: {e.Message}");
                s_enableFileLog = false;
                CleanupFileLog();
            }
        }
        
        /// <summary>
        /// 轮转日志文件
        /// </summary>
        private static void RotateLogFile()
        {
            if (s_fileWriter == null) return;
            
            try
            {
                WriteToFile($"=== Log File Rotated at {DateTime.Now:yyyy-MM-dd HH:mm:ss} ===");
                s_fileWriter.Close();
                s_fileWriter.Dispose();
                
                // 创建新的日志文件
                var logDirectory = System.IO.Path.GetDirectoryName(s_logFilePath);
                s_logFilePath = System.IO.Path.Combine(logDirectory, $"AssetPipeline_{DateTime.Now:yyyyMMdd_HHmmss}.log");
                s_fileWriter = new System.IO.StreamWriter(s_logFilePath, append: false, encoding: System.Text.Encoding.UTF8);
                s_fileWriter.AutoFlush = true;
                
                WriteToFile($"=== AssetPipeline Log Session Started at {DateTime.Now:yyyy-MM-dd HH:mm:ss} ===");
            }
            catch (Exception e)
            {
                UnityEngine.Debug.LogError($"[AssetPipeline] 日志文件轮转失败: {e.Message}");
                s_enableFileLog = false;
                CleanupFileLog();
            }
        }
        
        #endregion
        
        #region 核心日志方法

        /// <summary>
        /// 检查是否应该输出日志
        /// </summary>
        [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
        private static bool ShouldLog(LogLevel level, LogModule module)
        {
            LoadSettingsIfNeeded();
            
            // 快速路径：检查全局级别
            if (level < s_globalLogLevel) return false;
            
            // 检查模块是否启用
            return (s_enabledModules & module) != 0;
        }

        /// <summary>
        /// 通用日志方法 - 性能优化版
        /// </summary>
        public static void Log(LogLevel level, LogModule module, string message)
        {
            if (!ShouldLog(level, module)) return;

            var logData = new LogData(level, module, message);
            
            // 如果需要堆栈跟踪，获取堆栈信息
            if (s_showStackTrace && (level == LogLevel.Warning || level == LogLevel.Error))
            {
                logData.StackTrace = System.Environment.StackTrace;
            }
            
            // 格式化并输出日志
            var formattedMessage = FormatMessageOptimized(logData);
            
            // 输出到Unity控制台
            OutputToUnityConsole(level, formattedMessage);
            
            // 输出到文件（如果启用）
            if (s_enableFileLog)
            {
                WriteToFile(formattedMessage);
            }
            
            // 触发事件
            OnLogMessage?.Invoke(level, module, message);
        }
        
        /// <summary>
        /// 结构化日志方法
        /// </summary>
        public static void LogStructured(LogLevel level, LogModule module, string message, Dictionary<string, object> context = null)
        {
            if (!ShouldLog(level, module)) return;

            var logData = new LogData(level, module, message)
            {
                Context = context
            };
            
            if (s_showStackTrace && (level == LogLevel.Warning || level == LogLevel.Error))
            {
                logData.StackTrace = System.Environment.StackTrace;
            }
            
            var formattedMessage = FormatStructuredMessage(logData);
            
            OutputToUnityConsole(level, formattedMessage);
            
            if (s_enableFileLog)
            {
                WriteToFile(formattedMessage);
            }
            
            // 触发事件
            OnLogMessage?.Invoke(level, module, message);
        }

        /// <summary>
        /// 格式化消息 - 性能优化版（减少字符串拼接）
        /// </summary>
        private static string FormatMessageOptimized(LogData logData)
        {
            lock (s_logLock)
            {
                s_sharedStringBuilder.Clear();
                
                if (s_showTimestamp)
                {
                    s_sharedStringBuilder.Append('[');
                    s_sharedStringBuilder.Append(logData.Timestamp.ToString("HH:mm:ss.fff"));
                    s_sharedStringBuilder.Append("] ");
                }
                
                if (s_showModule)
                {
                    s_sharedStringBuilder.Append("[AssetPipeline:");
                    s_sharedStringBuilder.Append(logData.Module);
                    s_sharedStringBuilder.Append("] ");
                }
                
                s_sharedStringBuilder.Append(logData.Message);
                
                if (!string.IsNullOrEmpty(logData.StackTrace))
                {
                    s_sharedStringBuilder.AppendLine();
                    s_sharedStringBuilder.Append(logData.StackTrace);
                }
                
                return s_sharedStringBuilder.ToString();
            }
        }
        
        /// <summary>
        /// 格式化结构化消息
        /// </summary>
        private static string FormatStructuredMessage(LogData logData)
        {
            lock (s_logLock)
            {
                s_sharedStringBuilder.Clear();
                
                if (s_showTimestamp)
                {
                    s_sharedStringBuilder.Append('[');
                    s_sharedStringBuilder.Append(logData.Timestamp.ToString("HH:mm:ss.fff"));
                    s_sharedStringBuilder.Append("] ");
                }
                
                if (s_showModule)
                {
                    s_sharedStringBuilder.Append("[AssetPipeline:");
                    s_sharedStringBuilder.Append(logData.Module);
                    s_sharedStringBuilder.Append("] ");
                }
                
                s_sharedStringBuilder.Append(logData.Message);
                
                // 添加上下文信息
                if (logData.Context != null && logData.Context.Count > 0)
                {
                    s_sharedStringBuilder.Append(" | Context: {");
                    bool first = true;
                    foreach (var kvp in logData.Context)
                    {
                        if (!first) s_sharedStringBuilder.Append(", ");
                        s_sharedStringBuilder.Append(kvp.Key);
                        s_sharedStringBuilder.Append(": ");
                        s_sharedStringBuilder.Append(kvp.Value?.ToString() ?? "null");
                        first = false;
                    }
                    s_sharedStringBuilder.Append('}');
                }
                
                if (!string.IsNullOrEmpty(logData.StackTrace))
                {
                    s_sharedStringBuilder.AppendLine();
                    s_sharedStringBuilder.Append(logData.StackTrace);
                }
                
                return s_sharedStringBuilder.ToString();
            }
        }
        
        /// <summary>
        /// 输出到Unity控制台
        /// </summary>
        [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
        private static void OutputToUnityConsole(LogLevel level, string formattedMessage)
        {
            // 根据级别选择Unity日志方法
            switch (level)
            {
                case LogLevel.Debug:
                case LogLevel.Info:
                    UnityEngine.Debug.Log(formattedMessage);
                    break;
                case LogLevel.Warning:
                    UnityEngine.Debug.LogWarning(formattedMessage);
                    break;
                case LogLevel.Error:
                    UnityEngine.Debug.LogError(formattedMessage);
                    break;
            }
        }

        /// <summary>
        /// 调试日志
        /// </summary>
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        public static void Debug(LogModule module, string message)
        {
            Log(LogLevel.Debug, module, message);
        }

        /// <summary>
        /// 信息日志
        /// </summary>
        public static void Info(LogModule module, string message)
        {
            Log(LogLevel.Info, module, message);
        }

        /// <summary>
        /// 警告日志
        /// </summary>
        public static void Warning(LogModule module, string message)
        {
            Log(LogLevel.Warning, module, message);
        }

        /// <summary>
        /// 错误日志
        /// </summary>
        public static void Error(LogModule module, string message)
        {
            Log(LogLevel.Error, module, message);
        }
        
        /// <summary>
        /// 结构化调试日志
        /// </summary>
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        public static void DebugStructured(LogModule module, string message, Dictionary<string, object> context)
        {
            LogStructured(LogLevel.Debug, module, message, context);
        }
        
        /// <summary>
        /// 结构化信息日志
        /// </summary>
        public static void InfoStructured(LogModule module, string message, Dictionary<string, object> context)
        {
            LogStructured(LogLevel.Info, module, message, context);
        }
        
        /// <summary>
        /// 结构化警告日志
        /// </summary>
        public static void WarningStructured(LogModule module, string message, Dictionary<string, object> context)
        {
            LogStructured(LogLevel.Warning, module, message, context);
        }
        
        /// <summary>
        /// 结构化错误日志
        /// </summary>
        public static void ErrorStructured(LogModule module, string message, Dictionary<string, object> context)
        {
            LogStructured(LogLevel.Error, module, message, context);
        }

        #endregion
        
        #region 便捷方法
        
        /// <summary>
        /// 异常日志 - 性能优化版
        /// </summary>
        public static void LogException(LogModule module, Exception exception, string context = null)
        {
            if (!ShouldLog(LogLevel.Error, module)) return;
            
            var contextData = new Dictionary<string, object>
            {
                ["ExceptionType"] = exception.GetType().Name,
                ["Source"] = exception.Source ?? "Unknown"
            };
            
            if (exception.InnerException != null)
            {
                contextData["InnerExceptionType"] = exception.InnerException.GetType().Name;
                contextData["InnerExceptionMessage"] = exception.InnerException.Message;
            }
            
            var message = !string.IsNullOrEmpty(context) 
                ? $"{context}: {exception.Message}" 
                : exception.Message;
            
            LogStructured(LogLevel.Error, module, message, contextData);
        }
        
        /// <summary>
        /// 性能日志 - 优化版
        /// </summary>
        public static IDisposable LogPerformance(LogModule module, string operationName)
        {
            return new PerformanceLogger(module, operationName);
        }
        
        /// <summary>
        /// 批量日志输出（用于减少频繁的单条日志输出）
        /// </summary>
        public static void LogBatch(IEnumerable<LogData> logDataCollection)
        {
            if (logDataCollection == null) return;
            
            foreach (var logData in logDataCollection)
            {
                if (ShouldLog(logData.Level, logData.Module))
                {
                    var formattedMessage = FormatMessageOptimized(logData);
                    OutputToUnityConsole(logData.Level, formattedMessage);
                    
                    if (s_enableFileLog)
                    {
                        WriteToFile(formattedMessage);
                    }
                }
            }
        }

        #endregion
        
        #region 工具方法
        
        /// <summary>
        /// 获取模块显示名称 - 缓存优化版
        /// </summary>
        public static string GetModuleDisplayName(LogModule module)
        {
            LoadSettingsIfNeeded();
            
            if (s_moduleDisplayNameCache.TryGetValue(module, out var displayName))
                return displayName;
                
            // 如果缓存中没有，生成显示名称并缓存
            displayName = module.ToString();
            s_moduleDisplayNameCache[module] = displayName;
            return displayName;
        }
        
        /// <summary>
        /// 获取当前日志文件路径
        /// </summary>
        public static string GetCurrentLogFilePath()
        {
            return s_logFilePath;
        }
        
        /// <summary>
        /// 获取日志统计信息
        /// </summary>
        public static string GetLogStatistics()
        {
            var stats = new Dictionary<string, object>
            {
                ["GlobalLogLevel"] = s_globalLogLevel.ToString(),
                ["EnabledModules"] = s_enabledModules.ToString(),
                ["FileLogEnabled"] = s_enableFileLog,
                ["CurrentLogFile"] = s_logFilePath ?? "None",
                ["SettingsLoaded"] = s_settingsLoaded
            };
            
            lock (s_logLock)
            {
                s_sharedStringBuilder.Clear();
                s_sharedStringBuilder.AppendLine("AssetPipeline Logger Statistics:");
                
                foreach (var kvp in stats)
                {
                    s_sharedStringBuilder.AppendLine($"  {kvp.Key}: {kvp.Value}");
                }
                
                return s_sharedStringBuilder.ToString();
            }
        }
        
        #endregion
        
        #region 静态构造和清理
        
        /// <summary>
        /// 静态构造函数
        /// </summary>
        static Logger()
        {
            // 注册Unity编辑器清理事件
            EditorApplication.quitting += CleanupFileLog;
        }
        
        /// <summary>
        /// 手动清理资源
        /// </summary>
        public static void Cleanup()
        {
            CleanupFileLog();
            s_moduleDisplayNameCache.Clear();
            s_settingsLoaded = false;
        }
        
        #endregion
    }
    
    /// <summary>
    /// 性能日志器 - 优化版
    /// </summary>
    internal class PerformanceLogger : IDisposable
    {
        private readonly LogModule module;
        private readonly string operationName;
        private readonly System.Diagnostics.Stopwatch stopwatch;
        private readonly Dictionary<string, object> context;
        
        public PerformanceLogger(LogModule module, string operationName)
        {
            this.module = module;
            this.operationName = operationName;
            this.stopwatch = System.Diagnostics.Stopwatch.StartNew();
            this.context = new Dictionary<string, object>
            {
                ["Operation"] = operationName,
                ["StartTime"] = DateTime.Now.ToString("HH:mm:ss.fff")
            };
        }
        
        public void Dispose()
        {
            stopwatch.Stop();
            
            context["Duration"] = $"{stopwatch.ElapsedMilliseconds}ms";
            context["EndTime"] = DateTime.Now.ToString("HH:mm:ss.fff");
            
            Logger.DebugStructured(module, 
                $"{operationName} 执行完成", context);
        }
    }
} 