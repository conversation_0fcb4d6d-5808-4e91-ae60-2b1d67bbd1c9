using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;
using L10.Editor.AssetPipeline.Sqlite;
using UnityEngine.Windows;

namespace L10.Editor.AssetPipeline
{
    public class AssetReferenceScanner : AssetPostprocessor
    {
        private static string dbPath = "Library/AssetReferenceCache.sqlite";
        private static SQLiteConnection dbConnection;
        
        public static bool isScanning { get; private set; } = false;

        private static float scanProgress = 0f;

        [InitializeOnLoadMethod]
        private static void Initialize()
        {
            InitializeDatabase();
        }

        private static void InitializeDatabase()
        {
            dbConnection = new SQLiteConnection(dbPath);
            
            dbConnection.Execute(@"
                CREATE TABLE IF NOT EXISTS ForwardDependency (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    AssetGuid TEXT,
                    DependencyGuid TEXT
                )");
            
            dbConnection.Execute("CREATE INDEX IF NOT EXISTS idx_asset_guid ON ForwardDependency(AssetGuid)");
            dbConnection.Execute("CREATE INDEX IF NOT EXISTS idx_dependency_guid ON ForwardDependency(DependencyGuid)");
            
            long count = dbConnection.ExecuteScalar<long>("SELECT COUNT(DISTINCT AssetGuid) FROM ForwardDependency");
            scanProgress = (float)count / AssetDatabase.GetAllAssetPaths().Where(x=>IsValidAssetPath(x)).ToArray().Length;
        }

        private static bool IsValidAssetPath(string assetPath) => !string.IsNullOrEmpty(assetPath) && assetPath.StartsWith("Assets/");
        
        private static bool IsValidAssetGuid(string assetGuid) => !string.IsNullOrEmpty(assetGuid) && assetGuid != "0000000000000000e000000000000000";
        
        private static bool DoesAssetExistsForGUID(string assetGuid) => File.Exists(AssetDatabase.GUIDToAssetPath(assetGuid));
        
        private static void ProcessAsset(string assetPath)
        {
            if (dbConnection == null) return;
            string assetGuid = AssetDatabase.AssetPathToGUID(assetPath);
            string[] depPaths = AssetDatabase.GetDependencies(assetPath, false);
            
            dbConnection.RunInTransaction(() =>
            {
                dbConnection.Execute("DELETE FROM ForwardDependency WHERE AssetGuid = ?", assetGuid);

                var dependencies = depPaths
                    .Where(depPath => IsValidAssetPath(depPath))
                    .Select(depPath => AssetDatabase.AssetPathToGUID(depPath))
                    .Where(depGuid => IsValidAssetGuid(depGuid) && assetGuid != depGuid);

                if (!dependencies.Any())
                {
                    dbConnection.Execute(
                        "INSERT INTO ForwardDependency (AssetGuid, DependencyGuid) VALUES (?, ?)", 
                        assetGuid, null);
                }
                else
                {
                    foreach (var depGuid in dependencies)
                    {
                        dbConnection.Execute(
                            "INSERT INTO ForwardDependency (AssetGuid, DependencyGuid) VALUES (?, ?)", 
                            assetGuid, depGuid);
                    }
                }
            });
        }

        private static void RemoveAsset(string assetPath)
        {
            if (dbConnection == null) return;
            string assetGuid = AssetDatabase.AssetPathToGUID(assetPath);
            dbConnection.Execute("DELETE FROM ForwardDependency WHERE AssetGuid = ?", assetGuid);
        }

        private static void OnPostprocessAllAssets(string[] importedAssets, string[] deletedAssets,
            string[] movedAssets, string[] movedFromAssetPaths)
        {
            ToggleScanning(false);

            if (dbConnection == null) return;

            dbConnection.RunInTransaction(() =>
            {
                var assetsToProcess = new List<(string guid, string path, bool isDelete)>();

                foreach (string assetPath in importedAssets)
                {
                    string guid = AssetDatabase.AssetPathToGUID(assetPath);
                    assetsToProcess.Add((guid, assetPath, false));
                }
                
                foreach (string assetPath in deletedAssets)
                {
                    string guid = AssetDatabase.AssetPathToGUID(assetPath);
                    assetsToProcess.Add((guid, assetPath, true));
                }
                
                for (int i = 0; i < movedAssets.Length; i++)
                {
                    string oldGuid = AssetDatabase.AssetPathToGUID(movedFromAssetPaths[i]);
                    string newGuid = AssetDatabase.AssetPathToGUID(movedAssets[i]);

                    if (oldGuid != newGuid)
                    {
                        assetsToProcess.Add((oldGuid, movedFromAssetPaths[i], true));
                        assetsToProcess.Add((newGuid, movedAssets[i], false));
                    }
                }
                
                if (assetsToProcess.Any())
                {
                    var guidsToDelete = assetsToProcess.Select(x => x.guid).ToList();
                    // 将大量操作分批处理，防止超出 SQLite 变量数量的限制(999)
                    const int batchSize = 500;
                    for (int i = 0; i < guidsToDelete.Count; i += batchSize)
                    {
                        var batch = guidsToDelete.Skip(i).Take(batchSize).ToList();
                        string placeholders = string.Join(",", batch.Select(_ => "?"));
                        dbConnection.Execute(
                            $"DELETE FROM ForwardDependency WHERE AssetGuid IN ({placeholders})",
                            batch.ToArray());
                    }
                }
              
                foreach (var (guid, path, _) in assetsToProcess.Where(x => !x.isDelete))
                {
                    string[] depPaths = AssetDatabase.GetDependencies(path, false);

                    var dependencies = depPaths
                        .Where(depPath => IsValidAssetPath(depPath))
                        .Select(depPath => AssetDatabase.AssetPathToGUID(depPath))
                        .Where(depGuid => IsValidAssetGuid(depGuid) && guid != depGuid);

                    if (!dependencies.Any())
                    {
                        dbConnection.Execute(
                            "INSERT INTO ForwardDependency (AssetGuid, DependencyGuid) VALUES (?, ?)",
                            guid, null);
                    }
                    else
                    {
                        foreach (var depGuid in dependencies)
                        {
                            dbConnection.Execute(
                                "INSERT INTO ForwardDependency (AssetGuid, DependencyGuid) VALUES (?, ?)",
                                guid, depGuid);
                        }
                    }
                }
            });

            long count = dbConnection.ExecuteScalar<long>("SELECT COUNT(DISTINCT AssetGuid) FROM ForwardDependency");
            scanProgress = (float)count / AssetDatabase.GetAllAssetPaths().Where(x=>IsValidAssetPath(x)).ToArray().Length;
        }

        private static void Scan()
        {
            if (dbConnection == null)
            {
                EditorUtility.DisplayDialog("错误", "未初始化数据库连接！", "确定");
                return;
            }
            var allAssetPaths = AssetDatabase.GetAllAssetPaths().Where(x=>IsValidAssetPath(x)).ToArray();
            
            var cachedAssets = new HashSet<string>(
                dbConnection.QueryStrings("SELECT DISTINCT AssetGuid FROM ForwardDependency WHERE AssetGuid IS NOT NULL")
            );

            for (int i = 0, j = 0; i < allAssetPaths.Length; i++)
            {
                var assetPath = allAssetPaths[i];
                string assetGuid = AssetDatabase.AssetPathToGUID(assetPath);
                if (cachedAssets.Contains(assetGuid)) continue;
                
                ProcessAsset(assetPath);

                if (j++ % 100 == 0)
                {
                    long count = dbConnection.ExecuteScalar<long>("SELECT COUNT(DISTINCT AssetGuid) FROM ForwardDependency");
                    scanProgress = (float)count /  allAssetPaths.Length;
                }
                
                if (EditorUtility.DisplayCancelableProgressBar("扫描资产", $"扫描进度: {GetScanProgress():P2}", scanProgress))
                {
                    ToggleScanning(false);
                    EditorUtility.ClearProgressBar();
                    return;
                }
            }
            
            ToggleScanning(false);
            EditorUtility.ClearProgressBar();
        }

        public static HashSet<string> FindReverseReferences(string assetGuid)
        {
            HashSet<string> result = new HashSet<string>();
            if (dbConnection == null || !IsValidAssetGuid(assetGuid) || !DoesAssetExistsForGUID(assetGuid)) return result;

            Queue<string> queue = new Queue<string>();
            queue.Enqueue(assetGuid);

            while (queue.Count > 0)
            {
                string currentGuid = queue.Dequeue();
                var references = dbConnection.QueryStrings(
                    "SELECT AssetGuid FROM ForwardDependency WHERE DependencyGuid = ? AND AssetGuid IS NOT NULL", 
                    currentGuid);
                
                foreach (var referenceGuid in references)
                {
                    if (IsValidAssetGuid(referenceGuid) && 
                        DoesAssetExistsForGUID(referenceGuid) && 
                        referenceGuid != assetGuid && 
                        result.Add(referenceGuid))
                    {
                        queue.Enqueue(referenceGuid);
                    }
                }
            }

            return result;
        }

        public static void ToggleScanning(bool enable)
        {
            isScanning = enable;
            if (enable)
                Scan();
        }

        public static float GetScanProgress()
        {
            return scanProgress;
        }

        public static void ClearCache()
        {
             if (dbConnection == null)
             {
                 EditorUtility.DisplayDialog("错误", "未初始化数据库连接！", "确定");
                 return;
             }

            dbConnection.Execute("DELETE FROM ForwardDependency");
            var path = Application.dataPath.Remove(Application.dataPath.Length - 6) + dbPath;
            if (File.Exists(path)) File.Delete(path);
            scanProgress = 0f;
            Debug.Log("缓存已清除。");
        }
        // 加一个静态方法方便快速收集资源的引用情况
		public static Dictionary<Object, List<string>> FindReferencesByAsset(Object asset)
        {
            HashSet<Object> assetsToFind = new HashSet<Object>();
            Dictionary<Object, List<string>> foundReferences = new Dictionary<Object, List<string>>();
            string path = AssetDatabase.GetAssetPath(asset);
            if (string.IsNullOrEmpty(path))
                return null;

            if (System.IO.Directory.Exists(path))
            {
                // 如果是文件夹，递归添加所有子资源
                string[] subAssets = System.IO.Directory.GetFiles(path, "*", System.IO.SearchOption.AllDirectories).ToList()
                    .Where(file => !file.EndsWith(".meta"))
                    .ToArray();

                foreach (string subAssetPath in subAssets)
                {
                    Object subAsset = AssetDatabase.LoadAssetAtPath<Object>(subAssetPath);
                    if (subAsset != null)
                    {
                        assetsToFind.Add(subAsset);
                    }
                }
            }
            else
            {
                // 如果是单个资源，直接添加
                assetsToFind.Add(asset);
            }

            foreach (Object assetObj in assetsToFind)
            {
                string assetPath = AssetDatabase.GetAssetPath(assetObj);
                string assetGuid = AssetDatabase.AssetPathToGUID(assetPath);
                HashSet<string> references = AssetReferenceScanner.FindReverseReferences(assetGuid);
                foundReferences[assetObj] = new List<string>(references);
            }
            return foundReferences;
        } 
        
    }
}
