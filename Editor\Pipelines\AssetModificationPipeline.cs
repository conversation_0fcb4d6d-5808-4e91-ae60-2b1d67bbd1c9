using System;
using System.Collections.Generic;
using System.Linq;
using AssetPipeline.Config;
using UnityEngine;
using UnityEditor;
using AssetPipeline.Core;
using AssetPipeline.Processors;
using AssetPipeline.UI.Windows;
using Logger = AssetPipeline.Core.Logger;

namespace AssetPipeline.Pipelines
{
    /// <summary>
    /// 资源修改检查管线
    /// </summary>
    public class AssetModificationPipeline : AssetModificationProcessor
    {
        private const string PIPELINE_NAME = "modification";
        
        /// <summary>
        /// 资源即将保存时的检查
        /// </summary>
        static string[] OnWillSaveAssets(string[] paths)
        {
            if (!AssetPipelineConfig.IsPipelineEnabled(PIPELINE_NAME) || paths == null || paths.Length == 0)
            {
                return paths;
            }

            var context = new ModificationContext(paths);
            var operationName = "保存检查";
            
            Logger.Debug(LogModule.Pipeline, $"[{PIPELINE_NAME}] {operationName} - 开始检查 {paths.Length} 个资源。");

            var resultsByPath = PipelineHelper.ExecuteProcessors<IModificationProcessor>(
                paths,
                (processor, assetPath) => processor.OnWillSaveAsset(assetPath, context)
            );
            
            // 遍历结果，更新上下文并标记需要阻止的资产
            foreach (var a in resultsByPath)
            {
                var path = a.Key;
                var newResults = a.Value;
                
                context.Results.AddRange(newResults);

                if (newResults.Any(r => r.resultType == CheckResultType.Error))
                {
                    context.BlockAsset(path, "保存前检查发现错误，操作被阻止。");
                }
            }

            if (context.HasBlockedOperations)
            {
                Logger.Warning(LogModule.Pipeline, $"[{PIPELINE_NAME}] {operationName} - {context.BlockedAssets.Count} 个资源不合规，已阻止保存。");
                CheckResultWindow.ShowResults("资源保存检查报告", context.Results.Results.ToList());
                
                var allowedPaths = paths.Where(p => !context.IsBlocked(p)).ToArray();
                Logger.Debug(LogModule.Pipeline, $"[{PIPELINE_NAME}] {operationName} - 允许保存 {allowedPaths.Length} 个资源。");
                
                context.Complete();
                return allowedPaths;
            }
            
            Logger.Debug(LogModule.Pipeline, $"[{PIPELINE_NAME}] {operationName} - 所有资源检查通过。");
            context.Complete();
            return paths;
        }

        /// <summary>
        /// 资源即将删除时的检查
        /// </summary>
        static AssetDeleteResult OnWillDeleteAsset(string assetPath, RemoveAssetOptions options)
        {
            if (!AssetPipelineConfig.IsPipelineEnabled(PIPELINE_NAME))
            {
                return AssetDeleteResult.DidNotDelete;
            }

            var context = new ModificationContext(new[] { assetPath });
            var operationName = "删除检查";
            
            var resultsByPath = PipelineHelper.ExecuteProcessors<IModificationProcessor>(
                new[] { assetPath },
                (processor, path) => processor.OnWillDeleteAsset(assetPath, options, context)
            );
            
            context.Results.AddRange(resultsByPath.SelectMany(kvp => kvp.Value));
            
            return ProcessModificationResult(context, "删除", AssetDeleteResult.DidNotDelete, AssetDeleteResult.DidDelete, AssetDeleteResult.FailedDelete);
        }

        /// <summary>
        /// 资源即将移动时的检查
        /// </summary>
        static AssetMoveResult OnWillMoveAsset(string sourcePath, string destinationPath)
        {
            if (!AssetPipelineConfig.IsPipelineEnabled(PIPELINE_NAME))
            {
                return AssetMoveResult.DidNotMove;
            }

            var context = new ModificationContext(new[] { sourcePath, destinationPath });
            var executedProcessors = new HashSet<IModificationProcessor>();
            
            var pathsToCheck = new[] { sourcePath, destinationPath }.Distinct();
            
            var resultsByPath = PipelineHelper.ExecuteProcessors<IModificationProcessor>(
                pathsToCheck,
                (processor, path) =>
                {
                    if (executedProcessors.Add(processor))
                    {
                        return processor.OnWillMoveAsset(sourcePath, destinationPath, context);
                    }
                    return null;
                }
            );
            
            context.Results.AddRange(resultsByPath.SelectMany(kvp => kvp.Value));

            return ProcessModificationResult(context, "移动", AssetMoveResult.DidNotMove, AssetMoveResult.DidMove, AssetMoveResult.FailedMove);
        }

        private static T ProcessModificationResult<T>(ModificationContext context, string operationName, T didNotPerform, T didPerform, T failed)
        {
            // 是否已执行操作？
            if (context.Results.Any(r => r.Action == ProcessorAction.OperationPerformed))
            {
                Logger.Info(LogModule.Pipeline, $"[{PIPELINE_NAME}] {operationName}检查 - 处理器已自行执行操作。");
                context.Complete();
                return didPerform;
            }

            // 是否有阻断性错误？
            if (context.Results.HasErrors)
            {
                var assetPath = context.AssetPaths.FirstOrDefault() ?? "Unknown";
                context.BlockAsset(assetPath, $"{operationName}前检查发现错误，操作被阻止。");
                Logger.Warning(LogModule.Pipeline, $"[{PIPELINE_NAME}] {operationName}检查 - 阻止操作: {assetPath}。");
                CheckResultWindow.ShowResults($"资源{operationName}检查报告", context.Results.Results.ToList());
                
                context.Complete();
                return failed;
            }
            
            context.Complete();
            return didNotPerform;
        }

        /// <summary>
        /// 资源即将创建时的检查
        /// </summary>
        static void OnWillCreateAsset(string assetName)
        {
            if (!AssetPipelineConfig.IsPipelineEnabled(PIPELINE_NAME))
            {
                return;
            }
            
            var context = new ModificationContext(new[] { assetName });
            
            var resultsByPath = PipelineHelper.ExecuteProcessors<IModificationProcessor>(
                new[] { assetName },
                (processor, path) => processor.OnWillCreateAsset(assetName, context)
            );
            var results = resultsByPath.TryGetValue(assetName, out var res) ? res : new List<CheckResult>();

            if (results.Any())
            {
                context.Results.AddRange(results);
                Logger.Info(LogModule.Pipeline, $"[{PIPELINE_NAME}] 创建检查 - 发现问题: {assetName}");
                CheckResultWindow.ShowResults("新资源检查报告", context.Results.Results.ToList());
            }
            
            context.Complete();
        }

        /// <summary>
        /// 检查资源是否可编辑
        /// </summary>
        static bool IsOpenForEdit(string assetPath, out string message)
        {
            message = "";
            if (!AssetPipelineConfig.IsPipelineEnabled(PIPELINE_NAME))
            {
                return true;
            }
            
            var context = new ModificationContext(new[] { assetPath });
            
            var resultsByPath = PipelineHelper.ExecuteProcessors<IModificationProcessor>(
                new[] { assetPath },
                (processor, path) => processor.IsOpenForEdit(assetPath, context)
            );
            var results = resultsByPath.TryGetValue(assetPath, out var res) ? res : new List<CheckResult>();

            var errors = results.Where(r => r.resultType == CheckResultType.Error).ToList();
            if (errors.Any())
            {
                message = string.Join("\n", errors.Select(e => e.message).ToArray());
                Logger.Warning(LogModule.Pipeline, $"[{PIPELINE_NAME}] 编辑检查 - {assetPath} 不可编辑: {message}");
                return false;
            }

            return true;
        }
    }
} 