﻿using System;
using System.Collections.Generic;
using System.Linq;
using AssetPipeline.Config;
using AssetPipeline.Core;
using AssetPipeline.UI.Windows;
using AssetPipeline.UI.Components;
using UnityEditor;
using UnityEditor.Callbacks;
using UnityEditor.IMGUI.Controls;
using UnityEngine;

namespace AssetPipeline.UI
{
    /// <summary>
    /// AssetTree编辑器
    /// </summary>
    internal class AssetTreeWindow : EditorWindow
    {
        [NonSerialized] bool m_Initialized;
        [SerializeField] TreeViewState m_TreeViewState;
        [SerializeField] MultiColumnHeaderState m_MultiColumnHeaderState;
        [SerializeField] Vector2 m_ProfileScrollPosition;
        
        SearchField m_SearchField;
        AssetTreeView m_TreeView;
        AssetTree m_AssetTree;
        
        // 选中的Profile信息
        private AssetTreeElement selectedElement;
        private bool showProfileConfig = true;
        private bool showPathPreview = true;

        // 路径测试
        private string testPath = "";

        public AssetTreeView treeView
        {
            get { return m_TreeView; }
        }

        static AssetTreeWindow s_Window;

        public static AssetTreeWindow Window
        {
            get
            {
                if (s_Window == null)
                {
                    s_Window = GetWindow<AssetTreeWindow>(false, null, false);
                }
                return s_Window;
            }
        }

        public static void ShowWindow()
        {
            if (!s_Window)
            {
                s_Window = GetWindow<AssetTreeWindow>();
                s_Window.titleContent = new GUIContent("🌳 AssetTree Editor");
                s_Window.minSize = new Vector2(800, 500);
                s_Window.Focus();
                s_Window.Repaint();
                s_Window.Show();
            }
            else
            {
                s_Window.Focus();
            }
        }

        [OnOpenAsset]
        public static bool OnOpenAsset(int instanceID, int line)
        {
            var myTreeAsset = EditorUtility.InstanceIDToObject(instanceID) as AssetTree;
            if (myTreeAsset != null)
            {
                ShowWindow();
                Window.SetTreeAsset(myTreeAsset);
                return true;
            }
            return false;
        }

        void SetTreeAsset(AssetTree myTreeAsset)
        {
            m_AssetTree = myTreeAsset;
            m_Initialized = false;
        }

        // 布局常量
        const float TREE_MIN_WIDTH = 300f;
        const float PANEL_MIN_WIDTH = 250f;
        const float SPLITTER_WIDTH = 4f;
        
        float m_SplitterPercent = 0.6f;
        bool m_IsDraggingSplitter;
        
        Rect treeViewRect
        {
            get { return new Rect(0, 40, position.width * m_SplitterPercent, position.height - 40); }
        }

        Rect profilePanelRect
        {
            get { return new Rect(position.width * m_SplitterPercent + SPLITTER_WIDTH, 40, 
                position.width * (1 - m_SplitterPercent) - SPLITTER_WIDTH, position.height - 40); }
        }

        Rect splitterRect
        {
            get { return new Rect(position.width * m_SplitterPercent, 40, SPLITTER_WIDTH, position.height - 40); }
        }

        void InitIfNeeded()
        {
            if (m_Initialized)
                return;

            // 初始化TreeView
            if (m_TreeViewState == null)
                m_TreeViewState = new TreeViewState();

            var firstInit = m_MultiColumnHeaderState == null;
            var headerState = AssetTreeView.CreateDefaultMultiColumnHeaderState(treeViewRect.width);
            if (MultiColumnHeaderState.CanOverwriteSerializedFields(m_MultiColumnHeaderState, headerState))
            {
                MultiColumnHeaderState.OverwriteSerializedFields(m_MultiColumnHeaderState, headerState);
            }

            m_MultiColumnHeaderState = headerState;
            var multiColumnHeader = new MultiColumnHeader(headerState);
            if (firstInit)
                multiColumnHeader.ResizeToFit();

            // 初始化AssetTree数据
            if (m_AssetTree == null)
                m_AssetTree = AssetPipelineConfig.MainAssetTree;

            var treeModel = new TreeModel<AssetTreeElement>(GetData());

            // 创建TreeView
            m_TreeView = new AssetTreeView(m_TreeViewState, multiColumnHeader, treeModel);
            m_TreeView.ExpandAll();
            m_TreeView.treeChanged += () => EditorUtility.SetDirty(m_AssetTree);
            m_TreeView.selectionChanged += OnTreeSelectionChanged;

            // 初始化搜索
            m_SearchField = new SearchField();
            m_SearchField.downOrUpArrowKeyPressed += m_TreeView.SetFocusAndEnsureSelectedItem;

            m_Initialized = true;
        }

        IList<AssetTreeElement> GetData()
        {
            if (m_AssetTree == null)
                m_AssetTree = AssetPipelineConfig.MainAssetTree;

            return m_AssetTree.TreeElements;
        }

        void OnTreeSelectionChanged(IList<int> selectedIds)
        {
            selectedElement = null;
            
            if (selectedIds != null && selectedIds.Count > 0)
            {
                var selectedItem = m_TreeView.FindItem(selectedIds[0], m_TreeView.rootItem) as TreeViewItem<AssetTreeElement>;
                selectedElement = selectedItem?.data;
            }
            
            Repaint();
        }

        void OnSelectionChange()
        {
            if (!m_Initialized)
                return;

            var myTreeAsset = Selection.activeObject as AssetTree;
            if (myTreeAsset != null && myTreeAsset != m_AssetTree)
            {
                m_AssetTree = myTreeAsset;
                m_TreeView.treeModel.SetData(GetData());
                m_TreeView.Reload();
            }
        }

        void OnEnable()
        {
            m_SplitterPercent = EditorPrefs.GetFloat("AssetTreeWindow_SplitterPercent", 0.6f);
        }

        void OnDisable()
        {
            EditorPrefs.SetFloat("AssetTreeWindow_SplitterPercent", m_SplitterPercent);
        }

        void OnGUI()
        {
            InitIfNeeded();

            DrawToolbar();
            DrawMainContent();
            HandleSplitterDragging();
        }

        void DrawToolbar()
        {
            EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);

            // 树操作按钮
            if (GUILayout.Button("新建节点", EditorStyles.toolbarButton, GUILayout.Width(100)))
            {
                CreateNewNode();
            }

            if (GUILayout.Button("删除节点", EditorStyles.toolbarButton, GUILayout.Width(100)))
            {
                DeleteSelectedNode();
            }

            GUILayout.Space(20);

            // 显示选项
            showProfileConfig = GUILayout.Toggle(showProfileConfig, "显示配置", EditorStyles.toolbarButton, GUILayout.Width(80));
            showPathPreview = GUILayout.Toggle(showPathPreview, "显示预览", EditorStyles.toolbarButton, GUILayout.Width(80));

            GUILayout.FlexibleSpace();

            // 搜索框
            var searchRect = GUILayoutUtility.GetRect(200, EditorGUIUtility.singleLineHeight);
            searchRect.y += 2;
            var searchString = m_SearchField.OnGUI(searchRect, m_TreeView.searchString ?? "");
            if (searchString != m_TreeView.searchString)
            {
                m_TreeView.searchString = searchString;
                m_TreeView.Reload();
            }

            // 配置按钮
            if (GUILayout.Button("⚙️ 设置", EditorStyles.toolbarButton, GUILayout.Width(60)))
            {
                Selection.activeObject = AssetPipelineConfig.Instance;
                EditorGUIUtility.PingObject(Selection.activeObject);
            }

            EditorGUILayout.EndHorizontal();
        }

        void DrawMainContent()
        {
            EditorGUILayout.BeginHorizontal();
            
            // 左侧：树形视图
            EditorGUILayout.BeginVertical(GUILayout.Width(treeViewRect.width));
            DrawTreeSection();
            EditorGUILayout.EndVertical();

            // 分割线
            DrawSplitter();

            // 右侧：配置面板
            EditorGUILayout.BeginVertical();
            DrawProfilePanel();
            EditorGUILayout.EndVertical();

            EditorGUILayout.EndHorizontal();
        }

        void DrawTreeSection()
        {
            EditorGUILayout.LabelField("AssetProfile 层次结构", EditorStyles.boldLabel);
            
            var treeRect = GUILayoutUtility.GetRect(0, treeViewRect.height - 25, GUILayout.ExpandWidth(true));
            m_TreeView.OnGUI(treeRect);
        }

        void DrawSplitter()
        {
            var splitterStyle = new GUIStyle();
            splitterStyle.normal.background = EditorGUIUtility.whiteTexture;
            
            var oldColor = GUI.color;
            GUI.color = new Color(0.5f, 0.5f, 0.5f, 1);
            
            GUILayout.Box("", splitterStyle, GUILayout.Width(SPLITTER_WIDTH), GUILayout.ExpandHeight(true));
            
            GUI.color = oldColor;
            
            // 鼠标指针
            var splitterDragRect = GUILayoutUtility.GetLastRect();
            EditorGUIUtility.AddCursorRect(splitterDragRect, MouseCursor.ResizeHorizontal);
        }

        void DrawProfilePanel()
        {
            EditorGUILayout.LabelField("Profile 配置面板", EditorStyles.boldLabel);

            if (selectedElement == null)
            {
                EditorGUILayout.HelpBox("选择一个节点以查看和编辑其配置", MessageType.Info);
                return;
            }

            m_ProfileScrollPosition = EditorGUILayout.BeginScrollView(m_ProfileScrollPosition);

            // 基本信息
            DrawBasicInfo();
            
            if (showProfileConfig && selectedElement.Profile != null)
            {
                EditorGUILayout.Space();
                DrawProfileConfiguration();
            }

            if (showPathPreview)
            {
                EditorGUILayout.Space();
                DrawPathPreview();
            }

            EditorGUILayout.EndScrollView();
        }

        void DrawBasicInfo()
        {
            EditorGUILayout.LabelField("基本信息", EditorStyles.boldLabel);
            
            EditorGUI.BeginChangeCheck();
            
            var newName = EditorGUILayout.TextField("节点名称", selectedElement.name);
            if (EditorGUI.EndChangeCheck())
            {
                selectedElement.name = newName;
                m_TreeView.Reload();
                EditorUtility.SetDirty(m_AssetTree);
            }

            // 移除深度和ID显示，这些是内部技术信息
            
            if (selectedElement.Profile != null)
            {
                // 紧凑显示Profile状态
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Profile状态:", GUILayout.Width(80));
                
                var statusText = selectedElement.Profile.Enabled ? "启用" : "禁用";
                var statusColor = selectedElement.Profile.Enabled ? Color.green : Color.gray;
                
                var oldColor = GUI.color;
                GUI.color = statusColor;
                EditorGUILayout.LabelField(statusText, EditorStyles.miniLabel, GUILayout.Width(40));
                GUI.color = oldColor;
                
                EditorGUILayout.LabelField($"优先级: {selectedElement.Profile.Priority}", EditorStyles.miniLabel);
                EditorGUILayout.EndHorizontal();
            }
            else
            {
                EditorGUILayout.HelpBox("此节点没有关联的AssetProfile", MessageType.Warning);
                if (GUILayout.Button("创建AssetProfile"))
                {
                    CreateProfileForNode();
                }
            }
        }

        void DrawProfileConfiguration()
        {
            EditorGUILayout.LabelField("Profile 配置概览", EditorStyles.boldLabel);
            
            if (selectedElement.Profile == null)
                return;

            var profile = selectedElement.Profile;
            
            // 紧凑的配置概览
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            
            // 路径配置
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("路径前缀:", GUILayout.Width(60));
            var pathText = string.IsNullOrEmpty(profile.PathPrefix) ? "未设置" : profile.PathPrefix;
            EditorGUILayout.LabelField(pathText, EditorStyles.miniLabel);
            EditorGUILayout.EndHorizontal();
            
            // 过滤器统计
            var filterCount = profile.Filters?.Count ?? 0;
            var layerCount = profile.Filters?.GroupBy(f => f.Layer).Count() ?? 0;
            
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("过滤器:", GUILayout.Width(60));
            EditorGUILayout.LabelField($"{filterCount}个 ({layerCount}层)", EditorStyles.miniLabel);
            EditorGUILayout.EndHorizontal();
            
            // 处理器统计
            var totalProcessors = 0;
            if (profile.Filters != null)
            {
                foreach (var filter in profile.Filters)
                {
                    if (filter?.Processors != null)
                    {
                        totalProcessors += filter.Processors.Count;
                    }
                }
            }
            
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("处理器:", GUILayout.Width(60));
            EditorGUILayout.LabelField($"{totalProcessors}个", EditorStyles.miniLabel);
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.EndVertical();
            
            EditorGUILayout.Space();
            
            // 操作按钮
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("配置"))
            {
                ConfigWindow.ShowWindow(selectedElement.Profile);
            }
            
            if (GUILayout.Button("检视器"))
            {
                Selection.activeObject = selectedElement.Profile;
                EditorGUIUtility.PingObject(selectedElement.Profile);
            }
            
            EditorGUILayout.EndHorizontal();
        }

        void DrawPathPreview()
        {
            if (selectedElement?.Profile == null)
            {
                EditorGUILayout.HelpBox("无Profile可预览", MessageType.Info);
                return;
            }

            // 保留路径测试功能
            DrawPathTestingSection();
        }

        void DrawPathTestingSection()
        {
            UIHelper.DrawHeader("路径测试工具", "🧪");

            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            EditorGUILayout.BeginHorizontal();
            testPath = EditorGUILayout.TextField("测试路径:", testPath);

            if (GUILayout.Button("测试", GUILayout.Width(50)))
            {
                TestPathMatchingDetailed();
            }

            if (GUILayout.Button("选择", GUILayout.Width(50)))
            {
                SelectAssetForTesting();
            }
            EditorGUILayout.EndHorizontal();

            if (!string.IsNullOrEmpty(testPath))
            {
                EditorGUILayout.HelpBox($"当前测试路径: {testPath}", MessageType.Info);
            }

            EditorGUILayout.EndVertical();
        }

        private void TestPathMatchingDetailed()
        {
            if (selectedElement?.Profile == null || string.IsNullOrEmpty(testPath))
            {
                EditorUtility.DisplayDialog("测试结果", "请输入要测试的路径", "确定");
                return;
            }

            var profile = selectedElement.Profile;
            var result = new System.Text.StringBuilder();

            result.AppendLine($"测试路径: {testPath}");
            result.AppendLine($"Profile: {profile.name}");
            result.AppendLine();

            // 1. 路径前缀匹配测试
            bool pathMatches = profile.IsMatch(testPath);
            result.AppendLine($"✓ 路径前缀匹配: {(pathMatches ? "通过" : "失败")}");
            if (!string.IsNullOrEmpty(profile.PathPrefix))
            {
                result.AppendLine($"  配置前缀: {profile.PathPrefix}");
            }

            if (pathMatches && profile.Filters?.Any() == true)
            {
                result.AppendLine();
                result.AppendLine("✓ 过滤器匹配测试:");

                // 2. 过滤器匹配测试
                var matchingFilters = profile.GetMatchingFilters(testPath);
                if (matchingFilters.Any())
                {
                    foreach (var filter in matchingFilters)
                    {
                        result.AppendLine($"  - {filter.DisplayName} (Layer: {filter.Layer}, Priority: {filter.Priority})");

                        // 显示匹配的处理器
                        if (filter.Processors?.Any() == true)
                        {
                            result.AppendLine($"    处理器: {string.Join(", ", filter.Processors.Select(p => p.name))}");
                        }
                    }
                }
                else
                {
                    result.AppendLine("  无匹配的过滤器");
                }
            }

            EditorUtility.DisplayDialog("路径匹配测试结果", result.ToString(), "确定");
        }

        private void SelectAssetForTesting()
        {
            var selectedAsset = Selection.activeObject;
            if (selectedAsset != null)
            {
                testPath = AssetDatabase.GetAssetPath(selectedAsset);
            }
            else
            {
                EditorUtility.DisplayDialog("提示", "请先在Project窗口中选择一个资产", "确定");
            }
        }

        void HandleSplitterDragging()
        {
            var e = Event.current;
            var splitterRect = this.splitterRect;
            var splitterDragRect = new Rect(splitterRect.x - 2, splitterRect.y, splitterRect.width + 4, splitterRect.height);

            switch (e.type)
            {
                case EventType.MouseDown:
                    if (e.button == 0 && splitterDragRect.Contains(e.mousePosition))
                    {
                        m_IsDraggingSplitter = true;
                        e.Use();
                    }
                    break;

                case EventType.MouseDrag:
                    if (m_IsDraggingSplitter)
                    {
                        var newPercent = e.mousePosition.x / position.width;
                        m_SplitterPercent = Mathf.Clamp(newPercent, 
                            TREE_MIN_WIDTH / position.width, 
                            1 - PANEL_MIN_WIDTH / position.width);
                        Repaint();
                        e.Use();
                    }
                    break;

                case EventType.MouseUp:
                    if (m_IsDraggingSplitter)
                    {
                        m_IsDraggingSplitter = false;
                        e.Use();
                    }
                    break;
            }
        }

        #region 节点操作方法

        void CreateNewNode()
        {
            if (m_TreeView?.treeModel == null)
                return;

            try
            {
                int id = m_TreeView.treeModel.GenerateUniqueID();
                var profile = AssetProfile.Create($"NewProfile_{id}");
                var element = new AssetTreeElement($"新节点_{id}", 0, id, profile);
                
                m_TreeView.treeModel.AddElement(element, m_TreeView.treeModel.root, 0);
                m_TreeView.SetSelection(new[] { id }, TreeViewSelectionOptions.RevealAndFrame);
                
                EditorUtility.SetDirty(m_AssetTree);
                
                Debug.Log($"[AssetTreeWindow] 成功创建新节点: {element.name}");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[AssetTreeWindow] 创建节点失败: {ex.Message}");
                EditorUtility.DisplayDialog("创建失败", "创建节点时发生错误，请查看Console了解详情。", "确定");
            }
        }

        void DeleteSelectedNode()
        {
            var selection = m_TreeView.GetSelection();
            if (selection == null || selection.Count == 0)
            {
                EditorUtility.DisplayDialog("提示", "请先选择要删除的节点", "确定");
                return;
            }

            if (EditorUtility.DisplayDialog("确认删除", "确定要删除选中的节点吗？这将同时删除关联的AssetProfile文件。", "删除", "取消"))
            {
                try
                {
                    // 获取要删除的元素，清理关联的Profile资产
                    var itemsToDelete = selection
                        .Select(id => m_TreeView.FindItem(id, m_TreeView.rootItem) as TreeViewItem<AssetTreeElement>)
                        .Where(item => item?.data != null)
                        .ToList();
                    
                    foreach (var item in itemsToDelete)
                    {
                        if (item.data.Profile != null)
                        {
                            var profilePath = AssetDatabase.GetAssetPath(item.data.Profile);
                            if (!string.IsNullOrEmpty(profilePath))
                            {
                                AssetDatabase.DeleteAsset(profilePath);
                            }
                        }
                    }
                    
                    m_TreeView.treeModel.RemoveElements(selection);
                    m_TreeView.Reload();
                    EditorUtility.SetDirty(m_AssetTree);
                    
                    Debug.Log($"[AssetTreeWindow] 成功删除 {itemsToDelete.Count} 个节点");
                }
                catch (System.Exception ex)
                {
                    Debug.LogError($"[AssetTreeWindow] 删除节点失败: {ex.Message}");
                    EditorUtility.DisplayDialog("删除失败", "删除节点时发生错误，请查看Console了解详情。", "确定");
                }
            }
        }

        void CreateProfileForNode()
        {
            if (selectedElement == null)
                return;

            try
            {
                var profile = AssetProfile.Create($"Profile_{selectedElement.name}_{System.DateTime.Now:HHmmss}");
                selectedElement.Profile = profile;
                
                EditorUtility.SetDirty(m_AssetTree);
                Repaint();
                
                Debug.Log($"[AssetTreeWindow] 为节点 '{selectedElement.name}' 创建Profile: {profile.name}");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[AssetTreeWindow] 创建Profile失败: {ex.Message}");
                EditorUtility.DisplayDialog("创建失败", "创建AssetProfile时发生错误，请查看Console了解详情。", "确定");
            }
        }

        #endregion
    }
}