using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEngine;
using UnityEditor;
using AssetPipeline.Core;
using AssetPipeline.Database;
using Logger = AssetPipeline.Core.Logger;
using Object = UnityEngine.Object;

namespace AssetPipeline.Processors
{
    /// <summary>
    /// 资产处理器基类，提供统一的资产检查和处理功能
    /// </summary>
    public abstract class AssetProcessor : ScriptableObject, IProcessor
    {
        #region 核心配置

        [Header("基础配置")]
        [SerializeField] private string guid = System.Guid.NewGuid().ToString();
        [SerializeField] private bool enabled = true;
        [SerializeField] private int priority = 0;
        
        // 缓存的处理器属性
        private ProcessorAttribute _cachedAttribute;
        
        // 处理器统计
        [NonSerialized] private ProcessorStatistics _statistics;
        
        // 初始化状态
        [NonSerialized] private bool _isInitialized = false;

        #endregion

        #region IProcessor 核心实现

        public string DisplayName
        {
            get
            {
                EnsureAttributeCached();
                return _cachedAttribute?.DisplayName ?? GetType().Name;
            }
        }

        public uint Version
        {
            get
            {
                EnsureAttributeCached();
                return _cachedAttribute?.Version ?? 1;
            }
        }
        
        public string GUID 
        { 
            get 
            { 
                EnsureGuid();
                return guid; 
            } 
        }
        
        public bool Enabled 
        { 
            get => enabled;
            set 
            { 
                if (enabled != value)
                {
                    enabled = value;
                    EditorUtility.SetDirty(this);
                }
            }
        }
        
        public int Priority 
        { 
            get => priority;
            set 
            { 
                if (priority != value)
                {
                    priority = value;
                    EditorUtility.SetDirty(this);
                }
            }
        }
        
        /// <summary>
        /// 检查是否可以处理指定资产
        /// </summary>
        public virtual bool CanProcess(string assetPath)
        {
            if (!enabled) return false;
            if (string.IsNullOrEmpty(assetPath)) return false;
            
            // 检查资产是否存在
            if (!File.Exists(assetPath) && !AssetDatabase.IsValidFolder(assetPath))
            {
                return false;
            }
            
            // 验证Unity资产路径
            if (!assetPath.StartsWith("Assets/") && !assetPath.StartsWith("Packages/"))
            {
                return false;
            }

            if (this is IImportProcessor p && p.OnlyFirstImport)
            {
                var importer = AssetImporter.GetAtPath(assetPath);
                if (importer == null || !importer.importSettingsMissing)
                    return false;
            }
            
            // 确保处理器已初始化
            EnsureInitialized();
            return true;
        }

        public virtual void ClearCache()
        {
            
        }

        #endregion

        #region IProcessorLifecycle 实现

        public virtual void OnAdded()
        {
            EnsureStatistics();
            Log(LogLevel.Debug, $"处理器已添加到配置");
        }
        
        public virtual bool OnStart()
        {
            var isValid = ValidateConfiguration();
            if (isValid)
            {
                Statistics.Reset();
                Statistics.RecordStart();
                _isInitialized = true;
                Log(LogLevel.Debug, $"处理器启动成功");
            }
            else
            {
                Log(LogLevel.Error, $"处理器启动失败 - 配置验证未通过");
            }
            
            return isValid;
        }
        
        public virtual void OnCompleted()
        {
            if (Statistics.ProcessedCount > 0)
            {
                Log(LogLevel.Debug, $"处理器完成 - {Statistics.GetSummary()}");
            }
        }

        #endregion

        #region 统计属性访问

        /// <summary>
        /// 处理器统计信息
        /// </summary>
        public ProcessorStatistics Statistics
        {
            get
            {
                EnsureStatistics();
                return _statistics;
            }
        }

        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;

        #endregion

        #region 内部管理方法

        /// <summary>
        /// 确保GUID存在且有效
        /// </summary>
        private void EnsureGuid()
        {
            if (string.IsNullOrEmpty(guid) || guid == "00000000-0000-0000-0000-000000000000")
            {
                guid = System.Guid.NewGuid().ToString();
                EditorUtility.SetDirty(this);
            }
        }

        /// <summary>
        /// 确保统计对象存在 - 优化版本，减少空值检查
        /// </summary>
        private void EnsureStatistics()
        {
            if (_statistics == null)
            {
                _statistics = new ProcessorStatistics();
                _statistics.RecordStart(); // 立即记录开始时间
            }
        }

        /// <summary>
        /// 确保处理器已初始化
        /// </summary>
        private void EnsureInitialized()
        {
            if (!_isInitialized)
            {
                InitializeProcessor();
            }
        }

        /// <summary>
        /// 处理器初始化
        /// </summary>
        protected virtual bool InitializeProcessor()
        {
            try
            {
                EnsureStatistics();
                EnsureGuid();

                // 缓存属性
                EnsureAttributeCached();
                
                // 子类可以重写此方法进行特定的初始化逻辑
                var isValid = ValidateConfiguration();
                _isInitialized = isValid;
                
                return isValid;
            }
            catch (Exception ex)
            {
                Log(LogLevel.Error, $"处理器初始化失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 验证处理器配置
        /// </summary>
        protected virtual bool ValidateConfiguration()
        {
            if (_cachedAttribute == null)
            {
                Log(LogLevel.Error, $"处理器 {GetType().Name} 缺少 [Processor] 特性标记");
                return false;
            }

            if (string.IsNullOrEmpty(DisplayName))
            {
                Log(LogLevel.Warning, $"处理器显示名称为空");
            }
            
            return true; 
        }
        
        #endregion

        #region 工具方法

        /// <summary>
        /// 确保属性已缓存
        /// </summary>
        private void EnsureAttributeCached()
        {
            if (_cachedAttribute == null)
            {
                var attributes = GetType().GetCustomAttributes(typeof(ProcessorAttribute), false);
                foreach (var attr in attributes)
                {
                    if (attr is ProcessorAttribute processorAttr)
                    {
                        _cachedAttribute = processorAttr;
                        break;
                    }
                }
            }
        }

        #endregion

        #region 结果处理方法
        
        protected CheckResult Error(string message, string details = "")
        {
            var result = CheckResult.Error(message, details);
            RecordResult(result);
            return result;
        }

        protected CheckResult Warning(string message, string details = "")
        {
            var result = CheckResult.Warning(message, details);
            RecordResult(result);
            return result;
        }

        protected CheckResult Info(string message)
        {
            return CheckResult.Info(message);
        }

        protected CheckResult Success(string message = "")
        {
            return CheckResult.Success(message);
        }

        #endregion

        #region 日志方法

        /// <summary>
        /// 统一的日志记录 
        /// </summary>
        protected void Log(LogLevel level, string message, string assetPath = "")
        {
            var logMessage = string.IsNullOrEmpty(assetPath)
                ? $"[{DisplayName}] {message}"
                : $"[{DisplayName}] {message} - {assetPath}";

            Logger.Log(level, LogModule.Processor, logMessage);
        }

        #endregion

        #region Unity生命周期

        protected virtual void OnEnable()
        {
            EnsureInitialized();
        }
        
        protected virtual void OnValidate()
        {
            EnsureGuid();
            
            // 清理非法优先级
            if (priority < 0) priority = 0;
            
            // 标记需要重新初始化
            _isInitialized = false;
        }

        #endregion

        #region 扩展属性

        /// <summary>
        /// 处理器作者
        /// </summary>
        public string Author
        {
            get
            {
                EnsureAttributeCached();
                return _cachedAttribute?.Author ?? "";
            }
        }

        /// <summary>
        /// 处理器描述
        /// </summary>
        public string Description
        {
            get
            {
                EnsureAttributeCached();
                return _cachedAttribute?.Description ?? "";
            }
        }

        /// <summary>
        /// 处理器分类
        /// </summary>
        public string Category
        {
            get
            {
                EnsureAttributeCached();
                return _cachedAttribute?.Category ?? "General";
            }
        }
        
        #endregion
        
        /// <summary>
        /// 记录处理结果并更新统计
        /// </summary>
        protected void RecordResult(CheckResult result)
        {
            if (result == null) return;

            Statistics.RecordResult(result);
        }

        /// <summary>
        /// 获取处理器摘要信息
        /// </summary>
        public virtual string GetSummary()
        {
            var statusText = Enabled ? "启用" : "禁用";
            var statsText = "";
            
            if (Statistics.ProcessedCount > 0)
            {
                statsText = $" ({Statistics.GetSummary()})";
            }
            
            return $"{DisplayName} (v{Version}) - {statusText}{statsText}";
        }

        /// <summary>
        /// 重置为默认配置
        /// </summary>
        public virtual void ResetToDefaults()
        {
            enabled = true;
            priority = 0;
            Statistics.Reset();
            
            EditorUtility.SetDirty(this);
            Log(LogLevel.Info, $"处理器已重置为默认配置: {DisplayName}");
        }

    }
} 