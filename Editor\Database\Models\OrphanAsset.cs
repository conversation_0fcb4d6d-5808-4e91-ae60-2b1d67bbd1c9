using System;
using L10.Editor.AssetPipeline.Sqlite;

namespace AssetPipeline.Database.Models
{
    [Table("OrphanAssets")]
    public class OrphanAsset
    {
        [PrimaryKey]
        public string GUID { get; set; }
        
        public string Path { get; set; }
        public string Type { get; set; }
        public long Size { get; set; }
        public DateTime LastScanTime { get; set; }
        public int ConfirmationLevel { get; set; }
        public bool CanSafeDelete { get; set; }
    }
} 