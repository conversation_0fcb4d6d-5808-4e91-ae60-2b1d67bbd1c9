我要分享的内容是：
随着游戏项目规模的不断扩大，资源管理的复杂度与日俱增。本次分享将围绕一套完整、可扩展的Unity资源规范与检查可视化管理框架展开，探讨如何通过系统化方案解决资源管理痛点。本次分享将通过具体案例，展示资源的自动化处理流程、规范化管理的实现方案，以及自定义资产检视和冗余资源的检测与清理等功能。同时，也将分析框架开发过程中遇到的技术难点及其解决方案，包括资源导入一致性、导入后处理死循环等问题，CacheServer机制。
这套框架旨在帮助项目团队建立统一的可视化资源管理标准，提升开发效率，降低资源冗余，为游戏性能优化提供可靠支持。期待与各位同行一起探讨Unity资源管理的最佳实践！

我的分享内容分为以下几个部分：
1. 资源管理在L10的现状与挑战
2. 资源管理框架的设计与核心模块
3. 规范化管理的落地
4. 利用系统化框架解决技术难点


# Unity资源规范与检查可视化管理框架

## 1. 资源管理在L10的现状与挑战

### 1.1 Unity对资源的导入和管理

1. 资源管理维度
- 游戏开发中需要管理模型、贴图、材质、动画、预制件等多种资源类型
- 资源管理的阶段：
    - 离线管理：资源规范与检查
    - 打包管理：资源分包与构建
    - 运行时管理：资源加载与释放
- 本框架聚焦：离线阶段的资源规范化管理

2. 资源格式分类
- Unity原生支持的格式：直接可用的Unity资源（如Material、Prefab等）
- 需导入处理的格式：需要转换的外部资源（如FBX、PNG等）

3. Unity资产导入系统
- Importer导入器：Unity为每种需处理的资源类型提供专门的导入器
- 导入过程：
    - 生成内部导入数据（存储于Library/metadata）
    - 创建.meta文件（保存导入设置）
    - 在Inspector面板显示与配置导入设置

### 1.2 常见资源问题

- 资源引用丢失问题
    - 删除或移动资源的依赖时未正确处理引用关系
    - 一般是复制/剪切后 GUID 冲突导致的引用丢失
    - 或者代码文件的变更或删除的组件丢失

- 性能与效率问题
    - 导入设置：资源导入参数配置不当，如：
        - 贴图压缩格式选择不当, 导致表现问题或性能问题
        - Readable 属性开启不合理, 导致运行时内存占用过高
    - 资源冗余：存在长期未使用、重复的资源文件
  
- 规范性问题
    - 非法命名：FBX附带的Mesh包含特殊字符
    - 资源路径太长：超出 Windows 文件系统路径长度限制

> 以上问题若不及时处理，可能导致项目体积膨胀、性能下降、协作效率降低等后果

我们要通过主动检查、自动化检查、提交检查等不同手段发现资源问题
通过资源分析、自动化处理、人工干预等不同手段来处理问题
尽早发现问题, 避免不符合规范的资源在游戏工程里使用
保证游戏性能和表现不会因资源规格而受到较大影响

### 1.3 L10项目资源管理存在的问题

- 主动检查与资源分析：通过工具分析并生成问题报告, 由相关人员跟进处理
    - 检查工具散布在项目各处, 缺乏统一维护
    - 检查结果没有统一的管理, 没有统一的反馈处理机制
    - 主动检查和其它检查规范的逻辑分离, 可能存在不一致
        
- 被动检查：本地提交检查 + CI/CD持续交付平台的后置检查
    - 美术在完成编辑后, 提交检查的流程往往需要较长时间, 并可能反复返工
    - 平台检查比较滞后, 可能已经在日常打包里出现问题了, 也不能直观反馈给美术修改

- 资源规范化：通过后处理工具实现自动规范化和修正
    - 资源类别判断逻辑过于冗杂, 条件判断层层嵌套，大量重复, 难以复用
    - 各历史时期的规范堆积, 导致维护困难且易产生处理死循环
    - 检查逻辑完全依赖于代码, 修改规范需要频繁更新代码和重新编译
    - 修改资源规则, 有时候会影响到旧资源, 本地存在不能提交的meta文件修改

## 2. 资源管理框架的设计与核心模块

### 2.1 设计原则与目标

- 用户需求
    - 程序：方便的规范配置和扩展机制
    - QA：统一的自动化检查接口、标准化的检查结果导出
    - 美术：即时清晰的错误提示反馈与一键修复、减少沟通和返工的成本
    - 策划：资源结构划分、资源信息视图化展示

- 设计原则
    1. 系统化
        - 统一管理框架：集成所有检查工具与后处理规范
        - 策略复用机制：通过配置化实现检查处理逻辑的复用
        - 数据持久化存储：构建资源数据库，支持增量信息采集、快速检索和版本追溯
    
    2. 可视化
        - 结构可视化：直观展示与配置资源组织架构
        - 配置可视化：直观展示与配置资源检查规则
        - 状态监控：实时维护并展示资源处理状态

    3. 稳定性
        - 前置检查机制：在资源制作早期快速发现并解决问题
        - 流程把控：确保资源处理流程的稳定性，降低后期排查成本
        - 自动化处理：减少人工干预，提高处理效率

    4. 兼容性
        - 版本兼容：新规则变更不影响已有资源
        - 增量更新：仅对修改后的资源应用新规则

### 2.2 整体架构

- 界面展示 + 流程与结构图

- 可视化管理界面
    - 检查规则配置
    - 检查结果反馈
    - 自定义资产视图

- 资产处理管线
    - 导入处理管线 AssetImportPipeline
    - 修改检查管线 AssetModificationPipeline
    - 提交检查管线 SvnCommitPipeline
    - 自动化检查管线 CheckRunnerPipeline
    
- 数据库持久化
    - 资产基本信息 AssetBaseInfo
    - 依赖关系图谱 AssetDependency
    - GUID历史关系 GUIDHistory
    - 自定义数据提供器 IDatabaseProvider

### 2.3 核心功能模块

- 序列化数据结构

    - 资产结构 AssetTree: 树形的虚拟文件结构，用于组织资源规则
        - 支持添加多个资产规则 AssetProfile 节点，并调整它们的层级关系
        - 在流程中，根据资源路径遍历节点，匹配节点定义的路径，成功则继续遍历子节点，否则返回
        
    - 资产规则 AssetProfile: 根据路径筛选资源，定义资产的处理和规范
        - 支持添加多个资产筛选 AssetFilter，并对其排序
        - 路径筛选支持多种匹配方式，包括Equals、Contains、StartsWith、EndsWith、通配符和正则表达式
        
    - 资产筛选 AssetFilter：根据文件名和资产类型筛选资源
        - 支持添加多个资产处理 AssetProcessor，并对其排序
        - 文件名支持多种匹配方式，包括Equals、Contains、StartsWith、EndsWith、通配符和正则表达式
        - 支持配置资产类型、例外文件和子路径匹配

    - 资产类型 AssetType: 定义资产类型
        - 支持按照拓展名配置一类资产

    - 资产处理 AssetProcessor：对资产进行处理
        - 每个实例可单独配置对应参数
        - 支持设置例外文件
        - 定义了在不同上下文中应该如何检查、修正、修复、输出、保存数据等等
        
    - 序列化对象配置的保存: 由于无法确定ScriptableObject和要修改的资源的导入顺序，需要使用独立于Unity导入机制的资源文件
        - 每个序列化对象都有一个对应的Json数据类 (AssetTreeJsonData、AssetProfileJsonData、AssetFilterJsonData、AssetTypeJsonData、AssetProcessorJsonData)
        - 配置修改后需要点击应用, 保存为Json格式的配置文件

- 资产处理管线

    - 处理流程：根据资源路径匹配当前配置的资产规则和筛选, 在特定的上下文中获取对应接口的资产处理策略, 按照管线自定义的方式进行处理
    - 通过定义资产管线流程接口, 自定义 AssetProcessor 实现对应接口, 即可在流程中处理资产
        - 通用处理接口 IProcessor：OnAdded(添加到Profile之后执行的初始化方法) + OnStart(检查对应流程启动时的预处理)
        - 导入处理接口 IImportProcessor：OnlyFirstImport(仅资产首次导入时处理)
            - 贴图导入处理接口 贴图导入处理接口 ITextureImportProcessor: OnPreprocessTexture(贴图导入前处理) + OnPostprocessTexture(贴图导入后处理)
            - 模型导入处理接口 IModelImportProcessor: OnPreprocessModel(模型导入前处理) + OnPostprocessModel(模型导入后处理)
        - 修改检查接口 IModificationProcessor：OnWillCreateAsset + OnWillDeleteAsset + OnWillMoveAsset + OnWillSaveAssets(修改时检查)
        - SVN提交前检查接口 ISvnPreCommitProcessor：OnPreCommit(提交前检查, 根据返回结果决定是否阻断提交)
        - SVN提交后检查接口 ISvnPostCommitProcessor：OnPostCommit(提交后检查, 根据返回结果决定是否阻断提交)
        - 主动检查接口 ICheckRunnerProcessor: RunCheck(执行检查并自动导出检查结果到Log目录)
        
        - 自动修复接口 IAutoFixProvider: DoFix(修复资产的行为, 返回值表示是否修复成功)
        - 数据库采集接口 IDatabaseProvider: GetTableSchema(获取数据表结构) + CollectData(采集资源数据) + Query(查询数据)
            - Processor 实现 IDatabaseProvider 接口进行数据采集
            - 执行过程中自动建立以 Processor 类名命名的数据表
            - 根据定义的字段自动生成查询接口
            - 支持增量更新和定向采集


- 增量更新只能在OnPostprocessAllAssets中执行, 因为只有这个方法会在导入已有meta的资源后执行
自定义的数据采集，也就是实现IDatabaseProvider接口的情况，只能在OnPostprocessAllAssets里采集数据，因为导入其他人上传的已有meta的资产，只会走这个后处理

            - 数据表结构定义
            public class TableSchema
            {
                public string TableName { get; set; }  // 表名,默认使用Processor类名
                public Dictionary<string, Type> Columns { get; set; }  // 字段名和类型定义
                public string[] PrimaryKeys { get; set; }  // 主键字段
                public string[] IndexFields { get; set; }  // 索引字段
            }


    - 导入后处理流程 AssetImportPipeline
        - 监听 Unity AssetPostprocessor 的资产导入事件
        - 维护导入过程的上下文
        - 在导入处理方法中获取 IImportProcessor 进行处理
        - 检查到问题禁止保存, 弹出检查结果提示

    - 修改后检查流程 AssetModificationPipeline
        - 监听 Unity AssetModificationProcessor 的资产修改事件
        - 获取 IModificationProcessor 进行处理
        - 检查到问题禁止保存, 弹出检查结果提示

    - 提交检查流程 SvnCommitPipeline
        - 提交前获取 ISvnPreCommitProcessor 进行检查
        - 提交后获取 ISvnPostCommitProcessor 进行检查
        - 阻断不合规资源提交, 弹出检查结果提示
            
    - 自动化检查流程 CheckRunnerPipeline
        - 支持手动/外部调用触发检查
        - 执行 ICheckRunnerProcessor 进行检查
        - 生成标准化检查报告
        - 支持导出结果到指定目录

- 资产数据库设计
    - 数据库持久化维护的基本表：

    - AssetBaseInfo 资产基本信息表
        CREATE TABLE AssetBaseInfo (
            GUID TEXT PRIMARY KEY,              -- 资产GUID
            Name TEXT NOT NULL,                 -- 资产名称(包括拓展名)
            Type TEXT NOT NULL,                 -- 资产类型
            Size INTEGER NOT NULL,              -- 文件大小(字节)
            Path TEXT NOT NULL,                 -- 资产路径
            ModifiedTime DATETIME NOT NULL      -- 修改时间(最近的svn修改时间)
            MD5 TEXT NOT NULL,                  -- 文件MD5
            CheckResultType INTEGER NOT NULL,   -- 检查结果类型(Unknown=0/Error=1/Warning=2/Info=3)
        );
        CREATE INDEX idx_asset_path ON AssetBaseInfo(Path);
        CREATE INDEX idx_asset_type ON AssetBaseInfo(Type);
        CREATE INDEX idx_asset_md5 ON AssetBaseInfo(MD5);
        CREATE INDEX idx_asset_checkresult ON AssetBaseInfo(CheckResultType);

    - AssetDependency 资产依赖表: 收集各种依赖关系, 通过 GUID 或 路径关联, 优化依赖分析性能
        CREATE TABLE AssetDependency (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            GUID TEXT NOT NULL,                 -- 资产GUID
            DependencyGUID TEXT NOT NULL,       -- 依赖资产的GUID
            FOREIGN KEY(GUID) REFERENCES Asset(GUID),
        );
        CREATE INDEX idx_dependency_guid ON AssetDependency(GUID);
        CREATE INDEX idx_dependency_dep ON AssetDependency(DependencyGUID);

    - GUIDHistory 资产GUID历史表: 追踪资源GUID变更, 辅助解决引用丢失问题
        CREATE TABLE GUIDHistory (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            GUID TEXT NOT NULL,                 -- 资产GUID
            Path TEXT NOT NULL,                 -- 历史路径
        );
        CREATE INDEX idx_history_guid ON GUIDHistory(GUID);
        

    **1. AssetBaseInfo** - 资产基础信息表
    - GUID、Path、Name、Type、Extension
    - Size、MD5、ImportTime、ModifyTime
    - CheckTime、CheckResult、IsDeleted

    **2. AssetDependency** - 资产依赖关系表
    - AssetGUID → DependencyGUID 映射
    - DependencyType（Direct/Indirect）

    **3. AssetReference** - 被引用关系表
    - TargetGUID ← ReferencerGUID 反向索引
    - ReferenceType、ReferenceCount

    **4. DuplicateGroups** - 重复资产组表
    - MD5、GroupSize、TotalSize
    - PrimaryGUID、CanDelete（基于精确MD5匹配）

    **5. MaterialFingerprint** - 材质指纹表
    - ShaderName、PropertyHash、TextureHash
    - FullHash、TextureCount、PropertyCount（精确材质对比）

    **6. GUIDHistory** - GUID历史记录表
    - GUID、Path、Operation、Timestamp
    - SourcePath（精确的GUID变更追踪）

    **7. OrphanAssets** - 孤立资产表
    - GUID、Path、Type、Size、LastScanTime
    - ConfirmationLevel、CanSafeDelete（明确无引用资产）

    **8. ProcessStatus** - 处理状态表
    - AssetGUID、ProcessorName、ProcessTime
    - ResultType、ResultMessage

    **9. CheckResult** - 检查结果表
    - AssetGUID、CheckerName、CheckTime
    - ResultType、Category、Message、CanAutoFix

    - 自定义数据提供器(IDatabaseProvider)
        - 支持扩展数据源
        - 提供统一的数据访问接口
        - 实现数据持久化存储


## 3. 规范化管理的落地

- 使用新框架后, 资源管理流程规范化案例

### 3.1 集成各流程的检查

#### 资源规范化管理的改进
- 处理策略模块化
    - 实现检查逻辑的复用和配置
    - 大幅降低重复代码，提高维护效率
    - 案例分析：预设处理器

- 配置驱动的架构设计
    - 将检查逻辑中的路径和数据配置迁移到序列化配置文件中
    - 避免规则变动频繁编译代码, 提升系统稳定性, 降低维护成本
    - 案例分析：预设处理器

#### 被动检查流程优化
- 实时检查反馈机制
    - 资源导入和保存时自动触发检查
    - 问题及时反馈，支持自动修复
    - 减少后期返工，提高开发效率
    - 案例分析: 检查结果界面

- 流程效率优化
    - 数据库存储检查状态
    - SVN提交时读取已有数据库检查状态避免重复检查
    - 显著提升工作流程效率
    - 案例分析：SVN提交时复用数据库已有检查结果
        - 如果没有找到检查过的状态, 则reimport执行检查
        - 错误和警告状态会弹出检查结果界面, 错误会阻断提交

#### 主动检查统一管理
- 标准化检查流程
    - 通过工具分析并生成问题报告, 由相关人员跟进处理
    - 检查结果有统一的管理, 有统一的反馈处理机制
    - 主动检查和其它检查的逻辑可复用, 实现检查逻辑的统一

### 3.2 资源引用丢失与GUID冲突

#### 资源引用丢失的检测与处理
- 实时检测机制
    - 导入和保存时自动检测引用完整性
    - 发现丢失立即弹出检查结果窗口警告
    - 支持一键修复清理丢失引用

- 资源删除保护
    - 从数据库 AssetDependency 表获取被引用的情况
    - Unity内删除保护
        - OnWillDeleteAsset 监测文件将被删除
        - 被其它资源引用时, 弹出警告确认框
        - 用户可选择取消或强制删除
    - 非Unity删除时的提示
        - OnPostprocessAllAssets 监测文件删除
        - 被其它资源引用时, 提示清理关联引用

#### 丢失组件的追踪
- 丢失的GUID资源视图
    - 从数据库 GUIDHistory 表获取丢失的GUID对应的历史路径
    - 展示所有引用该GUID的资源
    - 支持搜索GUID和路径

#### GUID冲突的预防
- 提交检查机制
    - 预防复制后meta文件冲突: 提交时拦截meta文件的GUID修改
    - 预防剪切后没在SVN删除旧资源: 对于added的meta文件的GUID，通过GUIDHistory表，找到对应的资源路径列表中的其它资源路径，若当前提交的没有delete该文件, 检查SVN服务器上是否存在(svn list url)，如果存在则表示会在SVN服务器上产生GUID冲突


### 3.3 资源冗余监测

- 冗余资源的产生原因
    - 复制导致的完全重复的资源
    - 历史遗留的废弃资源
        - 未及时清理的旧资源
        - 测试用的临时资源

- 本地冗余资源检测
    - 基于MD5的重复文件检测
        - 利用资产信息数据库存储文件MD5
        - 通过MD5匹配快速识别内容重复的资源
        - 提供可视化界面展示重复资源分布
        - (演示重复资源视图)
    - 引用分析
        - 资产依赖数据库搜索资源引用关系
        - 识别未被Prefab和场景引用的资源
        - 展示长期未使用的资源列表
        - (演示资源引用视图)

- 如何尽可能少地产生冗余资源
    - 建立清晰的目录结构划分, 避免因导入设置差异而复制资源
    - 导入时基于数据库自动检查MD5重复, 及时提示并引导处理重复资源
    
- 其它资源冗余的情况
    - 材质球完全重复：收集属性、贴图、Shader数据到数据库


## 4. 利用系统化框架解决技术难点

### 4.1 CacheServer 对导入流程的影响
- CacheServer 缓存服务器的原理
    - 首次导入时上传内部导入数据作为缓存
    - 内部导入数据位于 Library/metadata，而非导入设置.meta文件
    - CacheServer 缓存的是什么？
        - 资源文件本身 "myTexture.png"
        - 导入设置 "myTexture.png.meta"
        - 资源导入版本 "TextureImporter的内部版本号"
        - 当前平台 
    - metadata检查: 当 Unity 即将导入一个资源时，会生成以上所有源数据的MD5哈希值，当哈希值不同时会重新导入，否则从 CacheServer 下载
    - 相同资源在不同项目中会产生独立的缓存
    - 即使是完全相同的文件，如果路径不同也会重复缓存
    - 升级版 Accelerator 会自动删除一段时间内没有使用过的缓存

- 如何兼容旧资源
    - 问题背景
        - 即使没有主动修改资源, 旧资源的.meta文件也可能发生变更
        - 这会导致大量历史资源产生不必要的变更, 误提交的话可能影响打包, 增加维护成本
    - 在没有主动修改的情况下为什么会影响旧资源的.meta?
        - 源数据的MD5哈希值变更，触发了reimport
            - 当前平台发生变化
            - Unity 资源导入版本更新
            - CacheServer 缓存丢失
    - 解决方案
        - Reimport 的情况不可避免, 为了让新规范不影响老资源, 在metadata检查中, 不执行自定义导入后处理
            - metadata检查发生在 EditorApplication.delayCall 之前
            - delayCall 是一个one-shot静态委托，在所有导入和检视面板更新之后调用一次
                - 它的作用是将一些函数的执行延迟到检视面板更新完成之后，每个函数在添加到这个委托后仅执行一次，然后就会被移除 
                - 可以默认关闭自定义导入后处理管线, delayCall 调用之后才开启
        - 但是如果是Importer的序列化版本变更导致的 Reimport, 即使没有进行任何自定义导入后处理, 也会更新序列化格式导致meta变更
            - delayCall 调用之前在OnPostprocessAllAssets中revert掉重新序列化导致的变更
            - 这个功能会有一定的编辑器性能开销, 可选择是否开启

- 跳过导入后处理的问题
    - 问题背景
        - 启用 CacheServer 时，如果修改后的资源导入设置为已缓存的状态，Unity会直接从服务器下载资源, 不会触发导入流程
        - 导致 AssetPostprocessor 中的自定义后处理流程无法触发，规范检查和处理失效
    - 解决方案
        - SVN提交后检查流程中 强制执行 AssetDatabase.ImportAsset(path, ImportAssetOptions.DontDownloadFromCacheServer);

### 4.2 导入后处理死循环
- 问题背景
    - 导入后处理流程中, 如果处理不当, 可能导致死循环
    - 甚至可能导致AssetDatabase损坏，重新Calculating Asset Hashes, 浪费美术大量时间
- 原因分析
    - 不断创建新资产, 循环触发新资产的导入
    - 对资源的后处理修改形成环, 一直在重新导入
        - 修改资源文件，自动触发重新导入
        - 修改导入设置，且执行了 importer.SaveAndReimport() 触发重新导入
        - e.g.
            贴图MyTexture.png，压缩质量=best，最大尺寸=2048
            A处理：最大尺寸>1024的贴图修改为1024        -> myTexture的最大尺寸改为1024
            B处理：最大尺寸=1024的贴图压缩质量修改为fast -> myTexture的压缩质量改为fast
            C处理：压缩质量=fast的贴图最大尺寸修改为2048 -> myTexture的最大尺寸改为2048
- 解决思路
    - 在自定义的导入后处理流程中, 维护导入处理的上下文
        - 导入轮次信息: importer.SaveAndReimport() 触发重新导入时, 视为轮次+1
        - 每轮的修改记录: 记录每轮所有导入处理对资源和资源导入设置的修改
    - 当一轮导入处理的修改记录出现重复的时候, 就发现了死循环, 记录下来, 在下一轮导入开始时弹出提示并跳过所有导入后处理
    - 如何获得修改记录的标识? 
        - 递归遍历所有可写属性, 生成唯一的修改标识
            - 如果是 基本值类型 或 枚举类型 或 string 就直接比较
            - 如果是 继承自UnityEngine.Object的类型，比较资产路径
            - 如果是 数组 遍历其每个元素
            - 如果是 struct 就遍历其所有字段
            - 如果是 class 就遍历其所有属性
            - 特殊的，textureImporter 的 platformSetting 通过调用方法得到再去递归比较
    - 修改记录的获取会影响性能, 所以优化一下
        - 在导入轮次大于阈值时, 才开始检查修改记录
