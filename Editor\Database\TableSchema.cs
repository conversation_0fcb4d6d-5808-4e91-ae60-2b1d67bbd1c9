using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace AssetPipeline.Database
{
    /// <summary>
    /// 数据表结构定义
    /// </summary>
    public class TableSchema
    {
        /// <summary>
        /// 表名，默认使用Processor类名
        /// </summary>
        public string TableName { get; set; }
        
        /// <summary>
        /// 字段名和类型定义
        /// </summary>
        public Dictionary<string, Type> Columns { get; set; }
        
        /// <summary>
        /// 主键字段
        /// </summary>
        public string[] PrimaryKeys { get; set; }
        
        /// <summary>
        /// 索引字段
        /// </summary>
        public string[] IndexFields { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public TableSchema()
        {
            Columns = new Dictionary<string, Type>();
            PrimaryKeys = new string[0];
            IndexFields = new string[0];
        }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public TableSchema(string tableName, Dictionary<string, Type> columns, 
            string[] primaryKeys = null, string[] indexFields = null)
        {
            TableName = tableName;
            Columns = columns ?? new Dictionary<string, Type>();
            PrimaryKeys = primaryKeys ?? new string[0];
            IndexFields = indexFields ?? new string[0];
        }
        
        /// <summary>
        /// 生成建表SQL语句
        /// </summary>
        public string GenerateCreateTableSQL()
        {
            var sql = new StringBuilder();
            sql.AppendLine($"CREATE TABLE IF NOT EXISTS {TableName} (");
            
            var columnDefinitions = new List<string>();
            
            // 生成字段定义
            foreach (var column in Columns)
            {
                var columnDef = $"    {column.Key} {GetSQLiteType(column.Value)}";
                
                // 添加主键约束
                if (PrimaryKeys.Length == 1 && PrimaryKeys[0] == column.Key)
                {
                    columnDef += " PRIMARY KEY";
                }
                
                columnDefinitions.Add(columnDef);
            }
            
            sql.AppendLine(string.Join(",\n", columnDefinitions));
            
            // 添加复合主键约束
            if (PrimaryKeys.Length > 1)
            {
                sql.AppendLine($",    PRIMARY KEY ({string.Join(", ", PrimaryKeys)})");
            }
            
            sql.AppendLine(");");
            
            return sql.ToString();
        }
        
        /// <summary>
        /// 生成索引创建SQL语句
        /// </summary>
        public List<string> GenerateCreateIndexSQL()
        {
            var indexSQLs = new List<string>();
            
            foreach (var column in IndexFields)
            {
                var indexName = $"idx_{TableName}_{column}";
                var sql = $"CREATE INDEX IF NOT EXISTS {indexName} ON {TableName}({column});";
                indexSQLs.Add(sql);
            }
            
            return indexSQLs;
        }
        
        /// <summary>
        /// 将.NET类型转换为SQLite类型
        /// </summary>
        private string GetSQLiteType(Type type)
        {
            if (type == typeof(int) || type == typeof(long) || type == typeof(bool))
            {
                return "INTEGER";
            }
            else if (type == typeof(float) || type == typeof(double))
            {
                return "REAL";
            }
            else if (type == typeof(string))
            {
                return "TEXT";
            }
            else if (type == typeof(DateTime))
            {
                return "TEXT"; // SQLite中存储为ISO8601字符串
            }
            else
            {
                return "TEXT"; // 默认为文本类型
            }
        }
        
        /// <summary>
        /// 获取表的完整SQL定义（包括索引）
        /// </summary>
        public List<string> GetFullSQLDefinition()
        {
            var sqls = new List<string>();
            
            // 添加建表语句
            sqls.Add(GenerateCreateTableSQL());
            
            // 添加索引语句
            sqls.AddRange(GenerateCreateIndexSQL());
            
            return sqls;
        }
    }
} 