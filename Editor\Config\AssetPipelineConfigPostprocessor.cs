using System;
using UnityEngine;
using UnityEditor;
using AssetPipeline.Core;
using Logger = AssetPipeline.Core.Logger;

namespace AssetPipeline.Config
{
    /// <summary>
    /// 配置文件变化监听器
    /// 当团队成员通过版本控制更新配置文件时，自动刷新缓存
    /// </summary>
    public class AssetPipelineConfigPostprocessor : AssetPostprocessor
    {
        static bool _IsClear = false;
        
        public override int GetPostprocessOrder()
        {
            return Int32.MinValue; // 设置为最高优先级，确保在AssetImportPipeline之前执行
        }

        void OnPreprocessAsset()
        {
            if (!_IsClear && IsConfigFile(assetPath))
            {
                _IsClear = true;
                ClearCache();
            }
        }

        static void OnPostprocessAllAssets(string[] importedAssets, string[] deletedAssets, string[] movedAssets, string[] movedFromAssetPaths)
        {
            if (_IsClear)
            {
                _IsClear = false;
                return;
            }
            
            bool needRefresh = false;

            foreach (string assetPath in importedAssets)
            {
                if (IsConfigFile(assetPath))
                {
                    Logger.Debug(LogModule.Core, $"[ConfigPostprocessor] 检测到配置文件变化: {assetPath}");
                    needRefresh = true;
                    break;
                }
            }

            if (!needRefresh)
            {
                foreach (string assetPath in deletedAssets)
                {
                    if (IsConfigFile(assetPath))
                    {
                        Logger.Debug(LogModule.Core, $"[ConfigPostprocessor] 检测到配置文件删除: {assetPath}");
                        needRefresh = true;
                        break;
                    }
                }
            }

            if (!needRefresh)
            {
                foreach (string assetPath in movedAssets)
                {
                    if (IsConfigFile(assetPath))
                    {
                        Logger.Debug(LogModule.Core, $"[ConfigPostprocessor] 检测到配置文件移动: {assetPath}");
                        needRefresh = true;
                        break;
                    }
                }
            }

            if (needRefresh)
            {
                ClearCache();
            }
        }

        private static void ClearCache()
        {
            try
            {
                AssetPipelineConfig.ClearCache();
                Logger.Info(LogModule.Core, "[ConfigPostprocessor] 配置文件变化，自动刷新缓存完成");
            }
            catch (System.Exception ex)
            {
                Logger.Error(LogModule.Core, $"[ConfigPostprocessor] 刷新缓存时发生错误: {ex.Message}");
            }
        }

        private static bool IsConfigFile(string assetPath)
        {
            if (string.IsNullOrEmpty(assetPath)) return false;

            if (!assetPath.StartsWith(AssetPipelineConfig.ASSET_PIPELINE_FOLDER, System.StringComparison.OrdinalIgnoreCase))
            {
                return false;
            }

            var extension = System.IO.Path.GetExtension(assetPath);
            return extension.Equals(".cs", System.StringComparison.OrdinalIgnoreCase) || 
                   extension.Equals(".asset", System.StringComparison.OrdinalIgnoreCase) ||
                   extension.Equals(".json", System.StringComparison.OrdinalIgnoreCase);
        }

    }
} 