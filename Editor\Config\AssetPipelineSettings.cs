using UnityEngine;
using UnityEditor;

namespace AssetPipeline.Config
{
    /// <summary>
    /// 本地的编辑器设置
    /// </summary>
    public static class AssetPipelineSettings
    {
        #region EditorPrefs Keys

        private const string KeyPrefix = "AssetPipeline.";
        
        // 管道控制
        private const string KeyEnableImportPipeline = KeyPrefix + "EnableImportPipeline";
        private const string KeyEnableSvnCommitPipeline = KeyPrefix + "EnableSvnCommitPipeline";
        private const string KeyEnableCheckRunnerPipeline = KeyPrefix + "EnableCheckRunnerPipeline";
        private const string KeyEnableModificationPipeline = KeyPrefix + "EnableModificationPipeline";
        private const string KeyShowCommitWarnings = KeyPrefix + "ShowCommitWarnings";
        
        // 阻断行为
        private const string KeyBlockWarningsOnCommit = KeyPrefix + "BlockWarningsOnCommit";
        private const string KeyBlockWarningsOnImport = KeyPrefix + "BlockWarningsOnImport";
        
        // 显示设置
        private const string KeyShowDetailedResults = KeyPrefix + "ShowDetailedResults";
        private const string KeyAutoOpenResultWindow = KeyPrefix + "AutoOpenResultWindow";
        private const string KeyShowProgressDialog = KeyPrefix + "ShowProgressDialog";
        
        // 性能优化
        private const string KeyMaxBatchResultCount = KeyPrefix + "MaxBatchResultCount";
        private const string KeyEnableResultDeduplication = KeyPrefix + "EnableResultDeduplication";
        private const string KeyEnableAssetTreeCaching = KeyPrefix + "EnableAssetTreeCaching";
        private const string KeyMaxCacheSize = KeyPrefix + "MaxCacheSize";
        
        // 调试设置
        private const string KeyEnableVerboseLogging = KeyPrefix + "EnableVerboseLogging";
        private const string KeyShowPerformanceStats = KeyPrefix + "ShowPerformanceStats";
        private const string KeyEnableProcessorTiming = KeyPrefix + "EnableProcessorTiming";
        private const string KeyLogCacheHitRate = KeyPrefix + "LogCacheHitRate";
        
        // 兼容性设置
        private const string KeyEnableCacheServerCompatibility = KeyPrefix + "EnableCacheServerCompatibility";
        private const string KeyPreventOldAssetReimport = KeyPrefix + "PreventOldAssetReimport";

        #endregion

        #region 管道控制属性
        
        public static bool EnableImportPipeline
        {
            get => EditorPrefs.GetBool(KeyEnableImportPipeline, true);
            set => EditorPrefs.SetBool(KeyEnableImportPipeline, value);
        }

        public static bool EnableSvnCommitPipeline
        {
            get => EditorPrefs.GetBool(KeyEnableSvnCommitPipeline, true);
            set => EditorPrefs.SetBool(KeyEnableSvnCommitPipeline, value);
           
        }

        public static bool EnableCheckRunnerPipeline
        {
            get => EditorPrefs.GetBool(KeyEnableCheckRunnerPipeline, true);
            set => EditorPrefs.SetBool(KeyEnableCheckRunnerPipeline, value);
        }

        public static bool EnableModificationPipeline
        {
            get => EditorPrefs.GetBool(KeyEnableModificationPipeline, true);
            set => EditorPrefs.SetBool(KeyEnableModificationPipeline, value);
        }

        public static bool ShowCommitWarnings
        {
            get => EditorPrefs.GetBool(KeyShowCommitWarnings, true);
            set => EditorPrefs.SetBool(KeyShowCommitWarnings, value);
        }

        #endregion

        #region 阻断行为属性

        public static bool BlockWarningsOnCommit
        {
            get => EditorPrefs.GetBool(KeyBlockWarningsOnCommit, false);
            set => EditorPrefs.SetBool(KeyBlockWarningsOnCommit, value);
        }

        public static bool BlockWarningsOnImport
        {
            get => EditorPrefs.GetBool(KeyBlockWarningsOnImport, false);
            set => EditorPrefs.SetBool(KeyBlockWarningsOnImport, value);
        }

        #endregion

        #region 显示设置属性

        public static bool ShowDetailedResults
        {
            get => EditorPrefs.GetBool(KeyShowDetailedResults, true);
            set => EditorPrefs.SetBool(KeyShowDetailedResults, value);
        }

        public static bool AutoOpenResultWindow
        {
            get => EditorPrefs.GetBool(KeyAutoOpenResultWindow, true);
            set => EditorPrefs.SetBool(KeyAutoOpenResultWindow, value);
        }

        public static bool ShowProgressDialog
        {
            get => EditorPrefs.GetBool(KeyShowProgressDialog, true);
            set => EditorPrefs.SetBool(KeyShowProgressDialog, value);
        }

        #endregion

        #region 性能优化属性

        public static int MaxBatchResultCount
        {
            get => EditorPrefs.GetInt(KeyMaxBatchResultCount, 1000);
            set => EditorPrefs.SetInt(KeyMaxBatchResultCount, value);
        }

        public static bool EnableResultDeduplication
        {
            get => EditorPrefs.GetBool(KeyEnableResultDeduplication, true);
            set => EditorPrefs.SetBool(KeyEnableResultDeduplication, value);
        }

        public static bool EnableAssetTreeCaching
        {
            get => EditorPrefs.GetBool(KeyEnableAssetTreeCaching, true);
            set => EditorPrefs.SetBool(KeyEnableAssetTreeCaching, value);
        }

        public static int MaxCacheSize
        {
            get => EditorPrefs.GetInt(KeyMaxCacheSize, 500);
            set => EditorPrefs.SetInt(KeyMaxCacheSize, value);
        }

        #endregion

        #region 调试设置属性

        public static bool EnableVerboseLogging
        {
            get => EditorPrefs.GetBool(KeyEnableVerboseLogging, false);
            set => EditorPrefs.SetBool(KeyEnableVerboseLogging, value);
        }

        public static bool ShowPerformanceStats
        {
            get => EditorPrefs.GetBool(KeyShowPerformanceStats, false);
            set => EditorPrefs.SetBool(KeyShowPerformanceStats, value);
        }

        public static bool EnableProcessorTiming
        {
            get => EditorPrefs.GetBool(KeyEnableProcessorTiming, false);
            set => EditorPrefs.SetBool(KeyEnableProcessorTiming, value);
        }

        public static bool LogCacheHitRate
        {
            get => EditorPrefs.GetBool(KeyLogCacheHitRate, false);
            set => EditorPrefs.SetBool(KeyLogCacheHitRate, value);
        }

        #endregion

        #region 兼容性设置属性

        public static bool EnableCacheServerCompatibility
        {
            get => EditorPrefs.GetBool(KeyEnableCacheServerCompatibility, true);
            set => EditorPrefs.SetBool(KeyEnableCacheServerCompatibility, value);
        }

        public static bool PreventOldAssetReimport
        {
            get => EditorPrefs.GetBool(KeyPreventOldAssetReimport, true);
            set => EditorPrefs.SetBool(KeyPreventOldAssetReimport, value);
        }

        #endregion

        #region 工具方法

        /// <summary>
        /// 重置所有设置为默认值
        /// </summary>
        public static void ResetToDefaults()
        {
            var keys = new[]
            {
                KeyEnableImportPipeline, KeyEnableSvnCommitPipeline, KeyEnableCheckRunnerPipeline, KeyEnableModificationPipeline,
                KeyShowCommitWarnings,
                KeyBlockWarningsOnCommit, KeyBlockWarningsOnImport,
                KeyShowDetailedResults, KeyAutoOpenResultWindow, KeyShowProgressDialog,
                KeyMaxBatchResultCount, KeyEnableResultDeduplication, KeyEnableAssetTreeCaching, KeyMaxCacheSize,
                KeyEnableVerboseLogging, KeyShowPerformanceStats, KeyEnableProcessorTiming, KeyLogCacheHitRate,
                KeyEnableCacheServerCompatibility, KeyPreventOldAssetReimport
            };

            foreach (var key in keys)
            {
                EditorPrefs.DeleteKey(key);
            }
            
            Debug.Log("[AssetPipelineSettings] 所有设置已重置为默认值");
        }
        
        #endregion
    }
} 