using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using AssetPipeline.Core;
using AssetPipeline.Config;
using AssetPipeline.Processors;
using AssetPipeline.UI.Components;

namespace AssetPipeline.UI.Windows
{
    /// <summary>
    /// Asset Pipeline 主窗口
    /// </summary>
    public class AssetPipelineMainWindow : EditorWindow
    {
        private Vector2 scrollPosition;
        private int selectedTab = 0;
        private readonly string[] tabNames = { "Pipeline控制", "检查结果", "系统状态", "性能监控" };
        
        // 检查结果数据
        private List<CheckResult> latestResults = new List<CheckResult>();
        private string selectedAssetPath = "";
        
        // 状态统计
        private int totalAssets = 0;
        private int errorCount = 0;
        private int warningCount = 0;
        
        // 缓存配置信息，避免频繁读取
        private AssetTree cachedMainAssetTree;
        private double lastConfigRefreshTime = 0;
        private const double CONFIG_REFRESH_INTERVAL = 5.0; // 5秒刷新一次配置缓存
        
        [MenuItem("Asset Pipeline/主控制面板", priority = 1)]
        public static void ShowWindow()
        {
            var window = GetWindow<AssetPipelineMainWindow>("Asset Pipeline - 主控制面板");
            window.minSize = new Vector2(600, 400);
            window.Show();
        }
        
        [MenuItem("Asset Pipeline/配置编辑器/AssetTree编辑器", priority = 10)]
        public static void ShowAssetTreeEditor()
        {
            AssetTreeWindow.ShowWindow();
        }
        
        [MenuItem("Asset Pipeline/配置编辑器/Profile配置中心", priority = 11)]
        public static void ShowProfileConfigCenter()
        {
            var profiles = AssetDatabase.FindAssets("t:AssetProfile")
                .Select(AssetDatabase.GUIDToAssetPath)
                .Select(AssetDatabase.LoadAssetAtPath<AssetProfile>)
                .Where(p => p != null)
                .ToArray();
            
            if (profiles.Length == 0)
            {
                EditorUtility.DisplayDialog("提示", "没有找到AssetProfile，请先在AssetTree编辑器中创建", "确定");
                return;
            }
            
            var menu = new GenericMenu();
            foreach (var profile in profiles)
            {
                menu.AddItem(new GUIContent(profile.DisplayName), false, () =>
                {
                    ConfigWindow.ShowWindow(profile);
                });
            }
            menu.ShowAsContext();
        }
        

        
        void OnEnable()
        {
            RefreshData();
        }
        
        void OnGUI()
        {
            DrawHeader();
            DrawTabs();
            
            switch (selectedTab)
            {
                case 0: DrawPipelineControlTab(); break;
                case 1: DrawCheckResultsTab(); break;
                case 2: DrawSystemStatusTab(); break;
                case 3: DrawPerformanceMonitorTab(); break;
            }
        }
        
        #region UI绘制方法
        
        private void DrawHeader()
        {
            EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
            
            GUILayout.Label("Asset Pipeline 管理中心", EditorStyles.boldLabel);
            
            GUILayout.FlexibleSpace();
            
            // 快速操作按钮
            if (GUILayout.Button("刷新数据", EditorStyles.toolbarButton))
            {
                RefreshData();
            }
            
            if (GUILayout.Button("打开配置", EditorStyles.toolbarButton))
            {
                var config = AssetPipelineConfig.Instance;
                if (config != null)
                {
                    Selection.activeObject = config;
                    EditorGUIUtility.PingObject(config);
                }
            }
            
            EditorGUILayout.EndHorizontal();
        }
        
        private void DrawTabs()
        {
            selectedTab = GUILayout.Toolbar(selectedTab, tabNames);
            EditorGUILayout.Space();
        }
        
        private void DrawPipelineControlTab()
        {
            EditorGUILayout.LabelField("Pipeline 状态控制", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("启用全部", GUILayout.Height(30)))
            {
                SetAllPipelinesEnabled(true);
            }
            if (GUILayout.Button("禁用全部", GUILayout.Height(30)))
            {
                SetAllPipelinesEnabled(false);
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
             PipelineStatusIndicator.DrawStatusIndicator("导入处理Pipeline", "AssetImportPipeline.Enabled");
             PipelineStatusIndicator.DrawStatusIndicator("修改检查Pipeline", "AssetModificationPipeline.Enabled");
             PipelineStatusIndicator.DrawStatusIndicator("提交检查Pipeline", "SvnCommitPipeline.Enabled");
             PipelineStatusIndicator.DrawStatusIndicator("自动检查Pipeline", "CheckRunnerPipeline.Enabled");
            
            EditorGUILayout.Space();
            
            EditorGUILayout.LabelField("快速操作", EditorStyles.boldLabel);
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("运行全项目检查"))
            {
                RunFullProjectCheck();
            }
            
            if (GUILayout.Button("打开AssetTree编辑器"))
            {
                AssetTreeWindow.ShowWindow();
            }
            
            if (GUILayout.Button("打开配置中心"))
            {
                ShowProfileConfigCenter();
            }
            
            EditorGUILayout.EndHorizontal();
        }
        
        private void DrawCheckResultsTab()
        {
            EditorGUILayout.LabelField("最近检查结果", EditorStyles.boldLabel);
            
            // 结果统计
            EditorGUILayout.BeginHorizontal();
            GUILayout.Label($"总数: {latestResults.Count}");
            GUI.color = Color.red;
            GUILayout.Label($"错误: {errorCount}");
            GUI.color = Color.yellow;
            GUILayout.Label($"警告: {warningCount}");
            GUI.color = Color.white;
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            if (latestResults.Count == 0)
            {
                EditorGUILayout.HelpBox("暂无检查结果。点击'运行检查'开始检查。", MessageType.Info);
                if (GUILayout.Button("运行选中资源检查"))
                {
                    RunSelectedAssetCheck();
                }
                return;
            }
            
            // 结果列表
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            
            foreach (var result in latestResults)
            {
                DrawCheckResultItem(result);
            }
            
            EditorGUILayout.EndScrollView();
            
            // 底部操作
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("清空结果"))
            {
                latestResults.Clear();
                errorCount = warningCount = 0;
            }
            
            if (GUILayout.Button("详细查看"))
            {
                CheckResultWindow.ShowResults(selectedAssetPath, latestResults);
            }
            EditorGUILayout.EndHorizontal();
        }
        
        private void DrawSystemStatusTab()
        {
            EditorGUILayout.LabelField("系统状态", EditorStyles.boldLabel);
            
            // 基本信息
            EditorGUILayout.LabelField("配置信息:");
            EditorGUI.indentLevel++;
            
            var config = AssetPipelineConfig.Instance;
            EditorGUILayout.LabelField($"主配置: {(config != null ? "已加载" : "未找到")}");
            
            // 使用缓存的MainAssetTree，避免频繁读取JSON
            var mainAssetTree = GetCachedMainAssetTree();
            EditorGUILayout.LabelField($"AssetTree: {(mainAssetTree != null ? "已配置" : "未配置")}");
            
            EditorGUI.indentLevel--;
            
            EditorGUILayout.Space();
            
            // 统计信息
            EditorGUILayout.LabelField("项目统计:");
            EditorGUI.indentLevel++;
            EditorGUILayout.LabelField($"总资源数: {totalAssets}");
            EditorGUILayout.LabelField($"问题资源数: {errorCount + warningCount}");
            EditorGUI.indentLevel--;
            
            EditorGUILayout.Space();
            
            // 操作按钮
            if (GUILayout.Button("刷新统计信息"))
            {
                RefreshStatistics();
            }
            
            if (GUILayout.Button("强制刷新配置"))
            {
                // 强制刷新配置缓存
                cachedMainAssetTree = null;
                lastConfigRefreshTime = 0;
                Repaint();
            }
            
            if (GUILayout.Button("打开日志目录"))
            {
                var logPath = System.IO.Path.Combine(Application.dataPath, "../Logs");
                if (System.IO.Directory.Exists(logPath))
                {
                    EditorUtility.RevealInFinder(logPath);
                }
            }
        }

        private void DrawPerformanceMonitorTab()
        {
            EditorGUILayout.LabelField("性能监控", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            // 使用性能监控组件
            PerformanceMonitor.DrawMonitorPanel();

            EditorGUILayout.Space();

            // 额外的系统信息
            EditorGUILayout.LabelField("系统信息", EditorStyles.boldLabel);
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            EditorGUILayout.LabelField($"Unity版本: {Application.unityVersion}");
            EditorGUILayout.LabelField($"平台: {Application.platform}");
            EditorGUILayout.LabelField($"编辑器运行时间: {EditorApplication.timeSinceStartup:F1}秒");

            var memoryUsage = System.GC.GetTotalMemory(false) / (1024.0 * 1024.0);
            EditorGUILayout.LabelField($"托管内存: {memoryUsage:F1} MB");

            EditorGUILayout.EndVertical();
        }

        private void DrawCheckResultItem(CheckResult result)
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            
            EditorGUILayout.BeginHorizontal();
            
            // 类型图标
            var oldColor = GUI.color;
            switch (result.resultType)
            {
                case CheckResultType.Error:
                    GUI.color = Color.red;
                    GUILayout.Label("⚠", GUILayout.Width(20));
                    break;
                case CheckResultType.Warning:
                    GUI.color = Color.yellow;
                    GUILayout.Label("⚠", GUILayout.Width(20));
                    break;
                default:
                    GUI.color = Color.blue;
                    GUILayout.Label("ℹ", GUILayout.Width(20));
                    break;
            }
            GUI.color = oldColor;
            
            // 信息内容
            EditorGUILayout.BeginVertical();
            GUILayout.Label(result.message, EditorStyles.wordWrappedLabel);
            if (!string.IsNullOrEmpty(result.assetPath))
            {
                GUILayout.Label($"路径: {result.assetPath}", EditorStyles.miniLabel);
            }
            EditorGUILayout.EndVertical();
            
            // 操作按钮
            if (!string.IsNullOrEmpty(result.assetPath))
            {
                if (GUILayout.Button("定位", GUILayout.Width(50)))
                {
                    var obj = AssetDatabase.LoadAssetAtPath<Object>(result.assetPath);
                    if (obj != null)
                    {
                        Selection.activeObject = obj;
                        EditorGUIUtility.PingObject(obj);
                    }
                }
            }
            
            EditorGUILayout.EndHorizontal();
            EditorGUILayout.EndVertical();
        }
        
        #endregion
        
        #region 功能方法
        
        private void SetAllPipelinesEnabled(bool enabled)
        {
            EditorPrefs.SetBool("AssetImportPipeline.Enabled", enabled);
            EditorPrefs.SetBool("AssetModificationPipeline.Enabled", enabled);
            EditorPrefs.SetBool("SvnCommitPipeline.Enabled", enabled);
            EditorPrefs.SetBool("CheckRunnerPipeline.Enabled", enabled);
            
            Debug.Log($"所有Pipeline已{(enabled ? "启用" : "禁用")}");
        }
        
        /// <summary>
        /// 获取缓存的MainAssetTree，避免频繁读取JSON
        /// </summary>
        private AssetTree GetCachedMainAssetTree()
        {
            var currentTime = EditorApplication.timeSinceStartup;
            
            if (cachedMainAssetTree == null || (currentTime - lastConfigRefreshTime) > CONFIG_REFRESH_INTERVAL)
            {
                cachedMainAssetTree = AssetPipelineConfig.MainAssetTree;
                lastConfigRefreshTime = currentTime;
            }
            
            return cachedMainAssetTree;
        }
        
        private void RunFullProjectCheck()
        {
            latestResults.Clear();
            
            RefreshResultStatistics();
            selectedTab = 1; // 切换到检查结果tab
        }
        
        private void RunSelectedAssetCheck()
        {
            var selected = Selection.activeObject;
            if (selected == null)
            {
                EditorUtility.DisplayDialog("提示", "请先选择要检查的资源", "确定");
                return;
            }
            
            selectedAssetPath = AssetDatabase.GetAssetPath(selected);
            Debug.Log($"检查资源: {selectedAssetPath}");
            
            RefreshData();
        }
        
        private void RefreshData()
        {
            cachedMainAssetTree = null;
            lastConfigRefreshTime = 0;
            
            RefreshStatistics();
            RefreshResultStatistics();
        }
        
        private void RefreshStatistics()
        {
            var allAssets = AssetDatabase.FindAssets("");
            totalAssets = allAssets.Length;
        }
        
        private void RefreshResultStatistics()
        {
            errorCount = 0;
            warningCount = 0;
            
            foreach (var result in latestResults)
            {
                if (result.resultType == CheckResultType.Error)
                    errorCount++;
                else if (result.resultType == CheckResultType.Warning)
                    warningCount++;
            }
        }
        
        #endregion
    }
} 