using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using AssetPipeline.Processors;

namespace AssetPipeline.UI
{
    /// <summary>
    /// 安全Inspector管理器
    /// 管理属性锁定的作用域控制，确保锁定功能只在特定上下文中生效
    /// </summary>
    public static class SafeInspectorManager
    {
        #region 作用域管理

        private static bool isLockingEnabled = false;
        private static HashSet<string> activeScopes = new HashSet<string>();
        private static Dictionary<string, HashSet<string>> scopeLockedProperties = new Dictionary<string, HashSet<string>>();

        /// <summary>
        /// 启用属性锁定功能
        /// </summary>
        /// <param name="scope">作用域名称</param>
        public static void EnablePropertyLocking(string scope = "default")
        {
            isLockingEnabled = true;
            activeScopes.Add(scope);
            
            if (!scopeLockedProperties.ContainsKey(scope))
            {
                scopeLockedProperties[scope] = new HashSet<string>();
            }
            
            Debug.Log($"[SafeInspectorManager] 已启用属性锁定功能 - 作用域: {scope}");
        }

        /// <summary>
        /// 禁用属性锁定功能
        /// </summary>
        /// <param name="scope">作用域名称</param>
        public static void DisablePropertyLocking(string scope = "default")
        {
            activeScopes.Remove(scope);
            
            if (activeScopes.Count == 0)
            {
                isLockingEnabled = false;
            }
            
            Debug.Log($"[SafeInspectorManager] 已禁用属性锁定功能 - 作用域: {scope}");
        }

        /// <summary>
        /// 检查属性锁定功能是否启用
        /// </summary>
        public static bool IsLockingEnabled => isLockingEnabled;

        /// <summary>
        /// 获取活动的作用域列表
        /// </summary>
        public static IReadOnlyCollection<string> ActiveScopes => activeScopes;

        #endregion

        #region 属性锁定管理

        /// <summary>
        /// 锁定属性
        /// </summary>
        /// <param name="assetPath">资产路径</param>
        /// <param name="propertyPath">属性路径</param>
        /// <param name="scope">作用域</param>
        public static void LockProperty(string assetPath, string propertyPath, string scope = "default")
        {
            if (!isLockingEnabled) return;
            
            var key = GetPropertyKey(assetPath, propertyPath);
            
            if (!scopeLockedProperties.ContainsKey(scope))
            {
                scopeLockedProperties[scope] = new HashSet<string>();
            }
            
            scopeLockedProperties[scope].Add(key);
            SaveLockedProperties(scope);
            
            Debug.Log($"[SafeInspectorManager] 已锁定属性: {propertyPath} (资产: {assetPath}, 作用域: {scope})");
        }

        /// <summary>
        /// 解锁属性
        /// </summary>
        /// <param name="assetPath">资产路径</param>
        /// <param name="propertyPath">属性路径</param>
        /// <param name="scope">作用域</param>
        public static void UnlockProperty(string assetPath, string propertyPath, string scope = "default")
        {
            if (!isLockingEnabled) return;
            
            var key = GetPropertyKey(assetPath, propertyPath);
            
            if (scopeLockedProperties.ContainsKey(scope))
            {
                scopeLockedProperties[scope].Remove(key);
                SaveLockedProperties(scope);
            }
            
            Debug.Log($"[SafeInspectorManager] 已解锁属性: {propertyPath} (资产: {assetPath}, 作用域: {scope})");
        }

        /// <summary>
        /// 检查属性是否被锁定
        /// </summary>
        /// <param name="assetPath">资产路径</param>
        /// <param name="propertyPath">属性路径</param>
        /// <param name="scope">作用域</param>
        /// <returns>是否被锁定</returns>
        public static bool IsPropertyLocked(string assetPath, string propertyPath, string scope = "default")
        {
            if (!isLockingEnabled) return false;
            
            var key = GetPropertyKey(assetPath, propertyPath);
            
            return scopeLockedProperties.ContainsKey(scope) && 
                   scopeLockedProperties[scope].Contains(key);
        }

        /// <summary>
        /// 获取指定资产的所有锁定属性
        /// </summary>
        /// <param name="assetPath">资产路径</param>
        /// <param name="scope">作用域</param>
        /// <returns>锁定的属性路径列表</returns>
        public static List<string> GetLockedProperties(string assetPath, string scope = "default")
        {
            var result = new List<string>();
            
            if (!isLockingEnabled || !scopeLockedProperties.ContainsKey(scope))
                return result;
            
            var prefix = assetPath + "|";
            foreach (var key in scopeLockedProperties[scope])
            {
                if (key.StartsWith(prefix))
                {
                    result.Add(key.Substring(prefix.Length));
                }
            }
            
            return result;
        }

        /// <summary>
        /// 清除指定资产的所有锁定属性
        /// </summary>
        /// <param name="assetPath">资产路径</param>
        /// <param name="scope">作用域</param>
        public static void ClearAssetLocks(string assetPath, string scope = "default")
        {
            if (!isLockingEnabled || !scopeLockedProperties.ContainsKey(scope))
                return;
            
            var prefix = assetPath + "|";
            var keysToRemove = new List<string>();
            
            foreach (var key in scopeLockedProperties[scope])
            {
                if (key.StartsWith(prefix))
                {
                    keysToRemove.Add(key);
                }
            }
            
            foreach (var key in keysToRemove)
            {
                scopeLockedProperties[scope].Remove(key);
            }
            
            SaveLockedProperties(scope);
            Debug.Log($"[SafeInspectorManager] 已清除资产的所有锁定: {assetPath} (作用域: {scope})");
        }

        #endregion

        #region 属性值验证和恢复

        /// <summary>
        /// 验证并恢复锁定属性的值
        /// </summary>
        /// <param name="importer">资产导入器</param>
        /// <param name="scope">作用域</param>
        /// <returns>是否有属性被恢复</returns>
        public static bool ValidateAndRestoreLockedProperties(AssetImporter importer, string scope = "default")
        {
            if (!isLockingEnabled || importer == null) return false;
            
            var lockedProperties = GetLockedProperties(importer.assetPath, scope);
            if (lockedProperties.Count == 0) return false;
            
            bool hasChanges = false;
            var serializedImporter = new SerializedObject(importer);
            
            foreach (var propertyPath in lockedProperties)
            {
                var property = serializedImporter.FindProperty(propertyPath);
                if (property != null)
                {
                    var expectedValue = GetExpectedPropertyValue(importer.assetPath, propertyPath, scope);
                    var currentValue = GetPropertyValueAsString(property);
                    
                    if (currentValue != expectedValue && !string.IsNullOrEmpty(expectedValue))
                    {
                        SetPropertyValueFromString(property, expectedValue);
                        hasChanges = true;
                        
                        Debug.Log($"[SafeInspectorManager] 已恢复锁定属性: {propertyPath} = {expectedValue}");
                    }
                }
            }
            
            if (hasChanges)
            {
                serializedImporter.ApplyModifiedProperties();
            }
            
            return hasChanges;
        }

        /// <summary>
        /// 保存属性的期望值
        /// </summary>
        /// <param name="assetPath">资产路径</param>
        /// <param name="propertyPath">属性路径</param>
        /// <param name="value">期望值</param>
        /// <param name="scope">作用域</param>
        public static void SaveExpectedPropertyValue(string assetPath, string propertyPath, string value, string scope = "default")
        {
            var key = GetExpectedValueKey(assetPath, propertyPath, scope);
            EditorPrefs.SetString(key, value);
        }

        /// <summary>
        /// 获取属性的期望值
        /// </summary>
        /// <param name="assetPath">资产路径</param>
        /// <param name="propertyPath">属性路径</param>
        /// <param name="scope">作用域</param>
        /// <returns>期望值</returns>
        public static string GetExpectedPropertyValue(string assetPath, string propertyPath, string scope = "default")
        {
            var key = GetExpectedValueKey(assetPath, propertyPath, scope);
            return EditorPrefs.GetString(key, "");
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 生成属性键
        /// </summary>
        private static string GetPropertyKey(string assetPath, string propertyPath)
        {
            return $"{assetPath}|{propertyPath}";
        }

        /// <summary>
        /// 生成期望值键
        /// </summary>
        private static string GetExpectedValueKey(string assetPath, string propertyPath, string scope)
        {
            return $"ExpectedValue_{scope}_{assetPath}|{propertyPath}";
        }

        /// <summary>
        /// 保存锁定属性到EditorPrefs
        /// </summary>
        private static void SaveLockedProperties(string scope)
        {
            if (scopeLockedProperties.ContainsKey(scope))
            {
                var value = string.Join(";", scopeLockedProperties[scope]);
                EditorPrefs.SetString($"LockedProperties_{scope}", value);
            }
        }

        /// <summary>
        /// 从EditorPrefs加载锁定属性
        /// </summary>
        private static void LoadLockedProperties(string scope)
        {
            var value = EditorPrefs.GetString($"LockedProperties_{scope}", "");
            
            if (!scopeLockedProperties.ContainsKey(scope))
            {
                scopeLockedProperties[scope] = new HashSet<string>();
            }
            else
            {
                scopeLockedProperties[scope].Clear();
            }
            
            if (!string.IsNullOrEmpty(value))
            {
                var keys = value.Split(';');
                foreach (var key in keys)
                {
                    if (!string.IsNullOrEmpty(key))
                    {
                        scopeLockedProperties[scope].Add(key);
                    }
                }
            }
        }

        /// <summary>
        /// 获取SerializedProperty的字符串值
        /// </summary>
        private static string GetPropertyValueAsString(SerializedProperty property)
        {
            switch (property.propertyType)
            {
                case SerializedPropertyType.Boolean:
                    return property.boolValue ? "1" : "0";
                case SerializedPropertyType.Integer:
                    return property.intValue.ToString();
                case SerializedPropertyType.Float:
                    return property.floatValue.ToString();
                case SerializedPropertyType.String:
                    return property.stringValue;
                case SerializedPropertyType.Enum:
                    return property.enumValueIndex.ToString();
                default:
                    return property.stringValue ?? "";
            }
        }

        /// <summary>
        /// 从字符串设置SerializedProperty的值
        /// </summary>
        private static void SetPropertyValueFromString(SerializedProperty property, string value)
        {
            switch (property.propertyType)
            {
                case SerializedPropertyType.Boolean:
                    property.boolValue = value == "1";
                    break;
                case SerializedPropertyType.Integer:
                    if (int.TryParse(value, out int intValue))
                        property.intValue = intValue;
                    break;
                case SerializedPropertyType.Float:
                    if (float.TryParse(value, out float floatValue))
                        property.floatValue = floatValue;
                    break;
                case SerializedPropertyType.String:
                    property.stringValue = value;
                    break;
                case SerializedPropertyType.Enum:
                    if (int.TryParse(value, out int enumValue))
                        property.enumValueIndex = enumValue;
                    break;
            }
        }

        #endregion

        #region 初始化

        /// <summary>
        /// 初始化管理器
        /// </summary>
        [InitializeOnLoadMethod]
        private static void Initialize()
        {
            // 加载所有作用域的锁定属性
            var scopes = new[] { "default", "preset", "pipeline" };
            foreach (var scope in scopes)
            {
                LoadLockedProperties(scope);
            }
        }

        #endregion
    }
}
