using UnityEditor;
using AssetPipeline.Core;

namespace AssetPipeline.Processors.Fixes
{
    public class SetTextureFormatFix : IAutoFix
    {
        private readonly TextureImporterFormat _targetFormat;

        public SetTextureFormatFix(TextureImporterFormat format)
        {
            _targetFormat = format;
        }

        public string GetDescription() => $"将纹理格式设置为 {_targetFormat}";

        public bool CanFix(string assetPath)
        {
            return AssetImporter.GetAtPath(assetPath) is TextureImporter;
        }

        public bool DoFix(string assetPath)
        {
            if (AssetImporter.GetAtPath(assetPath) is TextureImporter importer)
            {
                var platformSettings = importer.GetDefaultPlatformTextureSettings();
                platformSettings.format = _targetFormat;
                importer.SetPlatformTextureSettings(platformSettings);
                importer.SaveAndReimport();
                return true;
            }
            return false;
        }
    }
} 