using UnityEditor;
using AssetPipeline.UI.Windows;
using AssetPipeline.Config;
using AssetPipeline.Core;

namespace AssetPipeline.UI
{
    public static class AssetPipelineMenu
    {
        #region 主要功能窗口
        
        [MenuItem("Asset Pipeline/主控制面板", priority = 1)]
        public static void OpenMainControlPanel()
        {
            AssetPipelineMainWindow.ShowWindow();
        }
        
        // [MenuItem("Asset Pipeline/Advanced/Asset Tree Editor", priority = 102)]
        // public static void OpenAssetTreeEditor()
        // {
        //     AssetTreeWindow.ShowWindow();
        // }
        //
        // [MenuItem("Asset Pipeline/Advanced/Check Results Viewer", priority = 103)]
        // public static void OpenCheckResultsViewer()
        // {
        //     CheckResultWindow.ShowWindow();
        // }
        
        #endregion
        
        #region 配置和设置
        
        [MenuItem("Asset Pipeline/Settings/Pipeline Configuration", priority = 201)]
        public static void OpenPipelineConfiguration()
        {
            var config = AssetPipelineConfig.Instance;
            if (config != null)
            {
                Selection.activeObject = config;
                EditorGUIUtility.PingObject(config);
            }
            else
            {
                EditorUtility.DisplayDialog("Configuration Not Found", 
                    "AssetPipelineConfig could not be found or created. Please check for errors.", "OK");
            }
        }
        
        /*
        [MenuItem("Asset Pipeline/Settings/Logger Settings", priority = 202)]
        public static void OpenLoggerSettings()
        {
            // 这里可以打开Logger设置窗口
            EditorUtility.DisplayDialog("Logger设置", 
                "Logger设置功能正在开发中，请通过代码配置Logger参数。", "确定");
        }
        */
        
        #endregion
        
        #region 快速操作
        
        /*
        [MenuItem("Asset Pipeline/Quick Actions/Refresh All Caches", priority = 301)]
        public static void RefreshAllCaches()
        {
            var config = AssetPipelineConfig.Instance;
            if (config?.assetTree != null)
            {
                config.assetTree.ClearCache();
                EditorUtility.DisplayDialog("缓存刷新", "所有缓存已刷新完成。", "确定");
            }
            else
            {
                EditorUtility.DisplayDialog("错误", "AssetTree配置未找到。", "确定");
            }
        }
        */
        
        /*
        [MenuItem("Asset Pipeline/Quick Actions/Run Full Asset Check", priority = 302)]
        public static void RunFullAssetCheck()
        {
            if (EditorUtility.DisplayDialog("完整资源检查", 
                "这将对所有资源执行完整检查，可能需要较长时间。是否继续？", "继续", "取消"))
            {
                // 这里可以调用完整的资源检查流程
                EditorUtility.DisplayDialog("检查完成", "完整资源检查功能正在开发中。", "确定");
            }
        }
        */
        
        /*
        [MenuItem("Asset Pipeline/Quick Actions/Clean Temporary Files", priority = 303)]
        public static void CleanTemporaryFiles()
        {
            if (EditorUtility.DisplayDialog("清理临时文件", 
                "这将清理Asset Pipeline产生的临时文件。是否继续？", "清理", "取消"))
            {
                // 这里可以添加临时文件清理逻辑
                EditorUtility.DisplayDialog("清理完成", "临时文件清理完成。", "确定");
            }
        }
        */
        
        #endregion
        
        #region Pipeline控制
        
        // [MenuItem("Asset Pipeline/Pipeline Control/Enable All Pipelines", priority = 401)]
        // public static void EnableAllPipelines()
        // {
        //     AssetPipelineSettings.EnableImportPipeline = true;
        //     AssetPipelineSettings.EnableSvnCommitPipeline = true;
        //     AssetPipelineSettings.EnableCheckRunnerPipeline = true;
        //     AssetPipelineSettings.EnableModificationPipeline = true;
        //     EditorUtility.DisplayDialog("Pipeline Enabled", "All pipelines have been enabled.", "OK");
        // }
        //
        // [MenuItem("Asset Pipeline/Pipeline Control/Disable All Pipelines", priority = 402)]
        // public static void DisableAllPipelines()
        // {
        //     if (EditorUtility.DisplayDialog("Disable Pipelines", 
        //         "This will disable all Asset Pipeline functions. Are you sure?", "Disable", "Cancel"))
        //     {
        //         AssetPipelineSettings.EnableImportPipeline = false;
        //         AssetPipelineSettings.EnableSvnCommitPipeline = false;
        //         AssetPipelineSettings.EnableCheckRunnerPipeline = false;
        //         AssetPipelineSettings.EnableModificationPipeline = false;
        //         EditorUtility.DisplayDialog("Pipeline Disabled", "All pipelines have been disabled.", "OK");
        //     }
        // }
        
        #endregion
        
        #region 上下文菜单（右键菜单）
        
        // [MenuItem("Assets/Asset Pipeline/Analyze This Asset", validate = true)]
        // public static bool ValidateAnalyzeSelectedAsset()
        // {
        //     return Selection.activeObject != null;
        // }
        
        /*
        [MenuItem("Assets/Asset Pipeline/Check This Asset", priority = 1001)]
        public static void CheckSelectedAsset()
        {
            if (Selection.activeObject != null)
            {
                var assetPath = AssetDatabase.GetAssetPath(Selection.activeObject);
                
                // 这里可以对选中的资源执行检查
                EditorUtility.DisplayDialog("检查资源", 
                    $"正在检查资源: {assetPath}\n检查功能正在开发中。", "确定");
            }
        }
        
        [MenuItem("Assets/Asset Pipeline/Check This Asset", validate = true)]
        public static bool ValidateCheckSelectedAsset()
        {
            return Selection.activeObject != null;
        }
        */
        
        #endregion
        
        #region 工具栏快捷方式
        
        /*
        [MenuItem("Tools/Asset Pipeline/Quick Check %#&c", priority = 1)]
        public static void QuickCheck()
        {
            // 快速检查当前选中的资源
            if (Selection.activeObject != null)
            {
                CheckSelectedAsset();
            }
            else
            {
                RunFullAssetCheck();
            }
        }
        */
        
        // [MenuItem("Tools/Asset Pipeline/Toggle Pipeline %#&p", priority = 2)]
        // public static void TogglePipeline()
        // {
        //     bool areAnyEnabled = AssetPipelineSettings.EnableImportPipeline || 
        //                         AssetPipelineSettings.EnableSvnCommitPipeline || 
        //                         AssetPipelineSettings.EnableCheckRunnerPipeline ||
        //                         AssetPipelineSettings.EnableModificationPipeline;
        //     if (areAnyEnabled)
        //     {
        //         DisableAllPipelines();
        //     }
        //     else
        //     {
        //         EnableAllPipelines();
        //     }
        // }
        
        #endregion
    }
} 