using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using UnityEditor.Presets;

namespace AssetPipeline.Processors
{
    /// <summary>
    /// PresetProcessor的改进版自定义Inspector
    /// 提供直观的属性选择界面，使用Unity Inspector风格的属性名称和分组
    /// </summary>
    [CustomEditor(typeof(PresetProcessor), true)]
    public class PresetProcessorEditor : Editor
    {
        #region 序列化属性

        private SerializedProperty processingModeProperty;
        private SerializedProperty targetPresetProperty;
        private SerializedProperty allowMipMapsFlexibilityProperty;
        private SerializedProperty allowReadWriteFlexibilityProperty;
        private SerializedProperty allowPlatformTextureSizeFlexibilityProperty;
        private SerializedProperty allowPlatformTextureFormatFlexibilityProperty;
        private SerializedProperty enableDetailedLoggingProperty;
        private SerializedProperty preserveUserDataProperty;

        #endregion

        #region Unity Preset Editor

        private Editor cachedPresetEditor;
        private bool showPresetEditor = true;

        #endregion

        #region 简化的属性选择器

        private bool showPropertySelector = false;
        private List<PropertySelectorSystem.PropertyInfo> availableProperties = new List<PropertySelectorSystem.PropertyInfo>();
        private Vector2 propertyScrollPosition;
        private string searchTerm = "";

        #endregion

        #region Unity生命周期

        void OnEnable()
        {
            // 获取序列化属性
            processingModeProperty = serializedObject.FindProperty("processingMode");
            targetPresetProperty = serializedObject.FindProperty("targetPreset");
            allowMipMapsFlexibilityProperty = serializedObject.FindProperty("allowMipMapsFlexibility");
            allowReadWriteFlexibilityProperty = serializedObject.FindProperty("allowReadWriteFlexibility");
            allowPlatformTextureSizeFlexibilityProperty = serializedObject.FindProperty("allowPlatformTextureSizeFlexibility");
            allowPlatformTextureFormatFlexibilityProperty = serializedObject.FindProperty("allowPlatformTextureFormatFlexibility");
            enableDetailedLoggingProperty = serializedObject.FindProperty("enableDetailedLogging");
            preserveUserDataProperty = serializedObject.FindProperty("preserveUserData");

            // 生产环境模式不需要复杂的属性列表初始化
        }

        void OnDisable()
        {
            // 清理缓存的编辑器
            if (cachedPresetEditor != null)
            {
                DestroyImmediate(cachedPresetEditor);
                cachedPresetEditor = null;
            }
        }

        #endregion

        #region Inspector GUI

        public override void OnInspectorGUI()
        {
            serializedObject.Update();

            DrawHeader();
            DrawBasicConfiguration();
            DrawProductionFlexibilityConfiguration();
            DrawPresetEditor();
            DrawAdvancedOptions();
            DrawActionButtons();

            if (serializedObject.ApplyModifiedProperties())
            {
                EditorUtility.SetDirty(target);
            }
        }

        /// <summary>
        /// 绘制头部信息
        /// </summary>
        private void DrawHeader()
        {
            var processor = target as PresetProcessor;
            var mode = (PresetProcessor.ProcessingMode)processingModeProperty.enumValueIndex;

            EditorGUILayout.Space();

            if (mode == PresetProcessor.ProcessingMode.PresetMode)
            {
                EditorGUILayout.HelpBox(
                    "预设模式：仅在资产首次导入时应用设置，后续导入保留用户修改。\n" +
                    "使用场景：为新资产提供标准设置，同时允许自定义调整。",
                    MessageType.Info);
            }
            else
            {
                EditorGUILayout.HelpBox(
                    "锁死模式：每次导入都强制应用设置，覆盖任何用户修改。\n" +
                    "使用场景：强制执行项目标准和性能要求。",
                    MessageType.Warning);
            }
        }

        /// <summary>
        /// 绘制基础配置
        /// </summary>
        private void DrawBasicConfiguration()
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("基础配置", EditorStyles.boldLabel);

            EditorGUILayout.PropertyField(processingModeProperty, new GUIContent("处理模式"));
            EditorGUILayout.PropertyField(targetPresetProperty, new GUIContent("目标预设"));

            // 验证Preset兼容性
            if (targetPresetProperty.objectReferenceValue != null)
            {
                var processor = target as PresetProcessor;
                var presetTypeName = processor.GetPresetTypeName();
                EditorGUILayout.LabelField("预设类型", presetTypeName ?? "未知");
            }
        }

        /// <summary>
        /// 绘制生产环境灵活性配置
        /// </summary>
        private void DrawProductionFlexibilityConfiguration()
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("生产环境灵活性控制", EditorStyles.boldLabel);

            EditorGUILayout.HelpBox(
                "生产环境模式：默认锁定所有预设属性，仅允许对关键属性进行有限的灵活性配置。\n" +
                "勾选下方选项表示允许该属性的灵活性，不勾选表示严格锁定。",
                MessageType.Info);

            DrawSimpleFlexibilityOptions();
        }

        /// <summary>
        /// 绘制简化的灵活性选项
        /// </summary>
        private void DrawSimpleFlexibilityOptions()
        {
            EditorGUILayout.Space();

            // 纹理基础属性灵活性
            EditorGUILayout.LabelField("纹理基础属性", EditorStyles.boldLabel);

            EditorGUILayout.PropertyField(allowMipMapsFlexibilityProperty,
                new GUIContent("允许 Generate Mip Maps 灵活性", "允许美术人员调整是否生成Mip Maps"));

            EditorGUILayout.PropertyField(allowReadWriteFlexibilityProperty,
                new GUIContent("允许 Read/Write Enabled 灵活性", "允许美术人员调整纹理的读写权限"));

            EditorGUILayout.Space();

            // 平台特定设置灵活性
            EditorGUILayout.LabelField("平台特定设置", EditorStyles.boldLabel);

            EditorGUILayout.PropertyField(allowPlatformTextureSizeFlexibilityProperty,
                new GUIContent("允许平台纹理尺寸灵活性", "允许美术人员调整各平台的最大纹理尺寸"));

            EditorGUILayout.PropertyField(allowPlatformTextureFormatFlexibilityProperty,
                new GUIContent("允许平台纹理格式灵活性", "允许美术人员调整各平台的纹理格式和压缩设置"));

            EditorGUILayout.Space();

            // 显示当前配置摘要
            DrawFlexibilityConfigurationSummary();
        }

        /// <summary>
        /// 绘制灵活性配置摘要
        /// </summary>
        private void DrawFlexibilityConfigurationSummary()
        {
            var processor = target as PresetProcessor;

            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("当前配置摘要", EditorStyles.boldLabel);

            var flexibleCount = 0;
            var totalOptions = 4;

            if (processor.AllowMipMapsFlexibility) flexibleCount++;
            if (processor.AllowReadWriteFlexibility) flexibleCount++;
            if (processor.AllowPlatformTextureSizeFlexibility) flexibleCount++;
            if (processor.AllowPlatformTextureFormatFlexibility) flexibleCount++;

            var lockedCount = totalOptions - flexibleCount;

            EditorGUILayout.LabelField($"🔒 严格锁定: {lockedCount} 项");
            EditorGUILayout.LabelField($"🔓 允许灵活: {flexibleCount} 项");

            if (flexibleCount == 0)
            {
                EditorGUILayout.HelpBox("完全锁定模式：所有属性都将被严格控制", MessageType.Info);
            }
            else if (flexibleCount <= 2)
            {
                EditorGUILayout.HelpBox("保守灵活模式：少量关键属性允许调整", MessageType.Info);
            }
            else
            {
                EditorGUILayout.HelpBox("宽松灵活模式：多个属性允许美术调整", MessageType.Warning);
            }

            EditorGUILayout.EndVertical();
        }

        /// <summary>
        /// 绘制Preset编辑器
        /// </summary>
        private void DrawPresetEditor()
        {
            if (targetPresetProperty.objectReferenceValue == null)
                return;

            EditorGUILayout.Space();
            showPresetEditor = EditorGUILayout.Foldout(showPresetEditor, "Preset设置", true);

            if (showPresetEditor)
            {
                // 创建或更新缓存的Preset编辑器
                if (cachedPresetEditor == null || cachedPresetEditor.target != targetPresetProperty.objectReferenceValue)
                {
                    if (cachedPresetEditor != null)
                        DestroyImmediate(cachedPresetEditor);

                    cachedPresetEditor = CreateEditor(targetPresetProperty.objectReferenceValue);
                }

                if (cachedPresetEditor != null)
                {
                    EditorGUILayout.BeginVertical("box");
                    cachedPresetEditor.OnInspectorGUI();
                    EditorGUILayout.EndVertical();
                }
            }
        }

        /// <summary>
        /// 绘制高级选项
        /// </summary>
        private void DrawAdvancedOptions()
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("高级选项", EditorStyles.boldLabel);

            EditorGUILayout.PropertyField(enableDetailedLoggingProperty, 
                new GUIContent("详细日志", "在控制台输出详细的处理日志"));
            
            EditorGUILayout.PropertyField(preserveUserDataProperty, 
                new GUIContent("保留UserData", "应用Preset时保留原始的userData"));
        }

        /// <summary>
        /// 绘制操作按钮
        /// </summary>
        private void DrawActionButtons()
        {
            EditorGUILayout.Space();
            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("测试处理器"))
            {
                TestProcessor();
            }

            if (GUILayout.Button("重新导入"))
            {
                var processor = target as PresetProcessor;
                Debug.Log("重新导入功能需要与AssetPipeline框架集成");
            }

            EditorGUILayout.EndHorizontal();
        }

        #endregion

        #region 测试功能

        /// <summary>
        /// 测试处理器功能
        /// </summary>
        private void TestProcessor()
        {
            var processor = target as PresetProcessor;
            if (processor == null)
                return;

            Debug.Log($"=== 测试处理器: {processor.name} ===");
            Debug.Log($"处理模式: {processor.Mode}");
            Debug.Log($"生产环境灵活性配置:");
            Debug.Log($"  - Mip Maps灵活性: {processor.AllowMipMapsFlexibility}");
            Debug.Log($"  - Read/Write灵活性: {processor.AllowReadWriteFlexibility}");
            Debug.Log($"  - 平台纹理尺寸灵活性: {processor.AllowPlatformTextureSizeFlexibility}");
            Debug.Log($"  - 平台纹理格式灵活性: {processor.AllowPlatformTextureFormatFlexibility}");

            if (processor.TargetPreset != null)
            {
                Debug.Log($"Preset类型: {processor.GetPresetTypeName()}");
                Debug.Log($"Preset属性数量: {processor.TargetPreset.PropertyModifications.Length}");

                var flexibleCount = 0;
                if (processor.AllowMipMapsFlexibility) flexibleCount++;
                if (processor.AllowReadWriteFlexibility) flexibleCount++;
                if (processor.AllowPlatformTextureSizeFlexibility) flexibleCount++;
                if (processor.AllowPlatformTextureFormatFlexibility) flexibleCount++;

                Debug.Log($"灵活性配置: {flexibleCount}/4 项允许灵活调整");
            }
        }

        #endregion
    }
}
