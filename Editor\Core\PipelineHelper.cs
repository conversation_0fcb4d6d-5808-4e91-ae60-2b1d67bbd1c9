using System;
using System.Collections.Generic;
using System.Linq;
using AssetPipeline.Config;
using AssetPipeline.Processors;
using Logger = AssetPipeline.Core.Logger;

namespace AssetPipeline.Core
{
    public static class PipelineHelper
    {
        /// <summary>
        /// 批量处理器执行方法
        /// 接收一批资产路径，内部完成处理器的获取、安全执行、异常捕获和结果聚合。
        /// </summary>
        /// <typeparam name="TProcessor">处理器接口类型, 如 IImportProcessor</typeparam>
        /// <param name="assetPaths">需要处理的一批资产路径</param>
        /// <param name="action">对每个匹配的处理器执行的操作。上下文通过闭包捕获。</param>
        /// <returns>一个字典，包含每个产生了结果的资产路径及其对应的结果列表。</returns>
        public static Dictionary<string, List<CheckResult>> ExecuteProcessors<TProcessor>(
            IEnumerable<string> assetPaths,
            Func<TProcessor, string, IEnumerable<CheckResult>> action)
            where TProcessor : class, IProcessor
        {
            var resultsByPath = new Dictionary<string, List<CheckResult>>();
            if (assetPaths == null || !assetPaths.Any())
            {
                return resultsByPath;
            }
            
            var assetTree = AssetPipelineConfig.MainAssetTree;
            if (assetTree == null)
            {
                Logger.Warning(LogModule.Pipeline, "主资源树未配置，无法执行处理器。");
                return resultsByPath;
            }


            foreach (var pathProcessorPair in assetTree.GetMatchingProcessors(assetPaths))
            {
                var assetPath = pathProcessorPair.Key;
                var allProcessors = pathProcessorPair.Value;
                var typedProcessors = allProcessors.OfType<TProcessor>();
                var pathResults = new List<CheckResult>();

                foreach (var processor in typedProcessors)
                {
                    try
                    {
                        var singleProcessorResults = action(processor, assetPath);
                        if (singleProcessorResults != null)
                        {
                            pathResults.AddRange(singleProcessorResults.Select(r => r.WithProcessor(processor)));
                        }
                    }
                    catch (Exception e)
                    {
                        var processorName = (processor as AssetProcessor)?.DisplayName ?? processor.GetType().Name;
                        var errorMsg = $"处理器 {processorName} 在对 {assetPath} 执行时发生未捕获异常: {e.Message}";
                        Logger.Error(LogModule.Pipeline, $"{errorMsg}\n{e.StackTrace}");
                        pathResults.Add(CheckResult.Error(errorMsg, e.ToString())
                            .WithAssetPath(assetPath)
                            .WithProcessor(processor));
                    }
                }

                if (pathResults.Any())
                {
                    resultsByPath[assetPath] = pathResults;
                }
            }
            
            return resultsByPath;
        }

        /// <summary>
        /// 批量处理器执行 - 支持自定义错误处理
        /// </summary>
        public static Dictionary<string, List<CheckResult>> ExecuteProcessorsWithErrorHandling<TProcessor>(
            IEnumerable<string> assetPaths,
            Func<TProcessor, string, IEnumerable<CheckResult>> action,
            Action<Exception, TProcessor, string> errorHandler = null)
            where TProcessor : class, IProcessor
        {
            var resultsByPath = new Dictionary<string, List<CheckResult>>();
            if (assetPaths == null || !assetPaths.Any())
            {
                return resultsByPath;
            }

            var assetTree = AssetPipelineConfig.MainAssetTree;
            if (assetTree == null)
            {
                Logger.Warning(LogModule.Pipeline, "主资源树未配置，无法执行处理器。");
                return resultsByPath;
            }

            foreach (var pathProcessorPair in assetTree.GetMatchingProcessors(assetPaths))
            {
                var assetPath = pathProcessorPair.Key;
                var allProcessors = pathProcessorPair.Value;
                var typedProcessors = allProcessors.OfType<TProcessor>();
                var pathResults = new List<CheckResult>();

                foreach (var processor in typedProcessors)
                {
                    try
                    {
                        var singleProcessorResults = action(processor, assetPath);
                        if (singleProcessorResults != null)
                        {
                            pathResults.AddRange(singleProcessorResults.Select(r => r.WithProcessor(processor)));
                        }
                    }
                    catch (Exception e)
                    {
                        // 使用自定义错误处理器
                        if (errorHandler != null)
                        {
                            try
                            {
                                errorHandler(e, processor, assetPath);
                            }
                            catch (Exception handlerEx)
                            {
                                Logger.Error(LogModule.Pipeline, $"错误处理器本身发生异常: {handlerEx.Message}");
                            }
                        }

                        // 默认错误处理
                        var processorName = (processor as AssetProcessor)?.DisplayName ?? processor.GetType().Name;
                        var errorMsg = $"处理器 {processorName} 在对 {assetPath} 执行时发生异常";
                        
                        pathResults.Add(CheckResult.Error(errorMsg, e.Message)
                            .WithAssetPath(assetPath)
                            .WithProcessor(processor));
                    }
                }

                if (pathResults.Any())
                {
                    resultsByPath[assetPath] = pathResults;
                }
            }
            
            return resultsByPath;
        }

        /// <summary>
        /// 执行单个资产的处理器 - 便利方法
        /// </summary>
        /// <typeparam name="TProcessor">处理器接口类型</typeparam>
        /// <param name="assetPath">资产路径</param>
        /// <param name="action">处理器执行动作</param>
        /// <returns>执行结果列表</returns>
        public static List<CheckResult> ExecuteProcessors<TProcessor>(
            string assetPath,
            Func<TProcessor, string, IEnumerable<CheckResult>> action)
            where TProcessor : class, IProcessor
        {
            if (string.IsNullOrEmpty(assetPath))
                return new List<CheckResult>();

            var resultsByPath = ExecuteProcessors<TProcessor>(new[] { assetPath }, action);
            return resultsByPath.TryGetValue(assetPath, out var results) ? results : new List<CheckResult>();
        }

        /// <summary>
        /// 执行单个资产的处理器 - 支持无路径参数的动作
        /// </summary>
        /// <typeparam name="TProcessor">处理器接口类型</typeparam>
        /// <param name="assetPath">资产路径</param>
        /// <param name="action">处理器执行动作（不需要路径参数）</param>
        /// <returns>执行结果列表</returns>
        public static List<CheckResult> ExecuteProcessors<TProcessor>(
            string assetPath,
            Func<TProcessor, IEnumerable<CheckResult>> action)
            where TProcessor : class, IProcessor
        {
            return ExecuteProcessors<TProcessor>(assetPath, (processor, path) => action(processor));
        }
    }
}