using System;
using AssetPipeline.Core;
using UnityEngine;

namespace AssetPipeline.Processors
{
    /// <summary>
    /// 处理器统计功能
    /// </summary>
    public class ProcessorStatistics
    {
        #region 统计数据

        private int _processedCount = 0;
        private int _successCount = 0;
        private int _errorCount = 0;
        private int _warningCount = 0;
        private int _infoCount = 0;
        
        private DateTime _startTime;
        private DateTime _lastProcessTime;

        #endregion

        #region 属性访问

        /// <summary>
        /// 总处理次数
        /// </summary>
        public int ProcessedCount => _processedCount;

        /// <summary>
        /// 成功次数
        /// </summary>
        public int SuccessCount => _successCount;

        /// <summary>
        /// 错误次数
        /// </summary>
        public int ErrorCount => _errorCount;

        /// <summary>
        /// 警告次数
        /// </summary>
        public int WarningCount => _warningCount;

        /// <summary>
        /// 信息次数
        /// </summary>
        public int InfoCount => _infoCount;

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime => _startTime;

        /// <summary>
        /// 最后处理时间
        /// </summary>
        public DateTime LastProcessTime => _lastProcessTime;

        /// <summary>
        /// 总运行时间
        /// </summary>
        public TimeSpan TotalRunTime => DateTime.Now - _startTime;

        /// <summary>
        /// 成功率（百分比）
        /// </summary>
        public float SuccessRate => _processedCount > 0 ? (float)_successCount / _processedCount * 100 : 0;

        #endregion

        #region 构造函数

        public ProcessorStatistics()
        {
            Reset();
        }

        #endregion

        #region 统计更新

        /// <summary>
        /// 记录处理开始
        /// </summary>
        public void RecordStart()
        {
            if (_startTime == default)
            {
                _startTime = DateTime.Now;
            }
            _lastProcessTime = DateTime.Now;
        }

        /// <summary>
        /// 记录处理结果
        /// </summary>
        public void RecordResult(CheckResult result)
        {
            if (result == null) return;

            _processedCount++;
            _lastProcessTime = DateTime.Now;

            switch (result.resultType)
            {
                case CheckResultType.Success:
                    _successCount++;
                    break;
                case CheckResultType.Error:
                    _errorCount++;
                    break;
                case CheckResultType.Warning:
                    _warningCount++;
                    break;
                case CheckResultType.Info:
                    _infoCount++;
                    break;
            }
        }

        /// <summary>
        /// 记录成功处理
        /// </summary>
        public void RecordSuccess()
        {
            _processedCount++;
            _successCount++;
            _lastProcessTime = DateTime.Now;
        }

        /// <summary>
        /// 记录错误
        /// </summary>
        public void RecordError()
        {
            _processedCount++;
            _errorCount++;
            _lastProcessTime = DateTime.Now;
        }

        /// <summary>
        /// 记录警告
        /// </summary>
        public void RecordWarning()
        {
            _processedCount++;
            _warningCount++;
            _lastProcessTime = DateTime.Now;
        }

        /// <summary>
        /// 记录信息
        /// </summary>
        public void RecordInfo()
        {
            _processedCount++;
            _infoCount++;
            _lastProcessTime = DateTime.Now;
        }

        #endregion

        #region 统计重置

        /// <summary>
        /// 重置所有统计数据
        /// </summary>
        public void Reset()
        {
            _processedCount = 0;
            _successCount = 0;
            _errorCount = 0;
            _warningCount = 0;
            _infoCount = 0;
            _startTime = DateTime.Now;
            _lastProcessTime = DateTime.Now;
        }

        #endregion

        #region 统计报告

        /// <summary>
        /// 获取简要统计信息
        /// </summary>
        public string GetSummary()
        {
            if (_processedCount == 0)
                return "未处理任何资源";

            return $"处理: {_processedCount}, 成功: {_successCount}, 错误: {_errorCount}, 警告: {_warningCount}, 成功率: {SuccessRate:F1}%";
        }

        /// <summary>
        /// 获取详细统计信息
        /// </summary>
        public string GetDetailedReport()
        {
            var report = new System.Text.StringBuilder();
            report.AppendLine($"=== 处理器统计报告 ===");
            report.AppendLine($"总处理次数: {_processedCount}");
            report.AppendLine($"成功次数: {_successCount}");
            report.AppendLine($"错误次数: {_errorCount}");
            report.AppendLine($"警告次数: {_warningCount}");
            report.AppendLine($"信息次数: {_infoCount}");
            report.AppendLine($"成功率: {SuccessRate:F2}%");
            report.AppendLine($"开始时间: {_startTime:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"最后处理: {_lastProcessTime:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"总运行时间: {TotalRunTime}");

            return report.ToString();
        }

        #endregion

        #region 扩展统计

        /// <summary>
        /// 合并其他统计数据
        /// </summary>
        public void Merge(ProcessorStatistics other)
        {
            if (other == null) return;

            _processedCount += other._processedCount;
            _successCount += other._successCount;
            _errorCount += other._errorCount;
            _warningCount += other._warningCount;
            _infoCount += other._infoCount;

            // 保留更早的开始时间
            if (other._startTime < _startTime)
                _startTime = other._startTime;

            // 保留更晚的最后处理时间
            if (other._lastProcessTime > _lastProcessTime)
                _lastProcessTime = other._lastProcessTime;
        }

        /// <summary>
        /// 检查是否有错误
        /// </summary>
        public bool HasErrors => _errorCount > 0;

        /// <summary>
        /// 检查是否有警告
        /// </summary>
        public bool HasWarnings => _warningCount > 0;

        /// <summary>
        /// 检查是否全部成功
        /// </summary>
        public bool IsAllSuccess => _processedCount > 0 && _errorCount == 0 && _warningCount == 0;

        #endregion

        #region ToString重载

        public override string ToString()
        {
            return GetSummary();
        }

        #endregion
    }
} 