using System;
using System.Collections.Generic;
using System.Linq;
using AssetPipeline.Config;
using AssetPipeline.Processors;
using AssetPipeline.Processors.Fixes;
using UnityEngine;

namespace AssetPipeline.Core
{
    /// <summary>
    /// 检查结果类型
    /// </summary>
    public enum CheckResultType
    {
        Unknown = -1, // 未定义（通常用于初始化或错误状态）
        Success = 0,    // 成功
        Info = 1,       // 信息
        Warning = 2,    // 警告（不阻断）
        Error = 3       // 错误（阻断操作）
    }

    /// <summary>
    /// 处理上下文类型 - 用于确定阻断逻辑
    /// </summary>
    public enum ProcessContext
    {
        Import = 0,     // 导入时
        Save = 1,       // 保存时
        Commit = 2,     // 提交时
        Check = 3       // 主动检查时
    }
    
    /// <summary>
    /// 处理器行为枚举
    /// </summary>
    public enum ProcessorAction
    {
        /// <summary>
        /// 默认行为，处理器仅执行检查。
        /// </summary>
        Default,
        /// <summary>
        /// 指示处理器已经自行完成了Unity的操作（如移动或删除文件）。
        /// </summary>
        OperationPerformed
    }
    
    /// <summary>
    /// 检查结果类，存储资产检查的结果信息
    /// 支持自动修复和错误阻断功能
    /// </summary>
    [Serializable]
    public class CheckResult : IEquatable<CheckResult>
    {
        #region 核心序列化字段

        [SerializeField] public CheckResultType resultType;
        [SerializeField] public string message;
        [SerializeField] public string assetPath;
        [SerializeField] public string processorName;
        [SerializeField] public string detail;
        
        // 时间戳（Ticks格式）
        [SerializeField] private long _timestampTicks;

        #endregion

        #region 运行时字段（非序列化）

        /// <summary>
        /// 检查时间
        /// </summary>
        public DateTime CheckTime => new DateTime(_timestampTicks);
        
        /// <summary>
        /// 自动修复器
        /// </summary>
        [System.NonSerialized]
        public IAutoFix AutoFix;

        /// <summary>
        /// 处理器行为意图
        /// </summary>
        public ProcessorAction Action { get; private set; }

        #endregion

        #region 便捷属性

        public bool IsSuccess => resultType == CheckResultType.Success;
        public bool IsInfo => resultType == CheckResultType.Info;
        public bool IsWarning => resultType == CheckResultType.Warning;
        public bool IsError => resultType == CheckResultType.Error;
        public bool HasAutoFix => AutoFix != null;
        
        /// <summary>
        /// 格式化时间戳
        /// </summary>
        public string FormattedTimestamp => CheckTime.ToString("HH:mm:ss");

        #endregion

        #region 构造函数

        public CheckResult()
        {
            _timestampTicks = DateTime.Now.Ticks;
            Action = ProcessorAction.Default;
        }
        
        public CheckResult(CheckResultType type, string message, string assetPath = "", string processorName = "")
        {
            this.resultType = type;
            this.message = message ?? string.Empty;
            this.assetPath = assetPath ?? string.Empty;
            this.processorName = processorName ?? string.Empty;
            this._timestampTicks = DateTime.Now.Ticks;
            this.Action = ProcessorAction.Default;
        }

        #endregion
        
        #region 静态工厂方法
        
        /// <summary>
        /// 创建成功结果
        /// </summary>
        public static CheckResult Success(string message = "检查通过")
        {
            return new CheckResult(CheckResultType.Success, message);
        }
        
        /// <summary>
        /// 创建跳过结果（作为信息类型）
        /// </summary>
        public static CheckResult Skip(string message = "跳过处理")
        {
            return new CheckResult(CheckResultType.Info, message);
        }
        
        /// <summary>
        /// 创建信息结果
        /// </summary>
        public static CheckResult Info(string message)
        {
            return new CheckResult(CheckResultType.Info, message);
        }
        
        /// <summary>
        /// 创建警告结果（带详细信息）
        /// </summary>
        public static CheckResult Warning(string message, string detail = null)
        {
            var result = new CheckResult(CheckResultType.Warning, message);
            if (!string.IsNullOrEmpty(detail))
                result.detail = detail;
            return result;
        }
        
        /// <summary>
        /// 创建带自动修复的警告结果
        /// </summary>
        public static CheckResult WarningWithFix(string message, IAutoFix autoFix)
        {
            var result = new CheckResult(CheckResultType.Warning, message)
            {
                AutoFix = autoFix
            };
            return result;
        }
        
        /// <summary>
        /// 创建错误结果
        /// </summary>
        public static CheckResult Error(string message)
        {
            return new CheckResult(CheckResultType.Error, message);
        }
        
        /// <summary>
        /// 创建带详细信息的错误结果
        /// </summary>
        public static CheckResult Error(string message, string detail)
        {
            var result = new CheckResult(CheckResultType.Error, message);
            if (!string.IsNullOrEmpty(detail))
                result.detail = detail;
            return result;
        }
        
        #endregion
        
        #region 流式API
        
        /// <summary>
        /// 设置资产路径
        /// </summary>
        public CheckResult WithAssetPath(string path)
        {
            assetPath = path ?? string.Empty;
            return this;
        }
        
        /// <summary>
        /// 设置处理器名称
        /// </summary>
        public CheckResult WithProcessor(IProcessor processor)
        {
            if (processor is AssetProcessor assetProcessor)
                processorName = assetProcessor.DisplayName ?? string.Empty;
            else
                processorName = processor?.GetType().Name ?? string.Empty;
            return this;
        }
        
        /// <summary>
        /// 设置详细信息
        /// </summary>
        public CheckResult WithDetail(string details)
        {
            detail = details ?? string.Empty;
            return this;
        }
        
        /// <summary>
        /// 设置自动修复功能
        /// </summary>
        public CheckResult WithAutoFix(IAutoFix autoFix)
        {
            AutoFix = autoFix;
            return this;
        }
        
        /// <summary>
        /// 设置处理器行为
        /// </summary>
        public CheckResult WithAction(ProcessorAction processorAction)
        {
            Action = processorAction;
            return this;
        }

        /// <summary>
        /// 兼容性方法
        /// </summary>
        public CheckResult WithFix(IAutoFix fix)
        {
            return WithAutoFix(fix);
        }

        #endregion

        #region 自动修复功能

        /// <summary>
        /// 尝试自动修复问题
        /// </summary>
        public bool TryAutoFix()
        {
            if (AutoFix == null || string.IsNullOrEmpty(assetPath))
                return false;

            try
            {
                if (AutoFix.CanFix(assetPath))
                {
                    AutoFix.DoFix(assetPath);
                    return true;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[CheckResult] 自动修复失败: {ex.Message}");
            }
            
            return false;
        }

        /// <summary>
        /// 获取自动修复描述
        /// </summary>
        public string GetAutoFixDescription()
        {
            return AutoFix?.GetDescription() ?? "无修复建议";
        }

        #endregion

        #region 显示方法

        /// <summary>
        /// 获取显示用的消息（带时间戳）
        /// </summary>
        public string GetDisplayMessage()
        {
            return $"[{FormattedTimestamp}] {message}";
        }
        
        /// <summary>
        /// 获取完整描述信息 - 优化版本，减少List分配
        /// </summary>
        public string GetFullDescription()
        {
            var result = GetDisplayMessage();

            if (!string.IsNullOrEmpty(assetPath))
                result += $"\n文件: {assetPath}";

            if (!string.IsNullOrEmpty(processorName))
                result += $"\n处理器: {processorName}";

            if (!string.IsNullOrEmpty(detail))
                result += $"\n详情: {detail}";

            if (HasAutoFix)
                result += $"\n修复: {GetAutoFixDescription()}";

            return result;
        }

        #endregion

        #region 相等性和哈希

        /// <summary>
        /// 获取用于去重的唯一键
        /// </summary>
        public string GetKey()
        {
            return $"{assetPath}|{message}|{processorName}".ToLowerInvariant();
        }

        public bool Equals(CheckResult other)
        {
            return other != null && GetKey() == other.GetKey();
        }

        public override bool Equals(object obj)
        {
            return Equals(obj as CheckResult);
        }

        public override int GetHashCode()
        {
            return GetKey().GetHashCode();
        }

        #endregion

        #region 阻断逻辑

        /// <summary>
        /// 判断在指定上下文中是否应该阻断操作
        /// </summary>
        public bool ShouldBlock(ProcessContext context)
        {
            // 只有错误类型才可能阻断
            if (resultType != CheckResultType.Error && resultType != CheckResultType.Warning)
                return false;

            switch (context)
            {
                case ProcessContext.Import:
                    return resultType == CheckResultType.Error || 
                           (resultType == CheckResultType.Warning && AssetPipelineSettings.BlockWarningsOnImport);
                case ProcessContext.Save:
                    return resultType == CheckResultType.Error;
                case ProcessContext.Commit:
                    return resultType == CheckResultType.Error || 
                           (resultType == CheckResultType.Warning && AssetPipelineSettings.BlockWarningsOnCommit);
                case ProcessContext.Check:
                    return false; // 主动检查不阻断
                default:
                    return false;
            }
        }

        /// <summary>
        /// 获取阻断原因说明
        /// </summary>
        public string GetBlockReason(ProcessContext context)
        {
            if (!ShouldBlock(context))
                return string.Empty;

            var contextName = GetContextDisplayName(context);
            var severityName = resultType == CheckResultType.Error ? "错误" : "警告";
            
            return $"{contextName}被{severityName}阻断: {message}";
        }

        private string GetContextDisplayName(ProcessContext context)
        {
            switch (context)
            {
                case ProcessContext.Import:
                    return "资源导入";
                case ProcessContext.Save:
                    return "文件保存";
                case ProcessContext.Commit:
                    return "代码提交";
                case ProcessContext.Check:
                    return "主动检查";
                default:
                    return "操作";
            }
        }

        #endregion

        #region ToString重写

        public override string ToString()
        {
            return $"[{resultType}] {message} ({assetPath})";
        }

        #endregion
    }

    /// <summary>
    /// 检查结果统计信息
    /// </summary>
    public class CheckResultStatistics
    {
        public int TotalCount;
        public int SuccessCount;
        public int ErrorCount;
        public int WarningCount;
        public int InfoCount;

        public override string ToString()
        {
            return $"总计:{TotalCount}, 成功:{SuccessCount}, 错误:{ErrorCount}, 警告:{WarningCount}, 信息:{InfoCount}";
        }
    }
} 