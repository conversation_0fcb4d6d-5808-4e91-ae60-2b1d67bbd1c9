using System.Linq;
using UnityEngine;
using UnityEditor;
using AssetPipeline.Processors;

namespace AssetPipeline.UI
{
    /// <summary>
    /// 属性锁定功能演示和测试工具
    /// </summary>
    public class PropertyLockingDemo : EditorWindow
    {
        #region 窗口管理

        [MenuItem("Tools/Asset Pipeline/Property Locking Demo")]
        public static void ShowWindow()
        {
            var window = GetWindow<PropertyLockingDemo>("属性锁定演示");
            window.minSize = new Vector2(400, 300);
        }

        #endregion

        #region 界面状态

        private Vector2 scrollPosition;
        private AssetImporter selectedImporter;
        private string[] availableAssets;
        private int selectedAssetIndex = 0;

        #endregion

        #region Unity生命周期

        void OnEnable()
        {
            RefreshAssetList();
        }

        void OnGUI()
        {
            DrawHeader();
            DrawAssetSelection();
            DrawPropertyInfo();
            DrawDemoActions();
        }

        #endregion

        #region 界面绘制

        /// <summary>
        /// 绘制头部信息
        /// </summary>
        private void DrawHeader()
        {
            EditorGUILayout.BeginVertical("box");
            
            EditorGUILayout.LabelField("属性锁定功能演示", EditorStyles.boldLabel);
            EditorGUILayout.LabelField("此工具用于演示和测试简化版属性锁定界面的功能");
            
            EditorGUILayout.Space();
            
            // 功能状态
            var inspectorEnhanced = InspectorEnhancer.IsEnhancementActive;
            var lockingEnabled = SafeInspectorManager.IsLockingEnabled;
            
            EditorGUILayout.LabelField("功能状态:", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Inspector增强: {(inspectorEnhanced ? "✅ 已激活" : "❌ 未激活")}");
            EditorGUILayout.LabelField($"属性锁定: {(lockingEnabled ? "✅ 已启用" : "❌ 未启用")}");
            
            if (!inspectorEnhanced || !lockingEnabled)
            {
                EditorGUILayout.HelpBox("请先激活Inspector增强功能", MessageType.Warning);
                if (GUILayout.Button("激活Inspector增强"))
                {
                    InspectorEnhancer.ActivateEnhancement();
                }
            }
            
            EditorGUILayout.EndVertical();
        }

        /// <summary>
        /// 绘制资产选择
        /// </summary>
        private void DrawAssetSelection()
        {
            EditorGUILayout.BeginVertical("box");
            
            EditorGUILayout.LabelField("选择测试资产", EditorStyles.boldLabel);
            
            if (availableAssets != null && availableAssets.Length > 0)
            {
                var newIndex = EditorGUILayout.Popup("资产:", selectedAssetIndex, availableAssets);
                if (newIndex != selectedAssetIndex)
                {
                    selectedAssetIndex = newIndex;
                    UpdateSelectedImporter();
                }
            }
            else
            {
                EditorGUILayout.LabelField("未找到可用的资产");
            }
            
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("刷新资产列表"))
            {
                RefreshAssetList();
            }
            
            if (GUILayout.Button("打开属性锁定界面"))
            {
                LockableImporterInspector.ShowWindow();
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.EndVertical();
        }

        /// <summary>
        /// 绘制属性信息
        /// </summary>
        private void DrawPropertyInfo()
        {
            if (selectedImporter == null) return;
            
            EditorGUILayout.BeginVertical("box");
            
            EditorGUILayout.LabelField("当前资产信息", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"类型: {selectedImporter.GetType().Name}");
            EditorGUILayout.LabelField($"路径: {selectedImporter.assetPath}");
            
            // 显示可用属性
            var importerType = selectedImporter.GetType().Name;
            var properties = PropertySelectorSystem.GetPropertiesForImporterType(importerType);
            
            EditorGUILayout.LabelField($"可锁定属性数量: {properties.Count}");
            
            // 显示锁定状态
            var lockedProperties = SafeInspectorManager.GetLockedProperties(selectedImporter.assetPath, "inspector");
            EditorGUILayout.LabelField($"已锁定属性数量: {lockedProperties.Count}");
            
            if (lockedProperties.Count > 0)
            {
                EditorGUILayout.LabelField("已锁定的属性:", EditorStyles.boldLabel);
                scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(100));
                
                foreach (var propertyPath in lockedProperties)
                {
                    var propertyInfo = properties.FirstOrDefault(p => p.propertyPath == propertyPath);
                    var displayName = propertyInfo?.displayName ?? propertyPath;
                    EditorGUILayout.LabelField($"🔒 {displayName}");
                }
                
                EditorGUILayout.EndScrollView();
            }
            
            EditorGUILayout.EndVertical();
        }

        /// <summary>
        /// 绘制演示操作
        /// </summary>
        private void DrawDemoActions()
        {
            if (selectedImporter == null) return;
            
            EditorGUILayout.BeginVertical("box");
            
            EditorGUILayout.LabelField("演示操作", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("锁定常用属性"))
            {
                LockCommonProperties();
            }
            
            if (GUILayout.Button("锁定平台设置"))
            {
                LockPlatformSettings();
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("解锁所有属性"))
            {
                SafeInspectorManager.ClearAssetLocks(selectedImporter.assetPath, "inspector");
            }
            
            if (GUILayout.Button("验证锁定属性"))
            {
                ValidateLockedProperties();
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            if (GUILayout.Button("在Project窗口中选择此资产"))
            {
                var asset = AssetDatabase.LoadAssetAtPath<Object>(selectedImporter.assetPath);
                Selection.activeObject = asset;
                EditorGUIUtility.PingObject(asset);
            }
            
            EditorGUILayout.EndVertical();
        }

        #endregion

        #region 资产管理

        /// <summary>
        /// 刷新资产列表
        /// </summary>
        private void RefreshAssetList()
        {
            var assetPaths = AssetDatabase.GetAllAssetPaths()
                .Where(path => path.StartsWith("Assets/"))
                .Where(path => AssetImporter.GetAtPath(path) != null)
                .Where(path => 
                {
                    var importer = AssetImporter.GetAtPath(path);
                    return importer is TextureImporter || importer is ModelImporter || importer is AudioImporter;
                })
                .Take(50) // 限制数量避免性能问题
                .ToArray();
            
            availableAssets = assetPaths.Select(path => 
            {
                var importer = AssetImporter.GetAtPath(path);
                return $"{importer.GetType().Name}: {path}";
            }).ToArray();
            
            if (availableAssets.Length > 0)
            {
                selectedAssetIndex = 0;
                UpdateSelectedImporter();
            }
        }

        /// <summary>
        /// 更新选中的导入器
        /// </summary>
        private void UpdateSelectedImporter()
        {
            if (availableAssets != null && selectedAssetIndex < availableAssets.Length)
            {
                var displayName = availableAssets[selectedAssetIndex];
                var assetPath = displayName.Substring(displayName.IndexOf(": ") + 2);
                selectedImporter = AssetImporter.GetAtPath(assetPath);
            }
        }

        #endregion

        #region 演示操作

        /// <summary>
        /// 锁定常用属性
        /// </summary>
        private void LockCommonProperties()
        {
            if (selectedImporter == null) return;
            
            var importerType = selectedImporter.GetType().Name;
            var properties = PropertySelectorSystem.GetPropertiesForImporterType(importerType);
            
            // 锁定基础和性能相关的属性
            var commonProperties = properties.Where(p => 
                p.propertyType == PropertySelectorSystem.PropertyType.Basic ||
                p.propertyType == PropertySelectorSystem.PropertyType.Performance
            ).Take(5).ToList();
            
            foreach (var property in commonProperties)
            {
                // 保存当前值
                SaveCurrentPropertyValue(property.propertyPath);
                
                // 锁定属性
                SafeInspectorManager.LockProperty(selectedImporter.assetPath, property.propertyPath, "inspector");
            }
            
            Debug.Log($"[PropertyLockingDemo] 已锁定 {commonProperties.Count} 个常用属性");
        }

        /// <summary>
        /// 锁定平台设置
        /// </summary>
        private void LockPlatformSettings()
        {
            if (selectedImporter == null || !(selectedImporter is TextureImporter)) return;
            
            var properties = PropertySelectorSystem.GetPropertiesForImporterType("TextureImporter");
            
            // 锁定平台相关的属性
            var platformProperties = properties.Where(p => 
                p.propertyType == PropertySelectorSystem.PropertyType.Platform &&
                (p.propertyPath.Contains("m_MaxTextureSize") || p.propertyPath.Contains("m_TextureFormat"))
            ).ToList();
            
            foreach (var property in platformProperties)
            {
                // 保存当前值
                SaveCurrentPropertyValue(property.propertyPath);
                
                // 锁定属性
                SafeInspectorManager.LockProperty(selectedImporter.assetPath, property.propertyPath, "inspector");
            }
            
            Debug.Log($"[PropertyLockingDemo] 已锁定 {platformProperties.Count} 个平台设置属性");
        }

        /// <summary>
        /// 验证锁定属性
        /// </summary>
        private void ValidateLockedProperties()
        {
            if (selectedImporter == null) return;
            
            var restored = SafeInspectorManager.ValidateAndRestoreLockedProperties(selectedImporter, "inspector");
            
            if (restored)
            {
                Debug.Log("[PropertyLockingDemo] 已恢复被修改的锁定属性");
            }
            else
            {
                Debug.Log("[PropertyLockingDemo] 所有锁定属性值正常");
            }
        }

        /// <summary>
        /// 保存当前属性值
        /// </summary>
        private void SaveCurrentPropertyValue(string propertyPath)
        {
            if (selectedImporter == null) return;
            
            var serializedImporter = new SerializedObject(selectedImporter);
            var property = serializedImporter.FindProperty(propertyPath);
            
            if (property != null)
            {
                var currentValue = GetPropertyValueAsString(property);
                SafeInspectorManager.SaveExpectedPropertyValue(
                    selectedImporter.assetPath, 
                    propertyPath, 
                    currentValue, 
                    "inspector"
                );
            }
        }

        /// <summary>
        /// 获取属性值字符串
        /// </summary>
        private string GetPropertyValueAsString(SerializedProperty property)
        {
            switch (property.propertyType)
            {
                case SerializedPropertyType.Boolean:
                    return property.boolValue ? "1" : "0";
                case SerializedPropertyType.Integer:
                    return property.intValue.ToString();
                case SerializedPropertyType.Float:
                    return property.floatValue.ToString();
                case SerializedPropertyType.String:
                    return property.stringValue;
                case SerializedPropertyType.Enum:
                    return property.enumValueIndex.ToString();
                default:
                    return property.stringValue ?? "";
            }
        }

        #endregion
    }
}
