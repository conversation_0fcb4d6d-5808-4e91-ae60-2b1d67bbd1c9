using System;
using System.Collections.Generic;
using System.Linq;
using AssetPipeline.Database.Models;
using L10.Editor.AssetPipeline.Sqlite;
using Logger = AssetPipeline.Core.Logger;

namespace AssetPipeline.Database
{
    internal class SqliteDatabaseService : IDatabaseService
    {
        private SQLiteConnection _connection;
        private readonly object _dbLock = new object();
        private readonly Dictionary<Type, object> _repositories = new Dictionary<Type, object>();

        public void Initialize(string databasePath)
        {
            try
            {
                lock (_dbLock)
                {
                    if (_connection != null)
                    {
                        Logger.Warning(Core.LogModule.Database, "Database service already initialized.");
                        return;
                    }

                    _connection = new SQLiteConnection(databasePath, SQLiteOpenFlags.ReadWrite | SQLiteOpenFlags.Create | SQLiteOpenFlags.SharedCache);
                    Logger.Info(Core.LogModule.Database, $"Database connection opened at {databasePath}");
                }
            }
            catch (Exception e)
            {
                Logger.LogException(Core.LogModule.Database, e, "Failed to initialize database service.");
                _connection = null;
            }
        }

        public void Shutdown()
        {
            lock (_dbLock)
            {
                _connection?.Close();
                _connection = null;
                _repositories.Clear();
                Logger.Info(Core.LogModule.Database, "Database connection closed.");
            }
        }

        public IRepository<T> GetRepository<T>() where T : class, new()
        {
            lock (_dbLock)
            {
                if (_repositories.TryGetValue(typeof(T), out var repo))
                {
                    return (IRepository<T>)repo;
                }

                var newRepo = new SqliteRepository<T>(_connection, _dbLock);
                _repositories[typeof(T)] = newRepo;
                return newRepo;
            }
        }
        
        public void CreateOrUpdateTable<T>() where T : new()
        {
            lock(_dbLock)
            {
                _connection.CreateTable<T>();
            }
        }

        public void BeginTransaction()
        {
            lock (_dbLock)
            {
                _connection.BeginTransaction();
            }
        }

        public void Commit()
        {
            lock (_dbLock)
            {
                _connection.Commit();
            }
        }

        public void Rollback()
        {
            lock (_dbLock)
            {
                _connection.Rollback();
            }
        }

        public void RunInTransaction(Action action)
        {
            lock (_dbLock)
            {
                _connection.RunInTransaction(action);
            }
        }

        public List<T> Query<T>(string sql, params object[] args) where T : new()
        {
            lock (_dbLock)
            {
                return _connection.Query<T>(sql, args);
            }
        }

        public int Execute(string sql, params object[] args)
        {
            lock (_dbLock)
            {
                return _connection.Execute(sql, args);
            }
        }
        
        public void Dispose()
        {
            Shutdown();
        }
    }
} 