using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using AssetPipeline.Config;
using UnityEngine;
using UnityEditor;
using AssetPipeline.Core;
using AssetPipeline.Processors;

namespace AssetPipeline.Core
{
    /// <summary>
    /// 资源筛选器，负责根据条件筛选资源并管理相应的处理器
    /// 支持按类型、文件名、路径等多种方式过滤资源
    /// </summary>
    public class AssetFilter : ScriptableObject
    {
        #region 基础配置

        [SerializeField] private string guid = System.Guid.NewGuid().ToString();
        public string GUID => guid;
        
        [SerializeField] private string displayName = "New Filter";
        public string DisplayName { get => displayName; set => displayName = value; }

        [SerializeField] private bool enabled = true;
        public bool Enabled { get => enabled; set => enabled = value; }
        
        [SerializeField] private int priority = 0;
        public int Priority { get => priority; set => priority = value; }
        
        // 层级概念，用于筛选器优先级控制
        [SerializeField] private int layer = 0;
        public int Layer { get => layer; set => layer = value; }

        #endregion

        #region 筛选规则配置

        [Header("筛选规则")]
        [SerializeField] private AssetTypeFlag assetTypes = AssetTypeFlag.All;
        public AssetTypeFlag AssetTypes => assetTypes;
        
        [SerializeField] private PathMatcher filePattern = new PathMatcher();
        public PathMatcher FilePattern => filePattern;
        
        [SerializeField] private List<PathMatcher> fileExclusions = new List<PathMatcher>();
        public List<PathMatcher> FileExclusions => fileExclusions;
        
        [SerializeField] private List<string> specificPaths = new List<string>();
        public List<string> SpecificPaths => specificPaths;
        
        [SerializeField] private List<string> customExtensions = new List<string>();
        public List<string> CustomExtensions => customExtensions;

        #endregion

        #region 处理器管理

        [Header("处理器列表")]
        [SerializeField] private List<AssetProcessor> processors = new List<AssetProcessor>();
        public List<AssetProcessor> Processors => processors;
        
        // 缓存已排序的处理器，避免重复排序
        [NonSerialized] private List<AssetProcessor> _cachedSortedProcessors;
        [NonSerialized] private bool _processorCacheDirty = true;

        #endregion

        #region 核心匹配逻辑

        /// <summary>
        /// 检查资源是否匹配此筛选器
        /// </summary>
        public bool IsMatch(string assetPath)
        {
            if (!enabled || string.IsNullOrEmpty(assetPath)) return false;

            return IsAssetTypeMatch(assetPath) && 
                   IsFileNameMatch(assetPath) && 
                   IsSpecificPathMatch(assetPath);
        }

        /// <summary>
        /// 检查资产类型是否匹配 - Other作为自定义扩展名开关
        ///
        /// 匹配策略：
        /// 1. All类型：匹配所有文件
        /// 2. None类型：不匹配任何文件
        /// 3. 标准类型匹配：检查预定义类型
        /// 4. Other开关：勾选Other才启用自定义扩展名匹配
        /// </summary>
        private bool IsAssetTypeMatch(string assetPath)
        {
            // 特殊类型处理 - 早期退出优化
            if (assetTypes == AssetTypeFlag.All) return true;
            if (assetTypes == AssetTypeFlag.None) return false;

            var extension = Path.GetExtension(assetPath);
            if (string.IsNullOrEmpty(extension)) return false;

            var actualAssetType = AssetType.GetAssetTypeFlag(assetPath);

            // 检查标准类型匹配（排除Other）
            var standardTypes = assetTypes & ~AssetTypeFlag.Other;
            if (standardTypes != AssetTypeFlag.None && (actualAssetType & standardTypes) != 0)
            {
                return true; // 已匹配标准类型，直接返回，无需检查自定义扩展名
            }

            // 检查Other开关：只有勾选了Other且未匹配标准类型时才检查自定义扩展名
            if ((assetTypes & AssetTypeFlag.Other) != 0 && customExtensions?.Count > 0)
            {
                // 优化：直接遍历，避免LINQ Any的开销
                foreach (var ext in customExtensions)
                {
                    if (string.Equals(extension, ext, StringComparison.OrdinalIgnoreCase))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 检查文件名是否匹配
        /// </summary>
        private bool IsFileNameMatch(string assetPath)
        {
            var fileName = Path.GetFileName(assetPath);
            if (string.IsNullOrEmpty(fileName)) return false;

            // 第一步：排除规则优先检查
            if (fileExclusions?.Count > 0)
            {
                foreach (var exclusion in fileExclusions)
                {
                    if (exclusion?.IsMatch(fileName) == true)
                    {
                        return false; // 匹配排除规则，立即拒绝
                    }
                }
            }

            // 第二步：包含规则检查
            if (filePattern != null && !string.IsNullOrEmpty(filePattern.Pattern))
            {
                return filePattern.IsMatch(fileName);
            }

            return true;
        }

        /// <summary>
        /// 检查特定路径匹配
        /// </summary>
        private bool IsSpecificPathMatch(string assetPath)
        {
            // 没有配置特定路径限制时，匹配所有路径
            if (specificPaths?.Count == 0) return true;

            var normalizedAssetPath = assetPath.Replace("\\", "/");

            // 检查是否包含任何一个配置的特定路径
            foreach (var specificPath in specificPaths)
            {
                if (!string.IsNullOrEmpty(specificPath))
                {
                    var normalizedSpecificPath = specificPath.Replace("\\", "/");
                    if (normalizedAssetPath.IndexOf(normalizedSpecificPath, StringComparison.OrdinalIgnoreCase) >= 0)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        #endregion

        #region 处理器获取

        /// <summary>
        /// 将匹配的处理器添加到结果列表中
        /// </summary>
        public void GetMatchingProcessors(string assetPath, List<AssetProcessor> results)
        {
            if (!enabled || !IsMatch(assetPath) || results == null)
                return;
            
            GetSortedValidProcessors(assetPath, results);
        }
        
        /// <summary>
        /// 获取指定类型的匹配处理器
        /// </summary>
        public List<T> GetMatchingProcessors<T>(string assetPath) where T : class, IProcessor
        {
            if (!enabled || !IsMatch(assetPath))
                return new List<T>();

            var allProcessors = new List<AssetProcessor>();
            GetSortedValidProcessors(assetPath, allProcessors);
            return allProcessors.OfType<T>().ToList();
        }
        
        /// <summary>
        /// 获取所有启用的处理器（不检查路径匹配）
        /// </summary>
        public List<AssetProcessor> GetAllEnabledProcessors()
        {
            if (!enabled) return new List<AssetProcessor>();
            
            var results = new List<AssetProcessor>();
            GetSortedValidProcessors(null, results);
            return results;
        }
        
        /// <summary>
        /// 检查是否包含指定类型的处理器
        /// </summary>
        public bool HasProcessor<T>(string assetPath = null) where T : class, IProcessor
        {
            if (!enabled) return false;
            
            if (assetPath != null && !IsMatch(assetPath))
                return false;

            EnsureCacheUpdated();

            for (int i = 0; i < _cachedSortedProcessors.Count; i++)
            {
                var processor = _cachedSortedProcessors[i];
                if (processor is T && (assetPath == null || processor.CanProcess(assetPath)))
                {
                    return true;
                }
            }
            
            return false;
        }

        /// <summary>
        /// 内部统一的处理器获取逻辑, 将结果添加到传入的列表中
        /// </summary>
        private void GetSortedValidProcessors(string assetPath, List<AssetProcessor> results)
        {
            EnsureCacheUpdated();

            // 如果不需要路径检查，直接返回缓存
            if (assetPath == null)
            {
                results.AddRange(_cachedSortedProcessors);
                return;
            }

            for (int i = 0; i < _cachedSortedProcessors.Count; i++)
            {
                var processor = _cachedSortedProcessors[i];
                if (processor.CanProcess(assetPath))
                {
                    results.Add(processor);
                }
            }
        }

        #endregion

        #region 处理器管理方法

        /// <summary>
        /// 添加处理器
        /// </summary>
        public void AddProcessor(AssetProcessor processor)
        {
            if (processor != null && !processors.Contains(processor))
            {
                processors.Add(processor);
                _processorCacheDirty = true;
                EditorUtility.SetDirty(this);
            }
        }
        
        /// <summary>
        /// 移除处理器
        /// </summary>
        public void RemoveProcessor(AssetProcessor processor)
        {
            if (processors.Remove(processor))
            {
                _processorCacheDirty = true;
                EditorUtility.SetDirty(this);
            }
        }
        
        /// <summary>
        /// 移动处理器位置
        /// </summary>
        public void MoveProcessor(int fromIndex, int toIndex)
        {
            if (fromIndex >= 0 && fromIndex < processors.Count && 
                toIndex >= 0 && toIndex < processors.Count && 
                fromIndex != toIndex)
            {
                var processor = processors[fromIndex];
                processors.RemoveAt(fromIndex);
                processors.Insert(toIndex, processor);
                _processorCacheDirty = true;
                EditorUtility.SetDirty(this);
            }
        }

        /// <summary>
        /// 清除所有缓存 - 配置变化时调用
        /// </summary>
        public void ClearCache()
        {
            _cachedSortedProcessors?.Clear();
            _cachedSortedProcessors = null;
            _processorCacheDirty = true;

            // 清理PathMatcher缓存
            filePattern?.ClearCache();
            if (fileExclusions != null)
            {
                foreach (var exclusion in fileExclusions)
                {
                    exclusion?.ClearCache();
                }
            }

            foreach (var processor in processors)
            {
                processor?.ClearCache();
            }
        }

        /// <summary>
        /// 确保处理器缓存是最新的 - 优化版本，避免LINQ开销
        /// </summary>
        private void EnsureCacheUpdated()
        {
            if (_processorCacheDirty || _cachedSortedProcessors == null)
            {
                // 优化：直接遍历，避免LINQ Where和OrderBy的开销
                var validProcessors = new List<AssetProcessor>(processors.Count);

                foreach (var processor in processors)
                {
                    if (processor != null && processor.Enabled)
                    {
                        validProcessors.Add(processor);
                    }
                }

                // 只在有多个处理器时才排序
                if (validProcessors.Count > 1)
                {
                    validProcessors.Sort((a, b) => a.Priority.CompareTo(b.Priority));
                }

                _cachedSortedProcessors = validProcessors;
                _processorCacheDirty = false;

                Logger.Debug(LogModule.Core,
                    $"[AssetFilter] 处理器缓存已更新 - {DisplayName}: {_cachedSortedProcessors.Count}个有效处理器");
            }
        }

        #endregion

        #region 实用方法

        /// <summary>
        /// 复制筛选器
        /// </summary>
        public AssetFilter Clone()
        {
            var clone = CreateInstance<AssetFilter>();
            
            clone.displayName = displayName + " (Copy)";
            clone.enabled = enabled;
            clone.priority = priority;
            clone.layer = layer;
            clone.assetTypes = assetTypes;
            clone.filePattern = filePattern?.Clone();
            clone.fileExclusions = fileExclusions?.Select(p => p?.Clone()).Where(p => p != null).ToList() ?? new List<PathMatcher>();
            clone.specificPaths = new List<string>(specificPaths ?? new List<string>());
            clone.customExtensions = new List<string>(customExtensions ?? new List<string>());
            clone.processors = new List<AssetProcessor>(processors ?? new List<AssetProcessor>());
            clone.guid = System.Guid.NewGuid().ToString();
            
            return clone;
        }

        public override string ToString()
        {
            // 并集关系的字符串表示
            string typesString;

            if (assetTypes == AssetTypeFlag.All)
            {
                typesString = "All";
            }
            else
            {
                var parts = new List<string>();

                // 添加标准类型部分
                parts.Add(assetTypes.ToString());

                // 添加自定义扩展名部分
                if (customExtensions?.Count > 0)
                {
                    parts.Add($"Custom({string.Join(",", customExtensions)})");
                }

                // 用 | 连接表示并集关系
                typesString = string.Join(" | ", parts);
            }

            return $"{displayName} ({typesString})";
        }

        #endregion

        #region Unity生命周期

        void OnValidate()
        {
            bool wasModified = false;
            
            // 清理自定义扩展名
            if (customExtensions != null)
            {
                for (int i = customExtensions.Count - 1; i >= 0; i--)
                {
                    var ext = customExtensions[i];
                    if (string.IsNullOrWhiteSpace(ext))
                    {
                        customExtensions.RemoveAt(i);
                        wasModified = true;
                    }
                    else
                    {
                        var normalized = ext.Trim();
                        if (!normalized.StartsWith("."))
                            normalized = "." + normalized;
                        
                        if (normalized != ext)
                        {
                            customExtensions[i] = normalized;
                            wasModified = true;
                        }
                    }
                }
            }

            // 清理特定路径
            if (specificPaths != null)
            {
                for (int i = specificPaths.Count - 1; i >= 0; i--)
                {
                    var path = specificPaths[i];
                    if (string.IsNullOrWhiteSpace(path))
                    {
                        specificPaths.RemoveAt(i);
                        wasModified = true;
                    }
                    else
                    {
                        var normalized = path.Replace("\\", "/").Trim();
                        if (normalized != path)
                        {
                            specificPaths[i] = normalized;
                            wasModified = true;
                        }
                    }
                }
            }
            
            // 清理空的排除规则
            if (fileExclusions != null)
            {
                for (int i = fileExclusions.Count - 1; i >= 0; i--)
                {
                    if (fileExclusions[i] == null)
                    {
                        fileExclusions.RemoveAt(i);
                        wasModified = true;
                    }
                }
            }
            
            // 清理空的处理器
            if (processors != null)
            {
                for (int i = processors.Count - 1; i >= 0; i--)
                {
                    if (processors[i] == null)
                    {
                        processors.RemoveAt(i);
                        _processorCacheDirty = true;
                        wasModified = true;
                    }
                }
            }
            
            if (wasModified)
            {
                EditorUtility.SetDirty(this);
            }
        }

        #endregion
    }
} 