using System.Collections.Generic;
using AssetPipeline.Core;
using AssetPipeline.Pipelines;
using UnityEditor;
using UnityEngine;

namespace AssetPipeline.Processors
{
    /// <summary>
    /// 导入处理器的便利基类
    /// 提供了IImportProcessor接口所有方法的默认空实现
    /// 继承此类，并只重写关心的导入方法
    /// </summary>
    public abstract class ImportProcessor : AssetProcessor, IImportProcessor
    {
        /// <summary>
        /// 是否仅在首次导入时处理
        /// 默认为false，子类可以重写此属性
        /// </summary>
        public virtual bool OnlyFirstImport => false;
        // --- 预处理 ---
        public virtual IEnumerable<CheckResult> OnPreprocessAsset(AssetImporter importer, ImportContext context) => null;
        public virtual IEnumerable<CheckResult> OnPreprocessTexture(TextureImporter importer, ImportContext context) => null;
        public virtual IEnumerable<CheckResult> OnPreprocessModel(ModelImporter importer, ImportContext context) => null;
        public virtual IEnumerable<CheckResult> OnPreprocessAudio(AudioImporter importer, ImportContext context) => null;

        // --- 后处理 ---
        public virtual IEnumerable<CheckResult> OnPostprocessAsset(AssetImporter importer, ImportContext context) => null;
        public virtual IEnumerable<CheckResult> OnPostprocessTexture(TextureImporter importer, Texture2D texture, ImportContext context) => null;
        public virtual IEnumerable<CheckResult> OnPostprocessModel(ModelImporter importer, GameObject model, ImportContext context) => null;
        public virtual IEnumerable<CheckResult> OnPostprocessAudio(AudioImporter importer, AudioClip clip, ImportContext context) => null;
    }
} 