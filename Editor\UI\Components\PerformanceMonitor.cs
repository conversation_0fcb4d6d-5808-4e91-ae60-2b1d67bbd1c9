using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using AssetPipeline.Core;

namespace AssetPipeline.UI.Components
{
    public static class PerformanceMonitor
    {
        private static readonly Dictionary<string, PerformanceMetric> _metrics = new Dictionary<string, PerformanceMetric>();
        private static readonly Queue<PerformanceSnapshot> _snapshots = new Queue<PerformanceSnapshot>();
        private const int MAX_SNAPSHOTS = 100;
        
        private static double _lastUpdateTime = 0;
        private const double UPDATE_INTERVAL = 1.0; // 1秒更新一次

        public struct PerformanceMetric
        {
            public string name;
            public double value;
            public double minValue;
            public double maxValue;
            public double averageValue;
            public int sampleCount;
            public DateTime lastUpdate;
        }

        public struct PerformanceSnapshot
        {
            public DateTime timestamp;
            public double memoryUsage;
            public double processingTime;
            public int activeProcessors;
            public int queuedAssets;
        }

        /// <summary>
        /// 记录性能指标
        /// </summary>
        public static void RecordMetric(string metricName, double value)
        {
            if (!_metrics.ContainsKey(metricName))
            {
                _metrics[metricName] = new PerformanceMetric
                {
                    name = metricName,
                    value = value,
                    minValue = value,
                    maxValue = value,
                    averageValue = value,
                    sampleCount = 1,
                    lastUpdate = DateTime.Now
                };
            }
            else
            {
                var metric = _metrics[metricName];
                metric.value = value;
                metric.minValue = Math.Min(metric.minValue, value);
                metric.maxValue = Math.Max(metric.maxValue, value);
                metric.averageValue = (metric.averageValue * metric.sampleCount + value) / (metric.sampleCount + 1);
                metric.sampleCount++;
                metric.lastUpdate = DateTime.Now;
                _metrics[metricName] = metric;
            }
        }

        /// <summary>
        /// 更新性能快照
        /// </summary>
        public static void UpdateSnapshot()
        {
            var currentTime = EditorApplication.timeSinceStartup;
            if (currentTime - _lastUpdateTime < UPDATE_INTERVAL)
                return;

            var snapshot = new PerformanceSnapshot
            {
                timestamp = DateTime.Now,
                memoryUsage = GC.GetTotalMemory(false) / (1024.0 * 1024.0), // MB
                processingTime = GetMetricValue("ProcessingTime"),
                activeProcessors = GetActiveProcessorCount(),
                queuedAssets = GetQueuedAssetCount()
            };

            _snapshots.Enqueue(snapshot);
            if (_snapshots.Count > MAX_SNAPSHOTS)
            {
                _snapshots.Dequeue();
            }

            _lastUpdateTime = currentTime;
        }

        /// <summary>
        /// 绘制性能监控面板
        /// </summary>
        public static void DrawMonitorPanel()
        {
            UpdateSnapshot();

            EditorGUILayout.LabelField("📊 性能监控", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            
            // 实时指标
            DrawRealTimeMetrics();
            
            EditorGUILayout.Space();
            
            // 历史趋势
            DrawHistoryTrends();
            
            EditorGUILayout.Space();
            
            // 操作按钮
            DrawMonitorActions();
            
            EditorGUILayout.EndVertical();
        }

        private static void DrawRealTimeMetrics()
        {
            EditorGUILayout.LabelField("实时指标:", EditorStyles.miniLabel);
            
            EditorGUI.indentLevel++;
            
            // 内存使用
            var memoryUsage = GC.GetTotalMemory(false) / (1024.0 * 1024.0);
            var memoryColor = memoryUsage > 500 ? Color.red : (memoryUsage > 200 ? Color.yellow : Color.green);
            DrawColoredMetric("内存使用", $"{memoryUsage:F1} MB", memoryColor);
            
            // 处理时间
            var processingTime = GetMetricValue("ProcessingTime");
            var timeColor = processingTime > 1000 ? Color.red : (processingTime > 500 ? Color.yellow : Color.green);
            DrawColoredMetric("处理时间", $"{processingTime:F1} ms", timeColor);
            
            // 活跃处理器
            var activeProcessors = GetActiveProcessorCount();
            DrawMetric("活跃处理器", activeProcessors.ToString());
            
            // 队列资产
            var queuedAssets = GetQueuedAssetCount();
            DrawMetric("队列资产", queuedAssets.ToString());
            
            EditorGUI.indentLevel--;
        }

        private static void DrawHistoryTrends()
        {
            if (_snapshots.Count < 2)
            {
                EditorGUILayout.LabelField("历史趋势: 数据收集中...", EditorStyles.miniLabel);
                return;
            }

            EditorGUILayout.LabelField("历史趋势:", EditorStyles.miniLabel);
            
            EditorGUI.indentLevel++;
            
            var snapshots = _snapshots.ToArray();
            var latest = snapshots.Last();
            var previous = snapshots[snapshots.Length - 2];
            
            // 内存趋势
            var memoryTrend = latest.memoryUsage - previous.memoryUsage;
            var memoryTrendText = memoryTrend > 0 ? $"↑ +{memoryTrend:F1} MB" : $"↓ {memoryTrend:F1} MB";
            var memoryTrendColor = memoryTrend > 10 ? Color.red : (memoryTrend > 0 ? Color.yellow : Color.green);
            DrawColoredMetric("内存趋势", memoryTrendText, memoryTrendColor);
            
            // 处理时间趋势
            var timeTrend = latest.processingTime - previous.processingTime;
            var timeTrendText = timeTrend > 0 ? $"↑ +{timeTrend:F1} ms" : $"↓ {timeTrend:F1} ms";
            var timeTrendColor = timeTrend > 100 ? Color.red : (timeTrend > 0 ? Color.yellow : Color.green);
            DrawColoredMetric("时间趋势", timeTrendText, timeTrendColor);
            
            EditorGUI.indentLevel--;
        }

        private static void DrawMonitorActions()
        {
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("🔄 刷新", EditorStyles.miniButton, GUILayout.Width(60)))
            {
                _lastUpdateTime = 0; // 强制更新
            }
            
            if (GUILayout.Button("🗑️ 清除", EditorStyles.miniButton, GUILayout.Width(60)))
            {
                ClearMetrics();
            }
            
            if (GUILayout.Button("📊 报告", EditorStyles.miniButton, GUILayout.Width(60)))
            {
                GeneratePerformanceReport();
            }
            
            GUILayout.FlexibleSpace();
            
            EditorGUILayout.EndHorizontal();
        }

        private static void DrawMetric(string name, string value)
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"{name}:", GUILayout.Width(80));
            EditorGUILayout.LabelField(value, EditorStyles.miniLabel);
            EditorGUILayout.EndHorizontal();
        }

        private static void DrawColoredMetric(string name, string value, Color color)
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"{name}:", GUILayout.Width(80));
            
            var oldColor = GUI.color;
            GUI.color = color;
            EditorGUILayout.LabelField(value, EditorStyles.miniLabel);
            GUI.color = oldColor;
            
            EditorGUILayout.EndHorizontal();
        }

        private static double GetMetricValue(string metricName)
        {
            return _metrics.ContainsKey(metricName) ? _metrics[metricName].value : 0.0;
        }

        private static int GetActiveProcessorCount()
        {
            // 这里应该从实际的Pipeline系统获取活跃处理器数量
            // 暂时返回模拟数据
            return UnityEngine.Random.Range(0, 5);
        }

        private static int GetQueuedAssetCount()
        {
            // 这里应该从实际的Pipeline系统获取队列中的资产数量
            // 暂时返回模拟数据
            return UnityEngine.Random.Range(0, 20);
        }

        private static void ClearMetrics()
        {
            _metrics.Clear();
            _snapshots.Clear();
            Debug.Log("[PerformanceMonitor] 性能指标已清除");
        }

        private static void GeneratePerformanceReport()
        {
            try
            {
                var reportPath = $"Assets/Reports/PerformanceReport_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                var reportDir = System.IO.Path.GetDirectoryName(reportPath);
                
                if (!System.IO.Directory.Exists(reportDir))
                {
                    System.IO.Directory.CreateDirectory(reportDir);
                }

                var report = new System.Text.StringBuilder();
                report.AppendLine("AssetPipeline 性能报告");
                report.AppendLine($"生成时间: {DateTime.Now}");
                report.AppendLine();

                // 指标摘要
                report.AppendLine("性能指标摘要:");
                foreach (var metric in _metrics.Values)
                {
                    report.AppendLine($"  {metric.name}:");
                    report.AppendLine($"    当前值: {metric.value:F2}");
                    report.AppendLine($"    最小值: {metric.minValue:F2}");
                    report.AppendLine($"    最大值: {metric.maxValue:F2}");
                    report.AppendLine($"    平均值: {metric.averageValue:F2}");
                    report.AppendLine($"    采样数: {metric.sampleCount}");
                    report.AppendLine();
                }

                // // 历史快照
                // if (_snapshots.Any())
                // {
                //     report.AppendLine("历史快照:");
                //     foreach (var snapshot in _snapshots.TakeLast(20))
                //     {
                //         report.AppendLine($"  {snapshot.timestamp:HH:mm:ss} - " +
                //                         $"内存: {snapshot.memoryUsage:F1}MB, " +
                //                         $"处理时间: {snapshot.processingTime:F1}ms, " +
                //                         $"活跃处理器: {snapshot.activeProcessors}, " +
                //                         $"队列资产: {snapshot.queuedAssets}");
                //     }
                // }

                System.IO.File.WriteAllText(reportPath, report.ToString());
                AssetDatabase.Refresh();
                
                EditorUtility.DisplayDialog("报告生成成功", $"性能报告已保存到:\n{reportPath}", "确定");
            }
            catch (Exception ex)
            {
                Debug.LogError($"生成性能报告失败: {ex.Message}");
                EditorUtility.DisplayDialog("生成失败", "生成性能报告时发生错误，请查看Console了解详情。", "确定");
            }
        }

        /// <summary>
        /// 开始性能测量
        /// </summary>
        public static IDisposable MeasurePerformance(string operationName)
        {
            return new PerformanceMeasurement(operationName);
        }

        private class PerformanceMeasurement : IDisposable
        {
            private readonly string _operationName;
            private readonly DateTime _startTime;

            public PerformanceMeasurement(string operationName)
            {
                _operationName = operationName;
                _startTime = DateTime.Now;
            }

            public void Dispose()
            {
                var elapsed = (DateTime.Now - _startTime).TotalMilliseconds;
                RecordMetric(_operationName, elapsed);
            }
        }
    }
}
