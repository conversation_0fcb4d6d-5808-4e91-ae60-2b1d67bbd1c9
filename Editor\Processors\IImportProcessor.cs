using System.Collections.Generic;
using AssetPipeline.Core;
using AssetPipeline.Pipelines;
using UnityEditor;
using UnityEngine;

namespace AssetPipeline.Processors
{
    /// <summary>
    /// 资源导入管线处理接口
    /// 实现此接口的处理器可以挂接到Unity的AssetPostprocessor事件中，
    /// 以在资源导入的不同阶段执行自定义逻辑。
    /// </summary>
    public interface IImportProcessor : IProcessor
    {
        /// <summary>
        /// 是否仅在首次导入时处理
        /// true: 仅首次导入时执行处理逻辑
        /// false: 每次导入都执行处理逻辑
        /// </summary>
        bool OnlyFirstImport { get; }

        // --- 处理方法 ---
        // --- 预处理 (Pre-processing) ---
        
        /// <summary>
        /// 在任何资源被导入之前调用。
        /// </summary>
        IEnumerable<CheckResult> OnPreprocessAsset(AssetImporter importer, ImportContext context);
        
        /// <summary>
        /// 在纹理被导入之前调用。
        /// </summary>
        IEnumerable<CheckResult> OnPreprocessTexture(TextureImporter importer, ImportContext context);
        
        /// <summary>
        /// 在模型被导入之前调用。
        /// </summary>
        IEnumerable<CheckResult> OnPreprocessModel(ModelImporter importer, ImportContext context);
        
        /// <summary>
        /// 在音频被导入之前调用。
        /// </summary>
        IEnumerable<CheckResult> OnPreprocessAudio(AudioImporter importer, ImportContext context);

        // --- 后处理 (Post-processing) ---
        
        /// <summary>
        /// 在任何资源被导入之后调用。
        /// </summary>
        IEnumerable<CheckResult> OnPostprocessAsset(AssetImporter importer, ImportContext context);
        
        /// <summary>
        /// 在纹理被导入之后调用。
        /// </summary>
        IEnumerable<CheckResult> OnPostprocessTexture(TextureImporter importer, Texture2D texture, ImportContext context);
        
        /// <summary>
        /// 在模型被导入之后调用。
        /// </summary>
        IEnumerable<CheckResult> OnPostprocessModel(ModelImporter importer, GameObject model, ImportContext context);
        
        /// <summary>
        /// 在音频被导入之后调用。
        /// </summary>
        IEnumerable<CheckResult> OnPostprocessAudio(AudioImporter importer, AudioClip clip, ImportContext context);
    }
} 