using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using AssetPipeline.Core;
using Logger = AssetPipeline.Core.Logger;

namespace AssetPipeline.Pipelines
{
    /// <summary>
    /// 导入上下文 - 会话管理与循环检测
    /// </summary>
    public class ImportContext : PipelineContext
    {
        #region 会话状态

        /// <summary>
        /// 每个资源的导入计数 (path -> rounds)
        /// </summary>
        private readonly Dictionary<string, int> importCounts = new Dictionary<string, int>();

        /// <summary>
        /// 检测到循环的资源集合
        /// </summary>
        private readonly HashSet<string> loopDetectedAssets = new HashSet<string>();

        /// <summary>
        /// 当前批次中被修改过的路径集合
        /// </summary>
        public HashSet<string> ModifiedPaths { get; private set; } = new HashSet<string>();

        /// <summary>
        /// 会话内是否检测到循环
        /// </summary>
        public bool IsLoopDetected => loopDetectedAssets.Count > 0;

        /// <summary>
        /// 获取指定资源的导入计数
        /// </summary>
        public int GetImportCount(string assetPath) => importCounts.ContainsKey(assetPath) ? importCounts[assetPath] : 0;

        /// <summary>
        /// 当前会话经历的最大轮次
        /// </summary>
        public int MaxImportRound => importCounts.Values.DefaultIfEmpty(1).Max();

        #endregion

        #region 构造函数

        public ImportContext()
        {
            // ImportContext的AssetPaths在运行时动态更新
        }
        
        public ImportContext(IReadOnlyList<string> assetPaths)
        {
            AssetPaths = assetPaths ?? new List<string>();
        }

        #endregion

        #region 导入管理
        
        /// <summary>
        /// 记录资源导入
        /// </summary>
        public void RecordImport(string assetPath)
        {
            if (string.IsNullOrEmpty(assetPath)) return;
            
            ModifiedPaths.Add(assetPath);
            
            // 使用通用上下文数据管理导入计数
            if (!importCounts.ContainsKey(assetPath))
                importCounts[assetPath] = 0;
            importCounts[assetPath]++;
            
            Logger.Debug(LogModule.Pipeline, 
                $"记录导入: {assetPath} (轮次: {importCounts[assetPath]})");
        }
        
        /// <summary>
        /// 检查导入循环
        /// </summary>
        public bool CheckImportLoop(string assetPath, AssetImporter importer)
        {
            if (string.IsNullOrEmpty(assetPath) || importer == null) return false;
            
            // 委托给ImportLoopHelper处理所有循环检测逻辑
            var isLoop = ImportLoopHelper.CheckLoop(assetPath, importer, this);
            
            if (isLoop)
            {
                // 记录检测到的循环资产
                loopDetectedAssets.Add(assetPath);
                
                Logger.Error(LogModule.Pipeline, $"检测到导入循环: {assetPath}");
            }
            
            return isLoop;
        }
        
        #endregion

        #region 统计信息
        
        /// <summary>
        /// 获取会话统计信息
        /// </summary>
        public override string GetStatistics()
        {
            var totalAssets = importCounts.Count;
            var totalImports = importCounts.Values.Sum();
            var maxRounds = importCounts.Values.DefaultIfEmpty(0).Max();
            var loopCount = loopDetectedAssets.Count;
            
            var baseStats = base.GetStatistics();
            return $"{baseStats}, 资源数: {totalAssets}, 总导入次数: {totalImports}, 最大轮次: {maxRounds}, 循环检测: {loopCount}";
        }
        
        #endregion

        #region 完成处理

        protected override void OnComplete()
        {
            // 报告循环检测结果
            if (loopDetectedAssets.Count > 0)
            {
                Logger.Warning(LogModule.Pipeline, 
                    $"导入处理流程检测到 {loopDetectedAssets.Count} 个循环:");
                    
                foreach (var path in loopDetectedAssets)
                {
                    Logger.Warning(LogModule.Pipeline, $"  - {path}");
                }
            }
            
            Logger.Debug(LogModule.Pipeline, $"导入会话完成: {GetStatistics()}");
            
            // 清理批次数据
            ModifiedPaths.Clear();
            
            // 调用基类清理
            base.OnComplete();
        }

        #endregion
    } 
} 