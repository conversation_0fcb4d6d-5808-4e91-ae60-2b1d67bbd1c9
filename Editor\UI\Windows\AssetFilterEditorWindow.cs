using UnityEngine;
using UnityEditor;
using AssetPipeline.Core;
using AssetPipeline.Processors;
using AssetPipeline.UI.Components;
using System.Collections.Generic;
using System.Linq;
using AssetPipeline.Config;

namespace AssetPipeline.UI.Windows
{
    public class AssetFilterEditorWindow : EditorWindow
    {
        private AssetFilter targetFilter;
        private Vector2 scrollPosition;
        private bool showAssetTypeConfig = true;
        private bool showFilePatternConfig = true;
        private bool showPathConfig = true;
        private bool showProcessorConfig = true;
        
        private string newCustomExtension = "";
        private string newSpecificPath = "";
        private string testAssetPath = "";

        public static void ShowWindow(AssetFilter filter)
        {
            var window = GetWindow<AssetFilterEditorWindow>("AssetFilter 编辑器");
            window.targetFilter = filter;
            window.minSize = new Vector2(600, 700);
            window.Show();
        }

        void OnGUI()
        {
            if (targetFilter == null)
            {
                EditorGUILayout.HelpBox("没有选中的AssetFilter", MessageType.Warning);
                if (GUILayout.Button("关闭"))
                {
                    Close();
                }
                return;
            }

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            DrawHeader();
            EditorGUILayout.Space();
            
            DrawBasicConfig();
            EditorGUILayout.Space();
            
            DrawFilePatternConfig();
            EditorGUILayout.Space();
            
            DrawSpecificPathConfig();
            EditorGUILayout.Space();
            
            DrawProcessorConfig();
            EditorGUILayout.Space();
            
            DrawTestSection();

            EditorGUILayout.EndScrollView();
        }

        void DrawHeader()
        {
            UIHelper.DrawHeader($"编辑Filter: {targetFilter.DisplayName}", "🔍");
            
            UIHelper.DrawBox(() =>
            {
                targetFilter.DisplayName = EditorGUILayout.TextField("显示名称", targetFilter.DisplayName);
                
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("启用状态", GUILayout.Width(60));
                targetFilter.Enabled = EditorGUILayout.Toggle(targetFilter.Enabled);
                EditorGUILayout.EndHorizontal();
                
                targetFilter.Priority = EditorGUILayout.IntField("优先级", targetFilter.Priority);
                targetFilter.Layer = EditorGUILayout.IntField("层级", targetFilter.Layer);
                
                UIHelper.DrawKeyValue("GUID", targetFilter.GUID, 60);
            });
        }

        void DrawBasicConfig()
        {
            showAssetTypeConfig = UIHelper.DrawFoldout(ref showAssetTypeConfig, "资产类型配置", "📋", true);
            
            if (showAssetTypeConfig)
            {
                UIHelper.DrawBox(() =>
                {
                    EditorGUILayout.LabelField("资产类型选择:", EditorStyles.boldLabel);
                    
                    var currentTypes = (AssetTypeFlag)EditorGUILayout.EnumFlagsField("资产类型", targetFilter.AssetTypes);
                    if (currentTypes != targetFilter.AssetTypes)
                    {
                        var typesField = typeof(AssetFilter).GetField("assetTypes", 
                            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                        typesField?.SetValue(targetFilter, currentTypes);
                        EditorUtility.SetDirty(targetFilter);
                    }
                    
                    if ((currentTypes & AssetTypeFlag.Other) != 0)
                    {
                        EditorGUILayout.Space();
                        EditorGUILayout.LabelField("自定义扩展名:", EditorStyles.boldLabel);
                        
                        for (int i = 0; i < targetFilter.CustomExtensions.Count; i++)
                        {
                            EditorGUILayout.BeginHorizontal();
                            targetFilter.CustomExtensions[i] = EditorGUILayout.TextField(targetFilter.CustomExtensions[i]);
                            if (GUILayout.Button("删除", GUILayout.Width(50)))
                            {
                                targetFilter.CustomExtensions.RemoveAt(i);
                                EditorUtility.SetDirty(targetFilter);
                                break;
                            }
                            EditorGUILayout.EndHorizontal();
                        }
                        
                        EditorGUILayout.BeginHorizontal();
                        newCustomExtension = EditorGUILayout.TextField("新扩展名", newCustomExtension);
                        if (GUILayout.Button("添加", GUILayout.Width(50)) && !string.IsNullOrEmpty(newCustomExtension))
                        {
                            if (!newCustomExtension.StartsWith("."))
                                newCustomExtension = "." + newCustomExtension;
                            targetFilter.CustomExtensions.Add(newCustomExtension);
                            newCustomExtension = "";
                            EditorUtility.SetDirty(targetFilter);
                        }
                        EditorGUILayout.EndHorizontal();
                    }
                });
            }
        }

        void DrawFilePatternConfig()
        {
            showFilePatternConfig = UIHelper.DrawFoldout(ref showFilePatternConfig, "文件名匹配配置", "📄", true);
            
            if (showFilePatternConfig)
            {
                UIHelper.DrawBox(() =>
                {
                    EditorGUILayout.LabelField("文件名匹配规则:", EditorStyles.boldLabel);
                    
                    if (targetFilter.FilePattern != null)
                    {
                        DrawPathMatcherConfig("包含规则", targetFilter.FilePattern);
                    }
                    
                    EditorGUILayout.Space();
                    EditorGUILayout.LabelField("排除规则:", EditorStyles.boldLabel);
                    
                    for (int i = 0; i < targetFilter.FileExclusions.Count; i++)
                    {
                        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                        EditorGUILayout.BeginHorizontal();
                        EditorGUILayout.LabelField($"排除规则 {i + 1}", EditorStyles.boldLabel);
                        if (GUILayout.Button("删除", GUILayout.Width(50)))
                        {
                            targetFilter.FileExclusions.RemoveAt(i);
                            EditorUtility.SetDirty(targetFilter);
                            break;
                        }
                        EditorGUILayout.EndHorizontal();
                        
                        if (i < targetFilter.FileExclusions.Count)
                        {
                            DrawPathMatcherConfig($"排除 {i + 1}", targetFilter.FileExclusions[i]);
                        }
                        EditorGUILayout.EndVertical();
                    }
                    
                    if (GUILayout.Button("添加排除规则"))
                    {
                        targetFilter.FileExclusions.Add(new PathMatcher());
                        EditorUtility.SetDirty(targetFilter);
                    }
                });
            }
        }

        void DrawPathMatcherConfig(string label, PathMatcher pathMatcher)
        {
            if (pathMatcher == null) return;
            
            EditorGUILayout.LabelField(label, EditorStyles.miniLabel);
            EditorGUI.indentLevel++;
            
            var mode = (PathMatcher.MatchMode)EditorGUILayout.EnumPopup("匹配模式", pathMatcher.Mode);
            var pattern = EditorGUILayout.TextField("匹配模式", pathMatcher.Pattern);
            var ignoreCase = EditorGUILayout.Toggle("忽略大小写", pathMatcher.IgnoreCase);
            
            var modeField = typeof(PathMatcher).GetField("mode", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var patternField = typeof(PathMatcher).GetField("pattern", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var ignoreCaseField = typeof(PathMatcher).GetField("ignoreCase", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (modeField?.GetValue(pathMatcher)?.Equals(mode) != true)
            {
                modeField?.SetValue(pathMatcher, mode);
                EditorUtility.SetDirty(targetFilter);
            }
            
            if ((string)patternField?.GetValue(pathMatcher) != pattern)
            {
                patternField?.SetValue(pathMatcher, pattern);
                EditorUtility.SetDirty(targetFilter);
            }
            
            if ((bool)ignoreCaseField?.GetValue(pathMatcher) != ignoreCase)
            {
                ignoreCaseField?.SetValue(pathMatcher, ignoreCase);
                EditorUtility.SetDirty(targetFilter);
            }
            
            if (!string.IsNullOrEmpty(pattern))
            {
                EditorGUILayout.LabelField($"描述: {pathMatcher.GetDescription()}", EditorStyles.miniLabel);
            }
            
            EditorGUI.indentLevel--;
        }

        void DrawSpecificPathConfig()
        {
            showPathConfig = UIHelper.DrawFoldout(ref showPathConfig, "特定路径配置", "📁", true);
            
            if (showPathConfig)
            {
                UIHelper.DrawBox(() =>
                {
                    EditorGUILayout.LabelField("特定路径限制:", EditorStyles.boldLabel);
                    EditorGUILayout.LabelField("(空时匹配所有路径)", EditorStyles.miniLabel);
                    
                    for (int i = 0; i < targetFilter.SpecificPaths.Count; i++)
                    {
                        EditorGUILayout.BeginHorizontal();
                        targetFilter.SpecificPaths[i] = EditorGUILayout.TextField(targetFilter.SpecificPaths[i]);
                        if (GUILayout.Button("删除", GUILayout.Width(50)))
                        {
                            targetFilter.SpecificPaths.RemoveAt(i);
                            EditorUtility.SetDirty(targetFilter);
                            break;
                        }
                        EditorGUILayout.EndHorizontal();
                    }
                    
                    EditorGUILayout.BeginHorizontal();
                    newSpecificPath = EditorGUILayout.TextField("新路径", newSpecificPath);
                    if (GUILayout.Button("添加", GUILayout.Width(50)) && !string.IsNullOrEmpty(newSpecificPath))
                    {
                        targetFilter.SpecificPaths.Add(newSpecificPath);
                        newSpecificPath = "";
                        EditorUtility.SetDirty(targetFilter);
                    }
                    EditorGUILayout.EndHorizontal();
                });
            }
        }

        void DrawProcessorConfig()
        {
            showProcessorConfig = UIHelper.DrawFoldout(ref showProcessorConfig, "处理器配置", "⚙️", true);
            
            if (showProcessorConfig)
            {
                UIHelper.DrawBox(() =>
                {
                    EditorGUILayout.LabelField($"已配置处理器: {targetFilter.Processors.Count}", EditorStyles.boldLabel);
                    
                    for (int i = 0; i < targetFilter.Processors.Count; i++)
                    {
                        var processor = targetFilter.Processors[i];
                        if (processor == null) continue;
                        
                        EditorGUILayout.BeginHorizontal();
                        EditorGUILayout.LabelField($"{i + 1}. {processor.DisplayName}", EditorStyles.miniLabel);
                        
                        if (GUILayout.Button("编辑", GUILayout.Width(50)))
                        {
                            Selection.activeObject = processor;
                            EditorGUIUtility.PingObject(processor);
                        }
                        
                        if (GUILayout.Button("删除", GUILayout.Width(50)))
                        {
                            targetFilter.RemoveProcessor(processor);
                            break;
                        }
                        
                        EditorGUILayout.EndHorizontal();
                    }
                    
                    EditorGUILayout.Space();
                    if (GUILayout.Button("添加处理器"))
                    {
                        ShowAddProcessorMenu();
                    }
                });
            }
        }

        void ShowAddProcessorMenu()
        {
            var menu = new GenericMenu();
            var processorTypes = GetAvailableProcessorTypes();
            
            foreach (var type in processorTypes)
            {
                var displayName = GetProcessorDisplayName(type);
                menu.AddItem(new GUIContent(displayName), false, () => CreateAndAddProcessor(type));
            }
            
            menu.ShowAsContext();
        }

        System.Type[] GetAvailableProcessorTypes()
        {
            return System.AppDomain.CurrentDomain.GetAssemblies()
                .SelectMany(assembly => assembly.GetTypes())
                .Where(type => typeof(AssetProcessor).IsAssignableFrom(type) && !type.IsAbstract)
                .ToArray();
        }

        string GetProcessorDisplayName(System.Type type)
        {
            var attribute = type.GetCustomAttributes(typeof(ProcessorAttribute), false).FirstOrDefault() as ProcessorAttribute;
            return attribute?.DisplayName ?? type.Name;
        }

        void CreateAndAddProcessor(System.Type processorType)
        {
            var processor = CreateInstance(processorType) as AssetProcessor;
            if (processor != null)
            {
                processor.name = $"{processorType.Name}_{System.DateTime.Now:HHmmss}";
                
                var processorDir = "Assets/Editor/AssetPipeline/Data/Processors";
                if (!System.IO.Directory.Exists(processorDir))
                {
                    System.IO.Directory.CreateDirectory(processorDir);
                }
                
                var processorPath = AssetDatabase.GenerateUniqueAssetPath($"{processorDir}/{processor.name}.asset");
                AssetDatabase.CreateAsset(processor, processorPath);
                
                targetFilter.AddProcessor(processor);
                AssetDatabase.SaveAssets();
                EditorUtility.SetDirty(targetFilter);
            }
        }

        void DrawTestSection()
        {
            UIHelper.DrawHeader("测试匹配", "🧪");
            
            UIHelper.DrawBox(() =>
            {
                EditorGUILayout.BeginHorizontal();
                testAssetPath = EditorGUILayout.TextField("测试路径", testAssetPath);
                if (GUILayout.Button("选择", GUILayout.Width(50)))
                {
                    var path = EditorUtility.OpenFilePanel("选择测试文件", "Assets", "");
                    if (!string.IsNullOrEmpty(path) && path.StartsWith(Application.dataPath))
                    {
                        testAssetPath = "Assets" + path.Substring(Application.dataPath.Length);
                    }
                }
                EditorGUILayout.EndHorizontal();
                
                if (!string.IsNullOrEmpty(testAssetPath))
                {
                    EditorGUILayout.Space();
                    var isMatch = targetFilter.IsMatch(testAssetPath);
                    var message = isMatch ? "✅ 匹配成功" : "❌ 不匹配";
                    var messageType = isMatch ? MessageType.Info : MessageType.Warning;
                    
                    UIHelper.DrawStatusText(message, messageType);
                    
                    if (isMatch)
                    {
                        var processors = new List<AssetProcessor>();
                        targetFilter.GetMatchingProcessors(testAssetPath, processors);
                        if (processors.Any())
                        {
                            EditorGUILayout.LabelField($"匹配的处理器: {processors.Count} 个", EditorStyles.miniLabel);
                            foreach (var processor in processors)
                            {
                                EditorGUILayout.LabelField($"  - {processor.DisplayName}", EditorStyles.miniLabel);
                            }
                        }
                    }
                }
            });
        }
    }
} 