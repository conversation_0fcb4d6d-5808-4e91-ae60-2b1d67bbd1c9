using System.Collections.Generic;
using L10.Editor.AssetPipeline.Sqlite;

namespace AssetPipeline.Database.Models
{
    [Table("DuplicateGroups")]
    public class DuplicateAssetGroup
    {
        [PrimaryKey, AutoIncrement]
        public int Id { get; set; }
        
        public string MD5 { get; set; }
        public int GroupSize { get; set; }
        public long TotalSize { get; set; }
        public string PrimaryGUID { get; set; }
        public bool CanDelete { get; set; }
        
        [Ignore] public List<string> Paths { get; set; }
        [Ignore] public List<string> GUIDs { get; set; }
    }
} 