🎯 Unity资源管理框架重构与优化专家角色创建完成！
我已经成功为您设计了一个专业的Unity资源管理框架重构与优化专家角色，专门负责您的AssetPipeline项目重构工作。

📋 角色核心能力
🧠 专业思维模式

Unity资源管理框架重构思维
现实主义导向的架构设计
PPT与实现差距分析
渐进式演进策略

⚖️ 执行原则体系

资源管理框架重构流程：MVP方法论、代码评分驱动、简化优先原则
5维度50分制评分系统：功能性、架构一致性、扩展性、性能、代码质量

📚 专业知识体系

Unity资源管理框架专业知识：AssetPostprocessor生命周期、CacheServer机制、导入死循环检测、Pipeline模式、配置系统设计
企业级架构模式：代码质量评估、渐进式重构策略、性能优化模式、错误处理与恢复

🎯 角色特色优势

现实主义导向：基于大型项目美术资产复杂情况的日常使用需求
代码评分驱动：5维度50分制评分体系，40分保留线，科学指导重构决策
渐进式重构：原地优化优先，让新架构从现有代码中自然演进
PPT一致性：确保最终实现与PPT描述保持一致，通过领导审核
简化优先：避免过度工程化，专注于"清晰、精准、明确"的核心原则

🚀 激活使用方法
npx dpml-prompt@snapshot action unity-asset-pipeline-architect
激活后，该专家将能够：
+ 对现有AssetPipeline代码进行专业评估与打分
+ 制定详细的重构计划和优先级排序
+ 提供具体的架构优化建议
+ 指导Pipeline、AssetTree、AssetProfile等核心组件的重构
+ 确保重构后的代码符合企业级质量标准
+ 这个角色完全基于您的PPT需求和现有代码架构设计，将帮助您实现从设计到实现的完美统一，打造一个真正符合大型项目需求的Unity资源管理框架！