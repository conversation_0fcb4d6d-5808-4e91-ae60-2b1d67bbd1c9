using System;
using System.Collections.Generic;
using System.Linq;
using AssetPipeline.Config;
using AssetPipeline.Core;
using AssetPipeline.Processors;
using AssetPipeline.UI.Windows;
using UnityEditor;
using UnityEditor.Experimental.AssetImporters;
using UnityEngine;
using Logger = AssetPipeline.Core.Logger;

namespace AssetPipeline.Pipelines
{
    /// <summary>
    /// 资源导入管线，基于Unity的AssetPostprocessor实现
    /// 在资源导入时执行各种检查和处理操作
    /// </summary>
    public class AssetImportPipeline : AssetPostprocessor
    {
        #region 核心状态管理

        private const string PIPELINE_NAME = "import";
        private static ImportContext currentContext;

        #endregion

        #region 上下文管理

        /// <summary>
        /// 检查是否应该处理指定路径
        /// </summary>
        private static bool ShouldProcess(string path)
        {
            if (!AssetPipelineConfig.IsPipelineEnabled(PIPELINE_NAME)) return false;
            if (string.IsNullOrEmpty(path) || !path.StartsWith("Assets/")) return false;
            return currentContext != null && !currentContext.IsLoopDetected;
        }

        /// <summary>
        /// 确保导入上下文存在
        /// </summary>
        private static void EnsureImportContext()
        {
            if (currentContext == null)
            {
                currentContext = new ImportContext();
                EditorApplication.delayCall += OnSessionComplete;
                Logger.Debug(LogModule.Pipeline, $"[{PIPELINE_NAME}] 创建新的导入会话");
            }
        }
        
        /// <summary>
        /// 处理导入会话完成后的操作
        /// </summary>
        private static void OnSessionComplete()
        {
            EditorApplication.delayCall -= OnSessionComplete;
            if (currentContext == null) return;

            var results = currentContext.Results;
            if (!results.IsEmpty)
            {
                // 有错误或警告时显示检查结果窗口
                if (results.HasErrors || (results.HasWarnings && AssetPipelineSettings.ShowCommitWarnings))
                {
                     CheckResultWindow.ShowResults("导入检查报告", results.Results.ToList());
                }
                Logger.Info(LogModule.Pipeline, $"[{PIPELINE_NAME}] 导入检查完成: {results.GetStatistics()}");
            }
            
            Logger.Debug(LogModule.Pipeline, $"[{PIPELINE_NAME}] 导入会话完成. {currentContext.GetStatistics()}");
            currentContext.Complete();
            currentContext = null;
        }
        
        #endregion

        #region Unity AssetPostprocessor 事件处理

        /// <summary>
        /// 资产预处理入口，Unity会自动调用
        /// </summary>
        void OnPreprocessAsset() => ExecuteWithLoopDetection(processor => processor.OnPreprocessAsset(assetImporter, currentContext));
        
        #region 特定资产类型预处理

        void OnPreprocessTexture() => Execute(processor => processor.OnPreprocessTexture(assetImporter as TextureImporter, currentContext));
        void OnPreprocessModel() => Execute(processor => processor.OnPreprocessModel(assetImporter as ModelImporter, currentContext));
        void OnPreprocessAudio() => Execute(processor => processor.OnPreprocessAudio(assetImporter as AudioImporter, currentContext));

        #endregion

        #region 特定资产类型后处理

        void OnPostprocessTexture(Texture2D texture) => Execute(processor => processor.OnPostprocessTexture(assetImporter as TextureImporter, texture, currentContext));
        void OnPostprocessModel(GameObject model) => Execute(processor => processor.OnPostprocessModel(assetImporter as ModelImporter, model, currentContext));
        void OnPostprocessAudio(AudioClip audioClip) => Execute(processor => processor.OnPostprocessAudio(assetImporter as AudioImporter, audioClip, currentContext));

        #endregion
        
        /// <summary>
        /// 批量后处理，Unity在所有资产导入完成后调用
        /// </summary>
        static void OnPostprocessAllAssets(string[] importedAssets, string[] deletedAssets, string[] movedAssets, string[] movedFromAssetPaths)
        {
            EnsureImportContext();

            // 过滤出需要处理的路径
            var validPaths = importedAssets.Concat(movedAssets)
                .Distinct()
                .Where(ShouldProcess)
                .ToList();

            if (!validPaths.Any()) return;

            Logger.Debug(LogModule.Pipeline, $"[{PIPELINE_NAME}] 批量后处理开始，资产数量: {validPaths.Count}");

            // 批量执行后处理器
            var resultsByPath = PipelineHelper.ExecuteProcessors<IImportProcessor>(
                validPaths,
                (processor, assetPath) => processor.OnPostprocessAsset(AssetImporter.GetAtPath(assetPath), currentContext)
            );

            // 收集所有结果
            var allResults = resultsByPath.SelectMany(kvp => kvp.Value).ToList();
            currentContext.Results.AddRange(allResults);

            if (allResults.Any())
            {
                Logger.Debug(LogModule.Pipeline, $"[{PIPELINE_NAME}] 批量后处理完成，检查结果: {allResults.Count}个");
            }
        }
        
        #endregion

        #region 导入管道专用封装方法

        /// <summary>
        /// 标准导入处理执行模式 - 消除重复代码
        /// </summary>
        /// <param name="processorAction">处理器执行动作</param>
        private void Execute(Func<IImportProcessor, IEnumerable<CheckResult>> processorAction)
        {
            EnsureImportContext();
            if (!ShouldProcess(assetPath)) return;

            var results = PipelineHelper.ExecuteProcessors<IImportProcessor>(assetPath, processorAction);
            currentContext.Results.AddRange(results);
        }

        /// <summary>
        /// 带循环检测的导入处理执行模式 - 专门用于OnPreprocessAsset
        /// </summary>
        /// <param name="processorAction">处理器执行动作</param>
        private void ExecuteWithLoopDetection(Func<IImportProcessor, IEnumerable<CheckResult>> processorAction)
        {
            EnsureImportContext();
            if (!ShouldProcess(assetPath)) return;

            // 记录导入信息并检查是否有循环导入
            currentContext.RecordImport(assetPath);
            if (currentContext.CheckImportLoop(assetPath, assetImporter))
            {
                var msg = $"检测到导入循环，跳过处理: {assetPath}";
                Logger.Warning(LogModule.Pipeline, $"[{PIPELINE_NAME}] {msg}");
                currentContext.Results.Add(CheckResult.Error(msg).WithAssetPath(assetPath));
                return;
            }

            var results = PipelineHelper.ExecuteProcessors<IImportProcessor>(assetPath, processorAction);
            currentContext.Results.AddRange(results);
        }

        #endregion
    }
}

