using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

namespace AssetPipeline.Processors
{
    /// <summary>
    /// 平台设置管理器
    /// 解决平台特定设置无法通过简单属性路径访问的问题
    /// </summary>
    public static class PlatformSettingsManager
    {
        #region 平台设置定义

        /// <summary>
        /// 平台设置项
        /// </summary>
        [System.Serializable]
        public class PlatformSetting
        {
            public string platformName;     // 平台名称（如"Standalone", "iPhone", "Android"）
            public string propertyName;     // 属性名称（如"maxTextureSize", "textureFormat"）
            public string displayName;      // 显示名称
            public string description;      // 描述
            public Type valueType;          // 值类型
            public object currentValue;     // 当前值
            public object expectedValue;    // 期望值
        }

        /// <summary>
        /// 支持的平台列表
        /// </summary>
        public static readonly string[] SupportedPlatforms = new string[]
        {
            "Standalone",
            "iPhone", 
            "Android",
            "WebGL",
            "PS4",
            "XboxOne",
            "Switch"
        };

        /// <summary>
        /// 支持的纹理平台属性
        /// </summary>
        public static readonly Dictionary<string, Type> TexturePlatformProperties = new Dictionary<string, Type>
        {
            ["maxTextureSize"] = typeof(int),
            ["textureFormat"] = typeof(TextureImporterFormat),
            ["compressionQuality"] = typeof(int),
            ["crunchedCompression"] = typeof(bool),
            ["allowsAlphaSplitting"] = typeof(bool),
            ["overridden"] = typeof(bool)
        };

        #endregion

        #region 平台设置访问

        /// <summary>
        /// 获取纹理导入器的平台设置
        /// </summary>
        public static List<PlatformSetting> GetTexturePlatformSettings(TextureImporter importer)
        {
            var settings = new List<PlatformSetting>();

            foreach (var platform in SupportedPlatforms)
            {
                try
                {
                    var platformSettings = importer.GetPlatformTextureSettings(platform);
                    
                    foreach (var property in TexturePlatformProperties)
                    {
                        var setting = new PlatformSetting
                        {
                            platformName = platform,
                            propertyName = property.Key,
                            displayName = GetPlatformPropertyDisplayName(platform, property.Key),
                            description = GetPlatformPropertyDescription(platform, property.Key),
                            valueType = property.Value,
                            currentValue = GetPlatformPropertyValue(platformSettings, property.Key)
                        };
                        
                        settings.Add(setting);
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogWarning($"获取平台设置失败 {platform}: {ex.Message}");
                }
            }

            return settings;
        }

        /// <summary>
        /// 应用纹理平台设置
        /// </summary>
        public static bool ApplyTexturePlatformSettings(TextureImporter importer, List<PlatformSetting> settings)
        {
            try
            {
                // 按平台分组设置
                var platformGroups = new Dictionary<string, List<PlatformSetting>>();
                foreach (var setting in settings)
                {
                    if (!platformGroups.ContainsKey(setting.platformName))
                        platformGroups[setting.platformName] = new List<PlatformSetting>();
                    
                    platformGroups[setting.platformName].Add(setting);
                }

                // 逐平台应用设置
                foreach (var group in platformGroups)
                {
                    var platform = group.Key;
                    var platformSettings = importer.GetPlatformTextureSettings(platform);
                    bool modified = false;

                    foreach (var setting in group.Value)
                    {
                        if (setting.expectedValue != null)
                        {
                            var applied = SetPlatformPropertyValue(ref platformSettings, setting.propertyName, setting.expectedValue);
                            if (applied) modified = true;
                        }
                    }

                    if (modified)
                    {
                        importer.SetPlatformTextureSettings(platformSettings);
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"应用平台设置失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查平台设置是否符合期望
        /// </summary>
        public static bool ValidatePlatformSettings(TextureImporter importer, List<PlatformSetting> expectedSettings)
        {
            try
            {
                foreach (var expected in expectedSettings)
                {
                    if (expected.expectedValue == null) continue;

                    var platformSettings = importer.GetPlatformTextureSettings(expected.platformName);
                    var currentValue = GetPlatformPropertyValue(platformSettings, expected.propertyName);
                    
                    if (!AreValuesEqual(currentValue, expected.expectedValue))
                    {
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"验证平台设置失败: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 属性访问辅助方法

        /// <summary>
        /// 获取平台属性值
        /// </summary>
        private static object GetPlatformPropertyValue(TextureImporterPlatformSettings platformSettings, string propertyName)
        {
            switch (propertyName)
            {
                case "maxTextureSize":
                    return platformSettings.maxTextureSize;
                case "textureFormat":
                    return platformSettings.format;
                case "compressionQuality":
                    return platformSettings.compressionQuality;
                case "crunchedCompression":
                    return platformSettings.crunchedCompression;
                case "allowsAlphaSplitting":
                    return platformSettings.allowsAlphaSplitting;
                case "overridden":
                    return platformSettings.overridden;
                default:
                    return null;
            }
        }

        /// <summary>
        /// 设置平台属性值
        /// </summary>
        private static bool SetPlatformPropertyValue(ref TextureImporterPlatformSettings platformSettings, string propertyName, object value)
        {
            try
            {
                switch (propertyName)
                {
                    case "maxTextureSize":
                        if (value is int maxSize)
                        {
                            platformSettings.maxTextureSize = maxSize;
                            return true;
                        }
                        break;
                    case "textureFormat":
                        if (value is TextureImporterFormat format)
                        {
                            platformSettings.format = format;
                            return true;
                        }
                        break;
                    case "compressionQuality":
                        if (value is int quality)
                        {
                            platformSettings.compressionQuality = quality;
                            return true;
                        }
                        break;
                    case "crunchedCompression":
                        if (value is bool crunch)
                        {
                            platformSettings.crunchedCompression = crunch;
                            return true;
                        }
                        break;
                    case "allowsAlphaSplitting":
                        if (value is bool alphaSplit)
                        {
                            platformSettings.allowsAlphaSplitting = alphaSplit;
                            return true;
                        }
                        break;
                    case "overridden":
                        if (value is bool overridden)
                        {
                            platformSettings.overridden = overridden;
                            return true;
                        }
                        break;
                }
                return false;
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"设置平台属性失败 {propertyName}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 比较两个值是否相等
        /// </summary>
        public static bool AreValuesEqual(object value1, object value2)
        {
            if (value1 == null && value2 == null) return true;
            if (value1 == null || value2 == null) return false;
            
            return value1.Equals(value2);
        }

        /// <summary>
        /// 获取平台属性显示名称
        /// </summary>
        private static string GetPlatformPropertyDisplayName(string platform, string propertyName)
        {
            var platformDisplayName = GetPlatformDisplayName(platform);
            
            switch (propertyName)
            {
                case "maxTextureSize": return $"Max Texture Size ({platformDisplayName})";
                case "textureFormat": return $"Format ({platformDisplayName})";
                case "compressionQuality": return $"Compression Quality ({platformDisplayName})";
                case "crunchedCompression": return $"Use Crunch Compression ({platformDisplayName})";
                case "allowsAlphaSplitting": return $"Alpha Is Transparency ({platformDisplayName})";
                case "overridden": return $"Override For {platformDisplayName}";
                default: return $"{propertyName} ({platformDisplayName})";
            }
        }

        /// <summary>
        /// 获取平台属性描述
        /// </summary>
        private static string GetPlatformPropertyDescription(string platform, string propertyName)
        {
            var platformDisplayName = GetPlatformDisplayName(platform);
            
            switch (propertyName)
            {
                case "maxTextureSize": 
                    return $"Maximum texture size for {platformDisplayName} platform";
                case "textureFormat": 
                    return $"Texture format used on {platformDisplayName} platform";
                case "compressionQuality": 
                    return $"Compression quality setting for {platformDisplayName} platform";
                case "crunchedCompression": 
                    return $"Whether to use Crunch compression on {platformDisplayName} platform";
                case "allowsAlphaSplitting": 
                    return $"Whether alpha channel splitting is allowed on {platformDisplayName} platform";
                case "overridden": 
                    return $"Whether platform-specific settings override default settings for {platformDisplayName}";
                default: 
                    return $"{propertyName} setting for {platformDisplayName} platform";
            }
        }

        /// <summary>
        /// 获取平台显示名称
        /// </summary>
        private static string GetPlatformDisplayName(string platform)
        {
            switch (platform)
            {
                case "Standalone": return "PC, Mac & Linux Standalone";
                case "iPhone": return "iOS";
                case "Android": return "Android";
                case "WebGL": return "WebGL";
                case "PS4": return "PlayStation 4";
                case "XboxOne": return "Xbox One";
                case "Switch": return "Nintendo Switch";
                default: return platform;
            }
        }

        #endregion
    }
}
