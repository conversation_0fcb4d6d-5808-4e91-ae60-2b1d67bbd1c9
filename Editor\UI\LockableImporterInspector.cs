using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using AssetPipeline.Processors;

namespace AssetPipeline.UI
{
    /// <summary>
    /// 可锁定的导入器Inspector
    /// 提供简化的平铺式界面，支持属性锁定功能
    /// </summary>
    public class LockableImporterInspector : EditorWindow
    {
        #region 静态管理

        private static LockableImporterInspector instance;
        private static AssetImporter currentImporter;
        private static bool isInspectorActive = false;

        /// <summary>
        /// 激活Inspector增强功能
        /// </summary>
        public static void ActivateInspectorEnhancement()
        {
            isInspectorActive = true;
            Selection.selectionChanged += OnSelectionChanged;
            EditorApplication.update += OnEditorUpdate;
        }

        /// <summary>
        /// 停用Inspector增强功能
        /// </summary>
        public static void DeactivateInspectorEnhancement()
        {
            isInspectorActive = false;
            Selection.selectionChanged -= OnSelectionChanged;
            EditorApplication.update -= OnEditorUpdate;
            
            if (instance != null)
            {
                instance.Close();
                instance = null;
            }
        }

        #endregion

        #region 界面状态

        private Vector2 scrollPosition;
        private List<PropertySelectorSystem.PropertyInfo> availableProperties = new List<PropertySelectorSystem.PropertyInfo>();
        private HashSet<string> lockedProperties = new HashSet<string>();
        private bool showAdvancedProperties = false;
        private string searchFilter = "";

        #endregion

        #region Unity生命周期

        [MenuItem("Tools/Asset Pipeline/Lockable Inspector")]
        public static void ShowWindow()
        {
            instance = GetWindow<LockableImporterInspector>("属性锁定");
            instance.minSize = new Vector2(300, 400);
            ActivateInspectorEnhancement();
        }

        void OnEnable()
        {
            RefreshProperties();
        }

        void OnDisable()
        {
            DeactivateInspectorEnhancement();
        }

        void OnGUI()
        {
            if (currentImporter == null)
            {
                EditorGUILayout.HelpBox("请在Project窗口中选择一个资产来查看其可锁定属性", MessageType.Info);
                return;
            }

            DrawHeader();
            DrawToolbar();
            DrawPropertyList();
        }

        #endregion

        #region 界面绘制

        /// <summary>
        /// 绘制头部信息
        /// </summary>
        private void DrawHeader()
        {
            EditorGUILayout.BeginVertical("box");
            
            EditorGUILayout.LabelField("当前资产", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"类型: {currentImporter.GetType().Name}");
            EditorGUILayout.LabelField($"路径: {currentImporter.assetPath}");
            
            var lockedCount = lockedProperties.Count;
            var totalCount = availableProperties.Count;
            EditorGUILayout.LabelField($"锁定属性: {lockedCount} / {totalCount}");
            
            EditorGUILayout.EndVertical();
        }

        /// <summary>
        /// 绘制工具栏
        /// </summary>
        private void DrawToolbar()
        {
            EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
            
            // 搜索框
            GUILayout.Label("搜索:", GUILayout.Width(40));
            var newSearchFilter = EditorGUILayout.TextField(searchFilter, EditorStyles.toolbarTextField);
            if (newSearchFilter != searchFilter)
            {
                searchFilter = newSearchFilter;
                //FilterProperties();
            }
            
            GUILayout.FlexibleSpace();
            
            // 显示选项
            showAdvancedProperties = GUILayout.Toggle(showAdvancedProperties, "高级", EditorStyles.toolbarButton);
            
            // 批量操作
            if (GUILayout.Button("全部锁定", EditorStyles.toolbarButton))
            {
                LockAllProperties();
            }
            
            if (GUILayout.Button("全部解锁", EditorStyles.toolbarButton))
            {
                UnlockAllProperties();
            }
            
            EditorGUILayout.EndHorizontal();
        }

        /// <summary>
        /// 绘制属性列表 - 简化的平铺式布局
        /// </summary>
        private void DrawPropertyList()
        {
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            
            var filteredProperties = GetFilteredProperties();
            
            foreach (var property in filteredProperties)
            {
                DrawPropertyItem(property);
            }
            
            if (filteredProperties.Count == 0)
            {
                EditorGUILayout.HelpBox("没有找到匹配的属性", MessageType.Info);
            }
            
            EditorGUILayout.EndScrollView();
        }

        /// <summary>
        /// 绘制单个属性项
        /// </summary>
        private void DrawPropertyItem(PropertySelectorSystem.PropertyInfo property)
        {
            EditorGUILayout.BeginHorizontal("box");
            
            // 锁定状态切换
            var isLocked = lockedProperties.Contains(property.propertyPath);
            var newLocked = EditorGUILayout.Toggle(isLocked, GUILayout.Width(20));
            
            if (newLocked != isLocked)
            {
                if (newLocked)
                {
                    lockedProperties.Add(property.propertyPath);
                }
                else
                {
                    lockedProperties.Remove(property.propertyPath);
                }
                SaveLockedProperties();
            }
            
            // 锁定图标
            if (isLocked)
            {
                var lockOriginalColor = GUI.color;
                GUI.color = Color.red;
                GUILayout.Label("🔒", GUILayout.Width(20));
                GUI.color = lockOriginalColor;
            }
            else
            {
                GUILayout.Label("🔓", GUILayout.Width(20));
            }
            
            // 属性信息
            EditorGUILayout.BeginVertical();
            
            // 属性名称
            EditorGUILayout.LabelField(property.displayName, EditorStyles.boldLabel);
            
            // 属性描述
            if (!string.IsNullOrEmpty(property.description))
            {
                EditorGUILayout.LabelField(property.description, EditorStyles.miniLabel);
            }
            
            // 分类和类型信息
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"分类: {property.category}", EditorStyles.miniLabel, GUILayout.Width(120));
            
            var typeColor = GetPropertyTypeColor(property.propertyType);
            var originalColor = GUI.color;
            GUI.color = typeColor;
            EditorGUILayout.LabelField(property.propertyType.ToString(), EditorStyles.miniLabel, GUILayout.Width(80));
            GUI.color = originalColor;
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.EndVertical();
            
            EditorGUILayout.EndHorizontal();
            
            // 锁定状态的红色边框
            if (isLocked)
            {
                var lastRect = GUILayoutUtility.GetLastRect();
                var borderRect = new Rect(lastRect.x - 2, lastRect.y - 2, lastRect.width + 4, lastRect.height + 4);
                EditorGUI.DrawRect(borderRect, new Color(1, 0, 0, 0.3f));
            }
        }

        #endregion

        #region 属性管理

        /// <summary>
        /// 刷新属性列表
        /// </summary>
        private void RefreshProperties()
        {
            availableProperties.Clear();
            
            if (currentImporter == null)
                return;

            // 根据导入器类型获取属性
            var importerType = currentImporter.GetType().Name;
            availableProperties = PropertySelectorSystem.GetPropertiesForImporterType(importerType);
            
            // 加载锁定状态
            LoadLockedProperties();
        }

        /// <summary>
        /// 过滤属性
        /// </summary>
        private List<PropertySelectorSystem.PropertyInfo> GetFilteredProperties()
        {
            var filtered = availableProperties;
            
            // 搜索过滤
            if (!string.IsNullOrEmpty(searchFilter))
            {
                filtered = PropertySelectorSystem.SearchProperties(filtered, searchFilter);
            }
            
            // 高级属性过滤
            if (!showAdvancedProperties)
            {
                filtered = filtered.Where(p => p.propertyType != PropertySelectorSystem.PropertyType.Advanced).ToList();
            }
            
            // 按Unity Inspector的顺序排列
            return filtered.OrderBy(p => GetPropertyOrder(p)).ToList();
        }

        /// <summary>
        /// 获取属性在Unity Inspector中的排序顺序
        /// </summary>
        private int GetPropertyOrder(PropertySelectorSystem.PropertyInfo property)
        {
            // 基础设置优先
            if (property.propertyType == PropertySelectorSystem.PropertyType.Basic)
                return 1;
            
            // 性能相关其次
            if (property.propertyType == PropertySelectorSystem.PropertyType.Performance)
                return 2;
            
            // 质量设置
            if (property.propertyType == PropertySelectorSystem.PropertyType.Quality)
                return 3;
            
            // 平台设置
            if (property.propertyType == PropertySelectorSystem.PropertyType.Platform)
                return 4;
            
            // 高级设置最后
            return 5;
        }

        /// <summary>
        /// 锁定所有属性
        /// </summary>
        private void LockAllProperties()
        {
            foreach (var property in availableProperties)
            {
                lockedProperties.Add(property.propertyPath);
            }
            SaveLockedProperties();
        }

        /// <summary>
        /// 解锁所有属性
        /// </summary>
        private void UnlockAllProperties()
        {
            lockedProperties.Clear();
            SaveLockedProperties();
        }

        /// <summary>
        /// 保存锁定属性状态
        /// </summary>
        private void SaveLockedProperties()
        {
            if (currentImporter == null) return;
            
            var key = $"LockedProperties_{currentImporter.assetPath}";
            var value = string.Join(";", lockedProperties);
            EditorPrefs.SetString(key, value);
        }

        /// <summary>
        /// 加载锁定属性状态
        /// </summary>
        private void LoadLockedProperties()
        {
            lockedProperties.Clear();
            
            if (currentImporter == null) return;
            
            var key = $"LockedProperties_{currentImporter.assetPath}";
            var value = EditorPrefs.GetString(key, "");
            
            if (!string.IsNullOrEmpty(value))
            {
                var paths = value.Split(';');
                foreach (var path in paths)
                {
                    if (!string.IsNullOrEmpty(path))
                    {
                        lockedProperties.Add(path);
                    }
                }
            }
        }

        /// <summary>
        /// 获取属性类型颜色
        /// </summary>
        private Color GetPropertyTypeColor(PropertySelectorSystem.PropertyType propertyType)
        {
            switch (propertyType)
            {
                case PropertySelectorSystem.PropertyType.Basic: return Color.green;
                case PropertySelectorSystem.PropertyType.Performance: return Color.red;
                case PropertySelectorSystem.PropertyType.Quality: return Color.blue;
                case PropertySelectorSystem.PropertyType.Advanced: return Color.yellow;
                case PropertySelectorSystem.PropertyType.Platform: return new Color(1f, 0.5f, 0);
                default: return Color.gray;
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 选择变化事件
        /// </summary>
        private static void OnSelectionChanged()
        {
            if (!isInspectorActive) return;
            
            var selectedObject = Selection.activeObject;
            if (selectedObject != null)
            {
                var assetPath = AssetDatabase.GetAssetPath(selectedObject);
                if (!string.IsNullOrEmpty(assetPath))
                {
                    var importer = AssetImporter.GetAtPath(assetPath);
                    if (importer != null && importer != currentImporter)
                    {
                        currentImporter = importer;
                        if (instance != null)
                        {
                            instance.RefreshProperties();
                            instance.Repaint();
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 编辑器更新事件
        /// </summary>
        private static void OnEditorUpdate()
        {
            if (!isInspectorActive) return;
            
            // 这里可以添加实时监控逻辑
        }

        #endregion
    }
}
