using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using AssetPipeline.Core;
using UnityEngine;
using UnityEditor;

namespace AssetPipeline.UI
{
    [Serializable]
    public class AssetTreeElement : TreeElement
    {
        public AssetProfile Profile;
        public string GUID => Profile != null ? Profile.GUID : string.Empty;

        SerializedObject m_SerializedObject;
        public SerializedObject SerializedObject
        {
            get
            {
                if (m_SerializedObject == null && Profile != null)
                {
                    m_SerializedObject = new SerializedObject(Profile);
                }
                return m_SerializedObject;
            }
        }
        
        public bool Enabled 
        {
            get
            {
                if (Profile == null)
                {
                    return false;
                }
                return Profile.Enabled;
            }
        }
        
        public int Priority
        {
            get
            {
                if (Profile == null)
                {
                    return -1; 
                }
                return Profile.Priority;
            }
        }
        
        public bool IsMatch(string assetPath)
        {
            if (Profile == null || string.IsNullOrEmpty(assetPath))
            {
                return false;
            }
            return Profile.IsMatch(assetPath);
        }

        public void Rename(string name)
        {
            if (Profile == null)
            {
                this.name = name;
                return;
            }
            
            // 确保只传递文件名，移除任何路径信息
            var fileName = Path.GetFileNameWithoutExtension(name);
            if (string.IsNullOrEmpty(fileName))
            {
                fileName = name;
            }
            
            if (Profile.Rename(fileName))
            {
                this.name = Profile.name;
            }
            else
            {
                // 重命名失败，保持原名称
                Debug.LogWarning($"[AssetTreeElement] Profile重命名失败，保持原名称: {this.name}");
            }
        }

        public AssetTreeElement(string name, int depth, int id, AssetProfile profile) : base(name, depth, id)
        {
            this.Profile = profile;
        }
        
        public bool IsValid()
        {
            return depth == -1 || Profile != null;
        }
    }
}