using UnityEditor;
using AssetPipeline.Core;

namespace AssetPipeline.Processors.Fixes
{
    public class SetTextureMaxSizeFix : IAutoFix
    {
        private readonly int _maxSize;

        public SetTextureMaxSizeFix(int size)
        {
            _maxSize = size;
        }

        public string GetDescription() => $"将最大尺寸设置为 {_maxSize}";

        public bool CanFix(string assetPath)
        {
            return AssetImporter.GetAtPath(assetPath) is TextureImporter;
        }

        public bool DoFix(string assetPath)
        {
            if (AssetImporter.GetAtPath(assetPath) is TextureImporter importer)
            {
                var platformSettings = importer.GetDefaultPlatformTextureSettings();
                if (platformSettings.maxTextureSize == _maxSize)
                {
                    return true; // Already fixed, do nothing.
                }
                
                platformSettings.maxTextureSize = _maxSize;
                importer.SetPlatformTextureSettings(platformSettings);
                importer.SaveAndReimport();
                return true;
            }
            return false;
        }
    }
} 