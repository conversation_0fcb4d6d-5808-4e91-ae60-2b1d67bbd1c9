using UnityEditor;
using AssetPipeline.Core;

namespace AssetPipeline.Processors.Fixes
{
    public class SetMipmapEnabledFix : IAutoFix
    {
        private readonly bool _enabled;

        public SetMipmapEnabledFix(bool enabled)
        {
            _enabled = enabled;
        }

        public string GetDescription() => $"将Mipmap生成设置为 {_enabled}";

        public bool CanFix(string assetPath)
        {
            return AssetImporter.GetAtPath(assetPath) is TextureImporter;
        }

        public bool DoFix(string assetPath)
        {
            if (AssetImporter.GetAtPath(assetPath) is TextureImporter importer)
            {
                importer.mipmapEnabled = _enabled;
                importer.SaveAndReimport();
                return true;
            }
            return false;
        }
    }
} 