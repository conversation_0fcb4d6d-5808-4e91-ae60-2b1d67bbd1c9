using L10.Editor.AssetPipeline.Sqlite;

namespace AssetPipeline.Database.Models
{
    [Table("MaterialFingerprint")]
    public class MaterialFingerprint
    {
        [PrimaryKey]
        public string GUID { get; set; }
        
        public string ShaderName { get; set; }
        public string PropertyHash { get; set; }
        public string TextureHash { get; set; }
        public string FullHash { get; set; }
        public int TextureCount { get; set; }
        public int PropertyCount { get; set; }
        
        [Ignore] public string Path { get; set; }
    }
} 