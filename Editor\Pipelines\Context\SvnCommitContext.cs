using System.Collections.Generic;
using AssetPipeline.Core;
using Logger = AssetPipeline.Core.Logger;

namespace AssetPipeline.Pipelines.Context
{
    /// <summary>
    /// SVN提交检查上下文
    /// </summary>
    public class SvnCommitContext : PipelineContext
    {
        #region 提交数据
        
        public IReadOnlyList<string> CommitPaths { get; }
        
        #endregion

        #region 提交状态跟踪
        
        /// <summary>
        /// 提交前检查是否已完成
        /// </summary>
        public bool PreCommitCompleted
        {
            get => GetData<bool>("PreCommitCompleted", false);
            set => SetData("PreCommitCompleted", value);
        }
        
        /// <summary>
        /// 提交后检查是否已完成
        /// </summary>
        public bool PostCommitCompleted
        {
            get => GetData<bool>("PostCommitCompleted", false);
            set => SetData("PostCommitCompleted", value);
        }
        
        /// <summary>
        /// 获取已检查的文件数量
        /// </summary>
        public int CheckedFileCount => GetData<HashSet<string>>("CheckedFiles", new HashSet<string>()).Count;
        
        #endregion

        #region 构造函数

        public SvnCommitContext(IReadOnlyList<string> commitPaths)
        {
            CommitPaths = commitPaths ?? new List<string>();
            
            // 设置批量资产路径以支持跨资产验证
            AssetPaths = CommitPaths;
            
            // 初始化检查文件集合
            SetData("CheckedFiles", new HashSet<string>());
        }

        #endregion

        #region 检查管理
        
        /// <summary>
        /// 标记文件已检查
        /// </summary>
        public void MarkFileChecked(string filePath)
        {
            if (string.IsNullOrEmpty(filePath)) return;
            
            var checkedFiles = GetData<HashSet<string>>("CheckedFiles", new HashSet<string>());
            checkedFiles.Add(filePath);
            SetData("CheckedFiles", checkedFiles);
        }
        
        /// <summary>
        /// 检查文件是否已被检查过
        /// </summary>
        public bool IsFileChecked(string filePath)
        {
            var checkedFiles = GetData<HashSet<string>>("CheckedFiles", new HashSet<string>());
            return checkedFiles.Contains(filePath);
        }
        
        #endregion

        #region 统计信息
        
        public override string GetStatistics()
        {
            var baseStats = base.GetStatistics();
            var checkedCount = CheckedFileCount;
            var totalCount = CommitPaths.Count;
            var preCommitStatus = PreCommitCompleted ? "已完成" : "未完成";
            var postCommitStatus = PostCommitCompleted ? "已完成" : "未完成";
            
            return $"{baseStats}, 文件: {checkedCount}/{totalCount}, 提交前: {preCommitStatus}, 提交后: {postCommitStatus}";
        }
        
        #endregion

        #region 完成处理

        protected override void OnComplete()
        {
            Logger.Info(LogModule.Pipeline, $"SVN提交检查会话完成: {GetStatistics()}");
            
            // 调用基类清理
            base.OnComplete();
        }

        #endregion
    }
} 