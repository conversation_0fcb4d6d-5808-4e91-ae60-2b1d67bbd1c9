# Unity资源管理框架专业知识体系

## 1. Unity资源导入系统深度理解

### 1.1 AssetPostprocessor生命周期
```csharp
// Unity资源导入的完整生命周期
public class AssetImportLifecycle : AssetPostprocessor
{
    // 1. 预处理阶段 - 在导入前修改导入设置
    void OnPreprocessTexture() { }
    void OnPreprocessModel() { }
    void OnPreprocessAudio() { }
    
    // 2. 导入处理阶段 - 在导入过程中处理
    void OnPostprocessTexture(Texture2D texture) { }
    void OnPostprocessModel(GameObject go) { }
    void OnPostprocessAudio(AudioClip clip) { }
    
    // 3. 批量后处理阶段 - 所有资源导入完成后
    static void OnPostprocessAllAssets(
        string[] importedAssets,
        string[] deletedAssets, 
        string[] movedAssets,
        string[] movedFromAssetPaths) { }
}
```

### 1.2 CacheServer机制与导入优化
```csharp
// CacheServer缓存机制的关键理解
public class CacheServerMechanism
{
    // 缓存键值计算：资源文件 + 导入设置 + 导入器版本 + 平台
    public static string CalculateCacheKey(string assetPath)
    {
        var assetContent = File.ReadAllBytes(assetPath);
        var metaContent = File.ReadAllText(assetPath + ".meta");
        var importerVersion = GetImporterVersion(assetPath);
        var platform = EditorUserBuildSettings.activeBuildTarget;
        
        return MD5Hash(assetContent + metaContent + importerVersion + platform);
    }
    
    // 兼容旧资源的策略
    public static bool ShouldSkipCustomProcessing()
    {
        // 在metadata检查期间跳过自定义处理
        return EditorApplication.delayCall == null;
    }
}
```

### 1.3 导入死循环检测机制
```csharp
// 导入死循环检测的核心实现
public class ImportLoopDetector
{
    private static Dictionary<string, List<string>> importHistory = 
        new Dictionary<string, List<string>>();
    
    public static bool DetectLoop(string assetPath, AssetImporter importer)
    {
        var currentState = SerializeImporterState(importer);
        
        if (!importHistory.ContainsKey(assetPath))
        {
            importHistory[assetPath] = new List<string>();
        }
        
        var history = importHistory[assetPath];
        
        // 检查是否出现重复状态
        if (history.Contains(currentState))
        {
            Logger.Error($"检测到导入死循环: {assetPath}");
            return true;
        }
        
        history.Add(currentState);
        
        // 限制历史记录长度
        if (history.Count > 10)
        {
            history.RemoveAt(0);
        }
        
        return false;
    }
}
```

## 2. 资源管理架构设计模式

### 2.1 Pipeline模式的现实价值
```csharp
// Pipeline模式解决的核心问题
public abstract class AssetPipeline
{
    // 1. 统一的处理流程编排
    protected abstract void PreProcess();
    protected abstract void Process();
    protected abstract void PostProcess();
    
    // 2. 上下文状态管理
    protected PipelineContext Context { get; set; }
    
    // 3. 错误聚合和批量反馈
    protected CheckResultCollection Results { get; set; }
    
    // 4. 生命周期控制
    public void Execute()
    {
        try
        {
            PreProcess();
            Process();
            PostProcess();
        }
        catch (Exception ex)
        {
            HandleError(ex);
        }
        finally
        {
            Cleanup();
        }
    }
}
```

### 2.2 AssetTree的层次化资源管理
```csharp
// AssetTree的核心价值：层次化规则匹配
public class AssetTreeMatcher
{
    // 路径匹配的层次化处理
    public List<AssetProcessor> FindProcessors(string assetPath)
    {
        var processors = new List<AssetProcessor>();
        var currentNode = rootNode;
        var pathSegments = assetPath.Split('/');
        
        // 逐层匹配，累积处理器
        foreach (var segment in pathSegments)
        {
            currentNode = currentNode.FindChild(segment);
            if (currentNode?.Profile != null)
            {
                processors.AddRange(currentNode.Profile.GetMatchingProcessors(assetPath));
            }
        }
        
        return processors.Distinct().ToList();
    }
}
```

## 3. 配置系统设计原理

### 3.1 双配置系统的职责分离
```csharp
// AssetPipelineConfig: 团队共享的项目配置
[CreateAssetMenu(fileName = "AssetPipelineConfig", menuName = "Asset Pipeline/Project Config")]
public class AssetPipelineConfig : ScriptableObject
{
    // 保存到SVN，团队共享
    public AssetTree mainAssetTree;
    public List<AssetTypeDefinition> assetTypeDefinitions;
    
    // 支持JSON优先加载机制
    public static AssetTree MainAssetTree => 
        AssetPipelineJsonManager.TryLoadConfigs() ?? Instance?.mainAssetTree;
}

// AssetPipelineSettings: 本地编辑器运行时设置
public static class AssetPipelineSettings
{
    // 不序列化，仅存储在EditorPrefs
    public static bool IsPipelineEnabled(string pipelineName) =>
        EditorPrefs.GetBool($"AssetPipeline.{pipelineName}.Enabled", true);
        
    public static void SetPipelineEnabled(string pipelineName, bool enabled) =>
        EditorPrefs.SetBool($"AssetPipeline.{pipelineName}.Enabled", enabled);
}
```

### 3.2 JSON配置优先机制
```csharp
// JSON配置的优先加载策略
public static class AssetPipelineJsonManager
{
    public static AssetTree TryLoadConfigs()
    {
        try
        {
            var jsonPath = "Assets/Editor/AssetPipeline/Data/AssetTreeConfig.json";
            if (!File.Exists(jsonPath)) return null;
            
            var jsonContent = File.ReadAllText(jsonPath);
            var treeData = JsonUtility.FromJson<AssetTreeJsonData>(jsonContent);
            
            return RebuildAssetTree(treeData);
        }
        catch (Exception ex)
        {
            Debug.LogError($"JSON配置加载失败: {ex.Message}");
            return null;
        }
    }
}
```

## 4. 数据库设计与性能优化

### 4.1 资源信息数据库架构
```sql
-- 核心资源信息表
CREATE TABLE AssetBaseInfo (
    GUID TEXT PRIMARY KEY,
    Name TEXT NOT NULL,
    Type TEXT NOT NULL,
    Size INTEGER NOT NULL,
    Path TEXT NOT NULL,
    ModifiedTime DATETIME NOT NULL,
    MD5 TEXT NOT NULL,
    CheckResultType INTEGER NOT NULL
);

-- 依赖关系表
CREATE TABLE AssetDependency (
    ID INTEGER PRIMARY KEY AUTOINCREMENT,
    GUID TEXT NOT NULL,
    DependencyGUID TEXT NOT NULL,
    FOREIGN KEY(GUID) REFERENCES AssetBaseInfo(GUID)
);

-- GUID历史追踪表
CREATE TABLE GUIDHistory (
    ID INTEGER PRIMARY KEY AUTOINCREMENT,
    GUID TEXT NOT NULL,
    Path TEXT NOT NULL,
    Operation TEXT NOT NULL,
    Timestamp DATETIME NOT NULL
);
```

### 4.2 增量更新策略
```csharp
// 增量数据采集的实现
public class IncrementalDataCollector
{
    public void CollectAssetData(string[] modifiedAssets)
    {
        foreach (var assetPath in modifiedAssets)
        {
            var assetInfo = BuildAssetInfo(assetPath);
            
            // 检查是否需要更新
            var existingInfo = DatabaseManager.Instance.Get<AssetInfo>(assetInfo.GUID);
            if (existingInfo == null || existingInfo.MD5 != assetInfo.MD5)
            {
                DatabaseManager.Instance.AddOrUpdate(assetInfo);
                CollectDependencies(assetPath);
            }
        }
    }
}
```

## 5. 处理器接口设计与实现模式

### 5.1 统一处理器接口架构
```csharp
// 处理器接口的层次化设计
public interface IProcessor
{
    bool CanProcess(string assetPath);
    void OnAdded();
    bool OnStart();
    void OnCompleted();
}

// 特化接口继承体系
public interface IImportProcessor : IProcessor
{
    bool OnlyFirstImport { get; }
}

public interface ITextureImportProcessor : IImportProcessor
{
    void OnPreprocessTexture();
    void OnPostprocessTexture(Texture2D texture);
}

public interface IModificationProcessor : IProcessor
{
    IEnumerable<CheckResult> OnWillCreateAsset(string assetPath);
    IEnumerable<CheckResult> OnWillDeleteAsset(string assetPath);
    IEnumerable<CheckResult> OnWillMoveAsset(string from, string to);
    IEnumerable<CheckResult> OnWillSaveAssets(string[] assetPaths);
}
```

### 5.2 处理器生命周期管理
```csharp
// 处理器的标准生命周期实现
public abstract class AssetProcessor : ScriptableObject, IProcessor
{
    private ProcessorStatistics _statistics;
    private bool _isInitialized = false;

    public virtual bool OnStart()
    {
        var isValid = ValidateConfiguration();
        if (isValid)
        {
            _statistics.Reset();
            _statistics.RecordStart();
            _isInitialized = true;
        }
        return isValid;
    }

    public virtual void OnCompleted()
    {
        if (_statistics.ProcessedCount > 0)
        {
            Log(LogLevel.Debug, $"处理器完成 - {_statistics.GetSummary()}");
        }
    }

    protected abstract bool ValidateConfiguration();
}
```

### 5.3 自动修复机制设计
```csharp
// 自动修复接口的实现模式
public interface IAutoFix
{
    string GetDescription();
    bool CanFix(string assetPath);
    bool DoFix(string assetPath);
}

// 具体修复实现示例
public class TextureReadableFix : IAutoFix
{
    private readonly bool _enabled;

    public string GetDescription() => $"设置贴图Read/Write为{_enabled}";

    public bool CanFix(string assetPath) =>
        AssetImporter.GetAtPath(assetPath) is TextureImporter;

    public bool DoFix(string assetPath)
    {
        if (AssetImporter.GetAtPath(assetPath) is TextureImporter importer)
        {
            importer.isReadable = _enabled;
            importer.SaveAndReimport();
            return true;
        }
        return false;
    }
}
```

## 6. 错误处理与用户反馈系统

### 6.1 检查结果标准化
```csharp
// 统一的检查结果数据结构
public class CheckResult
{
    public CheckResultType Type { get; set; }  // Error, Warning, Info
    public string Message { get; set; }
    public string AssetPath { get; set; }
    public string ProcessorName { get; set; }
    public IAutoFix AutoFix { get; set; }
    public DateTime CheckTime { get; set; }

    // 流式API支持
    public CheckResult WithAssetPath(string path) { AssetPath = path; return this; }
    public CheckResult WithFix(IAutoFix fix) { AutoFix = fix; return this; }
}

// 检查结果集合管理
public class CheckResultCollection
{
    private List<CheckResult> _results = new List<CheckResult>();

    public void Add(CheckResult result) => _results.Add(result);
    public bool HasErrors => _results.Any(r => r.Type == CheckResultType.Error);
    public bool HasWarnings => _results.Any(r => r.Type == CheckResultType.Warning);

    public void ShowResultsWindow()
    {
        if (_results.Any())
        {
            CheckResultWindow.ShowWindow(_results);
        }
    }
}
```
