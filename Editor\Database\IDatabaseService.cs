using System;
using System.Collections.Generic;
using AssetPipeline.Database.Models;

namespace AssetPipeline.Database
{
    /// <summary>
    /// 数据库服务的核心接口。
    /// 提供对通用仓储的访问和事务管理。
    /// </summary>
    public interface IDatabaseService : IDisposable
    {
        void Initialize(string databasePath);
        void Shutdown();

        /// <summary>
        /// 获取指定数据模型的仓储实例。
        /// </summary>
        /// <typeparam name="T">数据模型的类型，必须是一个类并有无参构造函数。</typeparam>
        /// <returns>该模型的仓储接口。</returns>
        IRepository<T> GetRepository<T>() where T : class, new();

        /// <summary>
        /// 开始一个数据库事务。
        /// </summary>
        void BeginTransaction();
        
        /// <summary>
        /// 提交当前事务。
        /// </summary>
        void Commit();
        
        /// <summary>
        /// 回滚当前事务。
        /// </summary>
        void Rollback();
        
        /// <summary>
        /// 在一个事务中执行一个操作。
        /// 如果操作成功，事务将被提交；如果发生异常，事务将被回滚。
        /// </summary>
        void RunInTransaction(Action action);

        /// <summary>
        /// 执行原始SQL查询并返回结果列表。慎用。
        /// </summary>
        List<T> Query<T>(string sql, params object[] args) where T : new();

        /// <summary>
        /// 执行原始SQL命令并返回受影响的行数。慎用。
        /// </summary>
        int Execute(string sql, params object[] args);
        
        /// <summary>
        /// 为给定的模型类型创建或迁移表结构。
        /// </summary>
        void CreateOrUpdateTable<T>() where T : new();
    }
} 