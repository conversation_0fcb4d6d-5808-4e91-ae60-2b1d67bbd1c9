using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;

namespace L10.Editor.AssetPipeline
{
    public class AssetReferenceFinderWindow : EditorWindow
    {
        private HashSet<Object> assetsToFind = new HashSet<Object>();
        private Dictionary<Object, List<string>> foundReferences = new Dictionary<Object, List<string>>();
        private Vector2 scrollPosition;
        private Vector2 assetListScrollPosition;
        private Dictionary<Object, int> currentPages = new Dictionary<Object, int>();
        private bool showAssetsToFind = true;
        private Dictionary<Object, bool> showResultsFoldout = new Dictionary<Object, bool>();
        private const float ObjectFieldWidth = 200f; // 固定宽度
        private const float ObjectFieldSpacing = 5f; // 间距
        private string searchString = "";
        private const int ItemsPerPage = 100; // 每页显示的项目数
        private int currentResultPage = 0;
        private const int ResultsPerPage = 5; // 每页显示的结果数量
        private Dictionary<string, Object> assetCache = new Dictionary<string, Object>();

        [MenuItem("Window/资源引用查找器")]
        public static void ShowWindow()
        {
            GetWindow<AssetReferenceFinderWindow>("资源引用查找器");
        }

        [MenuItem("Assets/查找资源引用", false, 25)]
        private static void FindReferences_ContextMenu()
        {
            AssetReferenceFinderWindow window = GetWindow<AssetReferenceFinderWindow>("资源引用查找器");
            window.assetsToFind.Clear();
            foreach (var obj in Selection.GetFiltered(typeof(Object), SelectionMode.DeepAssets))
            {
                window.assetsToFind.Add(obj); 
            }
            window.FindReferences();
        }
        
        [MenuItem("Assets/查找资源引用场景", false, 26)]
        private static void FindReferences_Scene_ContextMenu()
        {
            AssetReferenceFinderWindow window = GetWindow<AssetReferenceFinderWindow>("资源引用查找器");
            window.assetsToFind.Clear();
            foreach (var obj in Selection.GetFiltered(typeof(Object), SelectionMode.DeepAssets))
            {
                window.assetsToFind.Add(obj); 
            }
            window.FindReferences(".unity");
        }

        private void OnGUI()
        {   
            DisplayAssetList();

            if (GUILayout.Button("查找引用"))
            {
                FindReferences();
            }

            EditorGUILayout.BeginHorizontal();
            GUILayout.Label("在结果中搜索:", GUILayout.Width(100));
            searchString = EditorGUILayout.TextField(searchString, GUILayout.ExpandWidth(true));
            if (GUILayout.Button("清除", GUILayout.Width(50)))
            {
                searchString = "";
                GUI.FocusControl(null);
            }
            EditorGUILayout.EndHorizontal();

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            DisplayResults();
            EditorGUILayout.EndScrollView();

            EditorGUILayout.Space();
            DisplayScanningControls();
        }

        private void DisplayAssetList()
        {
            HandleDragAndDrop();
            
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            
            GUIStyle foldoutStyle = new GUIStyle(EditorStyles.foldout);
            foldoutStyle.fontStyle = FontStyle.Bold;
            
            showAssetsToFind = EditorGUILayout.Foldout(showAssetsToFind, $"要查找引用的资源: ({assetsToFind.Count})", true, foldoutStyle);

            if (showAssetsToFind)
            {
                float windowWidth = position.width;
                int itemsPerRow = Mathf.FloorToInt((windowWidth - 20) / (ObjectFieldWidth + ObjectFieldSpacing)); // 20 是左右边距
                itemsPerRow = Mathf.Max(1, itemsPerRow); // 确保至少显示一个

                var assetsToRemove = new List<Object>();
                
                // 计算需要的行数
                int rowCount = Mathf.CeilToInt((float)assetsToFind.Count / itemsPerRow);
                
                // 计算内容高度（每行高度为 EditorGUIUtility.singleLineHeight + 间距）
                float contentHeight = rowCount * (EditorGUIUtility.singleLineHeight + ObjectFieldSpacing);
                
                // 设置最大高度
                float maxHeight = position.height / 4;
                
                // 决定是否需要滚动视图
                bool needScrollView = contentHeight > maxHeight;
                
                // 如果需要滚动视图，使用 BeginScrollView，否则使用 BeginVertical
                if (needScrollView)
                {
                    assetListScrollPosition = EditorGUILayout.BeginScrollView(assetListScrollPosition, GUILayout.Height(maxHeight));
                }
                else
                {
                    EditorGUILayout.BeginVertical();
                }

                int i = 0;
                foreach (var asset in assetsToFind)
                {
                    if (i % itemsPerRow == 0)
                    {
                        EditorGUILayout.BeginHorizontal();
                    }

                    EditorGUILayout.BeginHorizontal(GUILayout.Width(ObjectFieldWidth));
                    EditorGUILayout.ObjectField(asset, typeof(Object), false, GUILayout.Width(ObjectFieldWidth - 25));
                    if (GUILayout.Button("X", GUILayout.Width(20)))
                    {
                        assetsToRemove.Add(asset);
                    }
                    EditorGUILayout.EndHorizontal();

                    GUILayout.Space(ObjectFieldSpacing);

                    if ((i + 1) % itemsPerRow == 0 || i == assetsToFind.Count - 1)
                    {
                        EditorGUILayout.EndHorizontal();
                        EditorGUILayout.Space(); // 行间距
                    }
                    i++;
                }

                // 根据是否使用滚动视图来结束相应的布局
                if (needScrollView)
                {
                    EditorGUILayout.EndScrollView();
                }
                else
                {
                    EditorGUILayout.EndVertical();
                }

                foreach (var asset in assetsToRemove)
                {
                    assetsToFind.Remove(asset);
                }

                if (GUILayout.Button("清除全部"))
                {
                    assetsToFind.Clear();
                    foundReferences.Clear();
                    currentPages.Clear();
                    showResultsFoldout.Clear();
                    searchString = "";
                }
            }

            EditorGUILayout.EndVertical();
        }

        private void HandleDragAndDrop()
        {
            Event evt = Event.current;
            Rect dropArea = GUILayoutUtility.GetRect(0.0f, 50.0f, GUILayout.ExpandWidth(true));

            // 创建自定义样式
            GUIStyle customBoxStyle = new GUIStyle(EditorStyles.helpBox);
            customBoxStyle.normal.background = MakeTex(2, 2, new Color(0.8f, 0.8f, 1f, 0.5f));
            customBoxStyle.normal.textColor = Color.black;
            customBoxStyle.fontStyle = FontStyle.Bold;
            customBoxStyle.fontSize = 14;
            customBoxStyle.alignment = TextAnchor.MiddleCenter;
            customBoxStyle.border = new RectOffset(2, 2, 2, 2);

            bool isHovering = dropArea.Contains(evt.mousePosition) && DragAndDrop.visualMode != DragAndDropVisualMode.None;

            // 如果正在悬停，改变背景颜色和文本
            if (isHovering)
            {
                customBoxStyle.normal.background = MakeTex(2, 2, new Color(0.6f, 0.6f, 1f, 0.7f));
                GUI.Box(dropArea, "释放以添加资源", customBoxStyle);
            }
            else
            {
                GUI.Box(dropArea, "将资源或文件夹拖放到此处以添加到搜索列表", customBoxStyle);
            }

            switch (evt.type)
            {
                case EventType.DragUpdated:
                case EventType.DragPerform:
                    if (!dropArea.Contains(evt.mousePosition))
                        return;

                    DragAndDrop.visualMode = DragAndDropVisualMode.Copy;

                    if (evt.type == EventType.DragPerform)
                    {
                        DragAndDrop.AcceptDrag();

                        foreach (Object draggedObject in DragAndDrop.objectReferences)
                        {
                            AddAssetRecursively(draggedObject);
                        }
                    }
                    Event.current.Use();
                    break;
            }
        }

        private void AddAssetRecursively(Object asset)
        {
            string path = AssetDatabase.GetAssetPath(asset);
            if (string.IsNullOrEmpty(path))
                return;

            if (System.IO.Directory.Exists(path))
            {
                // 如果是文件夹，递归添加所有子资源
                string[] subAssets = System.IO.Directory.GetFiles(path, "*", System.IO.SearchOption.AllDirectories).ToList()
                    .Where(file => !file.EndsWith(".meta"))
                    .ToArray();

                foreach (string subAssetPath in subAssets)
                {
                    Object subAsset = AssetDatabase.LoadAssetAtPath<Object>(subAssetPath);
                    if (subAsset != null)
                    {
                        assetsToFind.Add(subAsset);
                    }
                }
            }
            else
            {
                // 如果是单个资源，直接添加
                assetsToFind.Add(asset);
            }
        }

        private Texture2D MakeTex(int width, int height, Color col)
        {
            Color[] pix = new Color[width * height];
            for (int i = 0; i < pix.Length; i++)
            {
                pix[i] = col;
            }
            Texture2D result = new Texture2D(width, height);
            result.SetPixels(pix);
            result.Apply();
            return result;
        }

        private void FindReferences(string filteredExt = null)
        {
            foundReferences.Clear();
            currentPages.Clear();

            foreach (Object asset in assetsToFind)
            {
                string assetPath = AssetDatabase.GetAssetPath(asset);
                string assetGuid = AssetDatabase.AssetPathToGUID(assetPath);
                HashSet<string> references = AssetReferenceScanner.FindReverseReferences(assetGuid);
                if (filteredExt != null)
                    references = new HashSet<string>(references.Where(x => AssetDatabase.GUIDToAssetPath(x).EndsWith(filteredExt)));
                foundReferences[asset] = new List<string>(references);
                currentPages[asset] = 0;
            }
        }

        private void DisplayResults()
        {
            List<KeyValuePair<Object, List<string>>> filteredResults = foundReferences
                .Select(kvp => new KeyValuePair<Object, List<string>>(kvp.Key, FilterReferences(kvp.Key, kvp.Value)))
                .Where(kvp => kvp.Value.Count > 0)
                .ToList();

            int totalPages = Mathf.CeilToInt((float)filteredResults.Count / ResultsPerPage);
            currentResultPage = Mathf.Clamp(currentResultPage, 0, totalPages - 1);
            
            if (filteredResults.Count == 0)
            {
                EditorGUILayout.HelpBox("未找到所选资源的引用。", MessageType.Info);
                if (AssetReferenceScanner.GetScanProgress() < 1f)
                {
                    EditorGUILayout.HelpBox("资源扫描尚未完成，当前结果可能不完整。", MessageType.Warning);
                }
                return;
            }

            int startIndex = currentResultPage * ResultsPerPage;
            int endIndex = Mathf.Min(startIndex + ResultsPerPage, filteredResults.Count);

            for (int i = startIndex; i < endIndex; i++)
            {
                var kvp = filteredResults[i];
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("引用对象:", GUILayout.Width(100));
                EditorGUI.BeginDisabledGroup(true);
                EditorGUILayout.ObjectField(kvp.Key, typeof(Object), false);
                EditorGUI.EndDisabledGroup();
                EditorGUILayout.EndHorizontal();

                if (!showResultsFoldout.ContainsKey(kvp.Key))
                {
                    showResultsFoldout[kvp.Key] = true;
                }

                GUIStyle foldoutStyle = new GUIStyle(EditorStyles.foldout);
                foldoutStyle.fontStyle = FontStyle.Bold;

                showResultsFoldout[kvp.Key] = EditorGUILayout.Foldout(showResultsFoldout[kvp.Key], $"显示 {kvp.Value.Count} 个引用", true, foldoutStyle);

                if (showResultsFoldout[kvp.Key])
                {
                    if (!currentPages.ContainsKey(kvp.Key))
                    {
                        currentPages[kvp.Key] = 0;
                    }

                    int itemTotalPages = Mathf.CeilToInt((float)kvp.Value.Count / ItemsPerPage);
                    
                    if (itemTotalPages > 1)
                    {
                        EditorGUILayout.BeginHorizontal();
                        if (GUILayout.Button("上一页", GUILayout.Width(100)))
                        {
                            currentPages[kvp.Key] = Mathf.Max(0, currentPages[kvp.Key] - 1);
                        }
                        EditorGUILayout.LabelField($"第 {currentPages[kvp.Key] + 1} 页，共 {itemTotalPages} 页", GUILayout.Width(150));
                        if (GUILayout.Button("下一页", GUILayout.Width(100)))
                        {
                            currentPages[kvp.Key] = Mathf.Min(itemTotalPages - 1, currentPages[kvp.Key] + 1);
                        }
                        EditorGUILayout.EndHorizontal();
                    }

                    DisplayReferenceItems(kvp.Value, currentPages[kvp.Key]);
                }
                if (GUILayout.Button("将prefab导入到当前场景"))
                {
                    var guidsList = kvp.Value;
                    foreach (var guid in guidsList)
                    {
                        string path = AssetDatabase.GUIDToAssetPath(guid);
                        var obj = AssetDatabase.LoadAssetAtPath<Object>(path);
                        if (obj is GameObject)
                        {
                            var go = PrefabUtility.InstantiatePrefab(obj) as GameObject;
                        }
                    }
                }
                if (kvp.Key is Texture2D)
                {
                    if (GUILayout.Button("将材质球中引用贴图批量替换为新贴图"))
                    {
                        var materialListStr = "";
                        var originTexture = kvp.Key as Texture2D;
                        var oldguid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(originTexture));

                        var newtexturePath = EditorUtility.OpenFilePanel("选择新贴图", Application.dataPath, "png,jpg,jpeg,tga,tif,bmp,psd,exr");
                        newtexturePath = newtexturePath.Replace(Application.dataPath, "Assets");
                        var newTexture = AssetDatabase.LoadAssetAtPath<Object>(newtexturePath);
                        if (!string.IsNullOrEmpty(newtexturePath) && System.IO.File.Exists(newtexturePath) && newTexture is Texture2D)
                        {
                            var newguid = AssetDatabase.AssetPathToGUID(newtexturePath);
                            var guidsList = kvp.Value;
                            foreach (var guid in guidsList)
                            {
                                string path = AssetDatabase.GUIDToAssetPath(guid);
                                var material = AssetDatabase.LoadAssetAtPath<Material>(path);
                                if (material != null)
                                {
                                    materialListStr += material.name + "\n";
                                    var file = new System.IO.FileInfo(path);
                                    var content = System.IO.File.ReadAllText(path);
                                    content = content.Replace(oldguid, newguid);
                                    System.IO.File.WriteAllText(path, content);
                                }
                            }
                        }
                        if (materialListStr != "")
                        {
                            AssetDatabase.Refresh();
                            EditorUtility.DisplayDialog("替换成功", string.Format("已替换以下材质球中的贴图({0})为新贴图({1})：\n{2}\n",kvp.Key.name,newTexture.name,materialListStr), "确定");
                        }
                    }                
                }
                EditorGUILayout.EndVertical();
                EditorGUILayout.Space();
            }

            // 显示整体结果的分页控件
            if (totalPages > 1)
            {
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("上一页", GUILayout.Width(100)))
                {
                    currentResultPage = Mathf.Max(0, currentResultPage - 1);
                }
                EditorGUILayout.LabelField($"第 {currentResultPage + 1} 页，共 {totalPages} 页", GUILayout.Width(150));
                if (GUILayout.Button("下一页", GUILayout.Width(100)))
                {
                    currentResultPage = Mathf.Min(totalPages - 1, currentResultPage + 1);
                }
                EditorGUILayout.EndHorizontal();
            }

            if (AssetReferenceScanner.GetScanProgress() < 1f)
            {
                EditorGUILayout.HelpBox("资源扫描尚未完成，当前结果可能不完整。", MessageType.Warning);
            }
        }

        private void DisplayReferenceItems(List<string> guids, int currentPage)
        {
            float windowWidth = position.width;
            int itemsPerRow = Mathf.FloorToInt((windowWidth - 20) / (ObjectFieldWidth + ObjectFieldSpacing)); // 20 是左右边距
            itemsPerRow = Mathf.Max(1, itemsPerRow); // 确保至少显示一个

            int startIndex = currentPage * ItemsPerPage;
            int endIndex = Mathf.Min(startIndex + ItemsPerPage, guids.Count);

            for (int i = startIndex; i < endIndex; i += itemsPerRow)
            {
                EditorGUILayout.BeginHorizontal();
                for (int j = 0; j < itemsPerRow && i + j < endIndex; j++)
                {
                    DisplayReferenceItem(guids[i + j]);
                    GUILayout.Space(ObjectFieldSpacing);
                }
                EditorGUILayout.EndHorizontal();
                EditorGUILayout.Space(); // 行间距
            }
        }

        private void DisplayReferenceItem(string guid)
        {
            if (!assetCache.TryGetValue(guid, out Object referencingAsset))
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                referencingAsset = AssetDatabase.LoadAssetAtPath<Object>(path);
                assetCache[guid] = referencingAsset;
            }

            EditorGUILayout.ObjectField(referencingAsset, typeof(Object), false, GUILayout.Width(ObjectFieldWidth));
        }

        private void DisplayScanningControls()
        {
            EditorGUILayout.BeginHorizontal();
            
            float progress = AssetReferenceScanner.GetScanProgress();
            if (progress < 1f)
            {
                if (GUILayout.Button("开始扫描 (当前进度: " + (progress * 100).ToString("F2") + "%)", GUILayout.Height(EditorGUIUtility.singleLineHeight * 2.2f)))
                {
                    AssetReferenceScanner.ToggleScanning(true);
                }
            }
            else
            { 
                GUIStyle largeHelpBoxStyle = new GUIStyle(EditorStyles.helpBox);
                largeHelpBoxStyle.fontSize = 18; // 增大字体大小
                largeHelpBoxStyle.fontStyle = FontStyle.Bold; // 设置为粗体
                EditorGUILayout.HelpBox("", MessageType.Info, true);
                Rect lastRect = GUILayoutUtility.GetLastRect();
                lastRect.y += 5;
                GUI.Label(lastRect, "      资源扫描已完成！请开始使用", largeHelpBoxStyle);
            }
            if (GUILayout.Button("删除缓存", GUILayout.Height(EditorGUIUtility.singleLineHeight * 2.2f)))
            {
                if (EditorUtility.DisplayDialog("删除缓存", "确定要清空所有已扫描的数据吗？", "确定", "取消"))
                {
                    AssetReferenceScanner.ClearCache();
                    EditorUtility.DisplayDialog("删除缓存", "缓存已成功删除。", "确定");
                }
            }
            
            EditorGUILayout.EndHorizontal();
        }

        private List<string> FilterReferences(Object asset, List<string> references)
        {
            if (string.IsNullOrEmpty(searchString))
            {
                return references;
            }

            string lowerSearchString = searchString.ToLower();
            return references.Where(guid => 
            {
                if (!assetCache.TryGetValue(guid, out Object referencingAsset))
                {
                    string path = AssetDatabase.GUIDToAssetPath(guid);
                    referencingAsset = AssetDatabase.LoadAssetAtPath<Object>(path);
                    assetCache[guid] = referencingAsset;
                }
                return referencingAsset.name.ToLower().Contains(lowerSearchString);
            }).ToList();
        }
    }

}