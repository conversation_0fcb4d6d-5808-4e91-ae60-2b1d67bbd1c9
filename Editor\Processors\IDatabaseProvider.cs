using AssetPipeline.Database;
using System.Collections.Generic;

namespace AssetPipeline.Processors
{
    /// <summary>
    /// 数据库提供器接口。
    /// 实现此接口的处理器能够向资产数据库提供自定义数据。
    /// </summary>
    public interface IDatabaseProvider
    {
        /// <summary>
        /// 获取此提供器要写入的数据表的结构。
        /// </summary>
        TableSchema GetTableSchema();

        /// <summary>
        /// 针对给定的资产路径，采集并返回一行数据。
        /// 返回的字典的键应与TableSchema中定义的列名匹配。
        /// </summary>
        /// <param name="assetPath">资产的路径</param>
        /// <returns>包含单行数据的字典，键为列名，值为数据。</returns>
        Dictionary<string, object> CollectData(string assetPath);
    }
} 