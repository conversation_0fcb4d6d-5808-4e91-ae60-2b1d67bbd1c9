using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using AssetPipeline.Core;
using Logger = AssetPipeline.Core.Logger;

namespace AssetPipeline.Pipelines
{
    /// <summary>
    /// 管线处理上下文基类 - 统一的上下文管理架构
    /// </summary>
    public abstract class PipelineContext
    {
        #region 生命周期管理
        
        public DateTime StartTime { get; }
        public DateTime EndTime { get; private set; }
        public bool IsCompleted { get; private set; }
        public TimeSpan Duration => IsCompleted ? EndTime - StartTime : DateTime.Now - StartTime;

        #endregion

        #region 通用数据管理
        
        protected readonly CheckResultCollection results = new CheckResultCollection();
        public CheckResultCollection Results => results;
        
        /// <summary>
        /// 当前批次操作涉及的所有资产路径
        /// 用于处理器进行跨资产验证
        /// </summary>
        public IReadOnlyList<string> AssetPaths { get; protected set; } = new List<string>();
        
        private readonly Dictionary<string, object> contextData = new Dictionary<string, object>();
        
        /// <summary>
        /// 设置上下文数据
        /// </summary>
        public void SetData<T>(string key, T value)
        {
            contextData[key] = value;
        }
        
        /// <summary>
        /// 获取上下文数据
        /// </summary>
        public T GetData<T>(string key, T defaultValue = default(T))
        {
            if (contextData.TryGetValue(key, out var value) && value is T)
            {
                return (T)value;
            }
            return defaultValue;
        }
        
        /// <summary>
        /// 检查是否包含指定数据
        /// </summary>
        public bool HasData(string key)
        {
            return contextData.ContainsKey(key);
        }

        #endregion

        #region 构造和完成

        protected PipelineContext()
        {
            StartTime = DateTime.Now;
        }

        public virtual void Complete()
        {
            if (IsCompleted) return;
            EndTime = DateTime.Now;
            IsCompleted = true;
            OnComplete();
        }

        protected virtual void OnComplete()
        {
            // 清理通用数据
            contextData.Clear();
            
            // 子类可以重写此方法进行自定义清理
        }

        #endregion

        #region 统计信息

        /// <summary>
        /// 获取基础统计信息
        /// </summary>
        public virtual string GetStatistics()
        {
            var stats = Results.GetStatistics();
            return $"耗时: {Duration.TotalMilliseconds:F0}ms, {stats}";
        }

        #endregion
    }
} 