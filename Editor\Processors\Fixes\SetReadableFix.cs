using UnityEditor;
using AssetPipeline.Core;

namespace AssetPipeline.Processors.Fixes
{
    public class SetReadableFix : IAutoFix
    {
        private readonly bool _enabled;

        public SetReadableFix(bool enabled)
        {
            _enabled = enabled;
        }

        public string GetDescription() => $"将Read/Write属性设置为 {_enabled}";

        public bool CanFix(string assetPath)
        {
            return AssetImporter.GetAtPath(assetPath) is TextureImporter;
        }

        public bool DoFix(string assetPath)
        {
            if (AssetImporter.GetAtPath(assetPath) is TextureImporter importer)
            {
                importer.isReadable = _enabled;
                importer.SaveAndReimport();
                return true;
            }
            return false;
        }
    }
} 