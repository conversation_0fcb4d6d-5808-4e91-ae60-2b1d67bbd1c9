using System;
using System.Collections.Generic;
using System.Linq.Expressions;

namespace AssetPipeline.Database
{
    /// <summary>
    /// 通用仓储接口 - 定义了对任何数据模型的标准数据访问操作。
    /// </summary>
    /// <typeparam name="T">数据模型的类型</typeparam>
    public interface IRepository<T> where T : class, new()
    {
        void Add(T entity);
        void Add(IEnumerable<T> entities);
        
        void Update(T entity);
        void Update(IEnumerable<T> entities);

        void Delete(T entity);
        void Delete(IEnumerable<T> entities);
        void Delete(Expression<Func<T, bool>> predicate);

        T Get(object primaryKey);
        IEnumerable<T> GetAll();
        IEnumerable<T> Find(Expression<Func<T, bool>> predicate);
        
        T FirstOrDefault(Expression<Func<T, bool>> predicate);

        int Count();
        int Count(Expression<Func<T, bool>> predicate);
        
        bool Exists(Expression<Func<T, bool>> predicate);
    }
} 