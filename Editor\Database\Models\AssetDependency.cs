using L10.Editor.AssetPipeline.Sqlite;

namespace AssetPipeline.Database.Models
{
    [Table("AssetDependency")]
    public class AssetDependency
    {
        [PrimaryKey, AutoIncrement]
        public int Id { get; set; }
        
        public string AssetGUID { get; set; }
        public string DependencyGUID { get; set; }
        public string DependencyType { get; set; }
        
        [Ignore] public string AssetPath { get; set; }
        [Ignore] public string DependencyPath { get; set; }
    }
} 