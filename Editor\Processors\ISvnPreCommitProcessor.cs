using System.Collections.Generic;
using AssetPipeline.Core;
using AssetPipeline.Pipelines.Context;

namespace AssetPipeline.Processors
{
    /// <summary>
    /// SVN提交前检查处理接口
    /// 实现此接口的处理器可以在文件提交到版本控制系统之前执行验证。
    /// </summary>
    public interface ISvnPreCommitProcessor : IProcessor
    {
        /// <summary>
        /// 对单个即将提交的资产执行检查。
        /// </summary>
        /// <param name="assetPath">正在检查的资产路径。</param>
        /// <param name="context">本次提交操作的上下文，包含所有待提交文件等信息。</param>
        /// <returns>一个包含检查结果的集合。如果任何结果是Error类型，可能会阻断提交。</returns>
        IEnumerable<CheckResult> OnPreCommit(string assetPath, SvnCommitContext context);
    }
} 