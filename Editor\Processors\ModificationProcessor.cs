using System.Collections.Generic;
using AssetPipeline.Core;
using AssetPipeline.Pipelines;
using UnityEditor;

namespace AssetPipeline.Processors
{
    /// <summary>
    /// 修改事件处理的便利基类
    /// 提供了IModificationProcessor接口所有方法的默认空实现
    /// 继承此类，并只重写关心的资产修改事件
    /// </summary>
    public abstract class ModificationProcessor : AssetProcessor, IModificationProcessor
    {
        public virtual IEnumerable<CheckResult> OnWillSaveAsset(string path, ModificationContext context) => null;

        public virtual IEnumerable<CheckResult> OnWillCreateAsset(string path, ModificationContext context) => null;

        public virtual IEnumerable<CheckResult> OnWillDeleteAsset(string path, RemoveAssetOptions options, ModificationContext context) => null;

        public virtual IEnumerable<CheckResult> OnWillMoveAsset(string sourcePath, string destinationPath, ModificationContext context) => null;

        public virtual IEnumerable<CheckResult> IsOpenForEdit(string assetPath, ModificationContext context) => null;
    }
} 