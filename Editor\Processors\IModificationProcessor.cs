using System.Collections.Generic;
using AssetPipeline.Core;
using AssetPipeline.Pipelines;
using UnityEditor;

namespace AssetPipeline.Processors
{
    /// <summary>
    /// 资源修改管线处理接口
    /// </summary>
    public interface IModificationProcessor : IProcessor
    {
        /// <summary>
        /// 在Unity即将保存一个资源时调用。
        /// </summary>
        /// <param name="path">即将被保存的资源路径。</param>
        /// <param name="context">本次修改操作的上下文。</param>
        /// <returns>对该资源的检查结果列表。</returns>
        IEnumerable<CheckResult> OnWillSaveAsset(string path, ModificationContext context);
        
        /// <summary>
        /// 在通过编辑器UI创建一个新资源时调用。
        /// </summary>
        /// <param name="path">即将被创建的资源路径。</param>
        /// <param name="context">本次修改操作的上下文。</param>
        /// <returns>对该资源的检查结果列表。</returns>
        IEnumerable<CheckResult> OnWillCreateAsset(string path, ModificationContext context);
        
        /// <summary>
        /// 在一个资源即将被删除时调用。
        /// </summary>
        /// <param name="path">即将被删除的资源路径。</param>
        /// <param name="options">删除选项。</param>
        /// <param name="context">本次修改操作的上下文。</param>
        /// <returns>对该资源的检查结果列表。</returns>
        IEnumerable<CheckResult> OnWillDeleteAsset(string path, RemoveAssetOptions options, ModificationContext context);
        
        /// <summary>
        /// 在一个资源即将被移动时调用。
        /// </summary>
        /// <param name="sourcePath">原始路径。</param>
        /// <param name="destinationPath">目标路径。</param>
        /// <param name="context">本次修改操作的上下文。</param>
        /// <returns>对该资源的检查结果列表。</returns>
        IEnumerable<CheckResult> OnWillMoveAsset(string sourcePath, string destinationPath, ModificationContext context);
        
        /// <summary>
        /// 检查一个资源是否处于可编辑状态。
        /// </summary>
        /// <param name="assetPath">要检查的资源路径。</param>
        /// <param name="context">本次修改操作的上下文。</param>
        /// <returns>对该资源的检查结果列表。如果不可编辑，应返回一个Error级别的结果。</returns>
        IEnumerable<CheckResult> IsOpenForEdit(string assetPath, ModificationContext context);
    }
} 