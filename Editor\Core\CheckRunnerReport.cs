using System;
using System.Collections.Generic;

namespace AssetPipeline.Core
{
    /// <summary>
    /// 检查统计信息
    /// </summary>
    [Serializable]
    public class CheckStatistics
    {
        public string CheckType;
        public int TotalAssets;
        public int CheckedAssets;
        public int FailedAssets;
        public int ErrorCount;
        public int WarningCount;
        public int InfoCount;
        public int AutoFixableCount;
        public TimeSpan Duration;
        public float Progress;
        public int AutoFixAttempts;
        public int AutoFixSuccesses;

        public string GetSummary()
        {
            return $"{CheckType}: {CheckedAssets}/{TotalAssets}, 错误{ErrorCount}, 警告{WarningCount}, {Duration.TotalSeconds:F1}s";
        }
    }

    /// <summary>
    /// 检查性能统计
    /// </summary>
    [Serializable]
    public class CheckPerformanceStatistics
    {
        public TimeSpan TotalDuration;
        public Dictionary<string, TimeSpan> ProcessorPerformance;
        public Dictionary<string, int> ProcessorCallCounts;
        public List<string> SlowProcessors;
        public TimeSpan AverageTimePerAsset;

        public string GetPerformanceSummary()
        {
            var slowCount = SlowProcessors?.Count ?? 0;
            return $"总耗时: {TotalDuration.TotalSeconds:F2}s, 平均每文件: {AverageTimePerAsset.TotalMilliseconds:F1}ms, 慢速处理器: {slowCount}个";
        }
    }
} 